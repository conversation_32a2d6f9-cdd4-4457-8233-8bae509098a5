# file: infra_database/uow/session_proxy.py (新文件)

from functools import partial

from sqlalchemy.orm import Session

from infra_database.uow.unit_of_work_storage import get_uow_storage


class SessionProxy:
    """
    一个代理对象，它将所有对 Session 的操作委托给当前 UoW 上下文中的活动 Session。
    这使得 Repository 可以作为单例存在，因为它注入的不是一个固定的 Session，
    而是一个总能找到当前正确 Session 的代理。
    它通过返回偏函数来延迟对活动会话的查找，直到方法被实际调用为止，
    从而解决了 pytest 在测试收集阶段的内省问题。
    """

    def __getattribute__(self, name: str):
        # 允许访问自身的魔术方法和内部方法，避免无限递归
        if name.startswith("_") or name in ("__deepcopy__", "__setstate__"):
            return object.__getattribute__(self, name)

        # 对于其他所有属性访问，返回一个偏函数。
        # 这个偏函数在被实际调用时，才会执行 _proxy_method。
        return partial(self._proxy_method, name)

    def _proxy_method(self, name: str, *args, **kwargs):
        """在方法被实际调用时，获取活动会话并委托调用。"""
        storage = get_uow_storage()
        if not storage.current_container or not storage.current_container.session:
            raise RuntimeError(
                f"尝试在活动的 UnitOfWork 上下文之外调用数据库会话方法: '{name}'。"
                "请确保您的数据库操作在 'with uow:' 块内部执行。"
            )

        active_session: Session = storage.current_container.session

        # 获取真实 session 上的方法或属性
        attr = getattr(active_session, name)

        # 如果是可调用方法（如 query, add），则使用提供的参数调用它。
        # 否则（如访问 info 字典），直接返回属性值。
        if callable(attr):
            return attr(*args, **kwargs)
        return attr


# 我们需要一个 SessionProxy 的实例。
# 因为 SessionProxy 本身是无状态的，所以它可以是一个全局单例。
current_session_proxy = SessionProxy()
