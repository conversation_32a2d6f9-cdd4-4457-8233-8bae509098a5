import contextvars
import threading
from typing import Any, Generic, List, Optional, TypeVar, cast

# 暂时用不到，先注释
# from sqlalchemy.orm import Session
# from sqlalchemy.ext.asyncio import AsyncSession
# from sqlalchemy.exc import InvalidRequestError

# 定义一个类型变量，用于泛型化会话对象，使其可以是同步或异步的Session
SessionType = TypeVar('SessionType', bound=Any)  # 使用 Any 兼容 Sync/AsyncSession


class UnitOfWorkContainer(Generic[SessionType]):
    """
    一个轻量级容器，用于存储与当前工作单元层级关联的
    SQLAlchemy 会话 (Session) 和事务 (Transaction) 对象。
    """

    def __init__(self, session: SessionType, transaction: Any):
        self.session: SessionType = session
        self.transaction: Any = transaction

    def __repr__(self) -> str:
        """提供容器的字符串表示，方便调试。"""
        session_type_name = type(self.session).__name__
        transaction_type_name = type(self.transaction).__name__ if self.transaction else "None"
        return f"<UOWContainer(session_type={session_type_name}, transaction_type={transaction_type_name}, session_id={id(self.session)})>"


class ContextualStorage(Generic[SessionType]):
    """
    上下文存储管理器。

    它通过检查当前执行环境（同步/异步），
    透明地使用 `threading.local`（同步）或 `contextvars`（异步）
    来存储和管理工作单元堆栈。这确保了在不同并发模型下的
    会话和事务的正确隔离。
    """

    # 用于同步上下文的栈 (每个线程独立)
    _sync_stack: threading.local = threading.local()

    # 用于异步上下文的栈 (每个协程任务独立)
    # 使用 contextvars.ContextVar 来确保在异步任务切换时的上下文隔离
    _async_stack_var: contextvars.ContextVar[List[UnitOfWorkContainer[SessionType]]]

    def __init__(self):
        # 针对 ContextVar 的单例化处理，确保同一个名称的 ContextVar 只被创建一次
        try:
            self._async_stack_var = contextvars.ContextVar('uow_storage_stack', default=[])
        except ValueError:
            # 如果 ContextVar 已经存在，则直接获取
            self._async_stack_var = contextvars.ContextVar('uow_storage_stack')

    @property
    def uow_stack(self) -> List[UnitOfWorkContainer[SessionType]]:
        """
        获取当前执行上下文（同步或异步）中的工作单元堆栈。
        """
        try:
            # 尝试获取当前事件循环，来判断是否在异步上下文中
            import asyncio

            _ = asyncio.get_running_loop()
            # 如果有运行中的事件循环，则认为是异步上下文
            return self._async_stack_var.get()
        except RuntimeError:
            # 没有运行中的事件循环，则认为是同步上下文
            if not hasattr(self._sync_stack, 'stack'):
                self._sync_stack.stack = []
            return cast(List[UnitOfWorkContainer[SessionType]], self._sync_stack.stack)

    def push(self, container: UnitOfWorkContainer[SessionType]) -> None:
        """将工作单元容器推入当前上下文的堆栈顶部。"""
        self.uow_stack.append(container)

    def pop(self) -> Optional[UnitOfWorkContainer[SessionType]]:
        """从当前上下文的堆栈中弹出工作单元容器。"""
        if self.uow_stack:
            return self.uow_stack.pop()
        return None

    def clear(self) -> None:
        """清空当前上下文的堆栈（通常在最外层工作单元结束后调用）。"""
        self.uow_stack.clear()

    @property
    def current_container(self) -> Optional[UnitOfWorkContainer[SessionType]]:
        """返回当前堆栈顶部的容器（代表最内层的工作单元）。"""
        if self.uow_stack:
            return self.uow_stack[-1]
        return None


# 为了方便使用，提供一个全局的ContextualStorage实例获取函数
_global_uow_storage: Optional[ContextualStorage] = None


def get_uow_storage() -> ContextualStorage[Any]:
    """
    获取全局的 ContextualStorage 实例。
    推荐在应用启动时初始化一次或通过依赖注入管理。
    """
    global _global_uow_storage
    if _global_uow_storage is None:
        _global_uow_storage = ContextualStorage()
    return _global_uow_storage
