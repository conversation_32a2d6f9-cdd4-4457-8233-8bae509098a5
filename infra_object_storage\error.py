"""
对象存储异常
"""

from typing import Optional


class ObjectNotPublicReadError(Exception):
    """
    对象非公开读取异常
    """

    def __init__(self, object_name: Optional[str] = None):
        if object_name:
            message = f"{object_name} is not public read"
        else:
            message = "object is not public read"
        super().__init__(message)


class ObjectStorageError(Exception):
    """
    对象存储异常
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "object storage meet error"
        super().__init__(message)


class BucketNotExistedError(ObjectStorageError):
    """
    篮子不存在
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "bucket not exists"
        super().__init__(message)


class ObjectStorageHelperNotFoundError(ObjectStorageError):
    """
    未找到有效的对象存储帮助类
    """

    def __init__(
        self, message: Optional[str] = None, object_storage_type: Optional[str] = None
    ):
        if not message and not object_storage_type:
            message = "can not found valid object storage helper"
        elif not message and object_storage_type:
            message = f"can not found the help for 【{object_storage_type}】"
        super().__init__(message)


class InvalidObjectStorageHelperError(ObjectStorageError):
    """
    不是有效的对象存储帮助类
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "invalid object storage helper"
        super().__init__(message)


class BucketNotEmptyError(ObjectStorageError):
    """
    篮子不为空
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "bucket can not be empty"
        super().__init__(message)
