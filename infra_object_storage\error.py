"""
定义对象存储相关的自定义异常类。
"""

from typing import Optional


class ObjectStorageError(Exception):
    """
    对象存储操作的基类异常。
    所有自定义对象存储异常都应继承自此类。
    """

    def __init__(self, message: Optional[str] = None):
        """
        初始化 ObjectStorageError 实例。

        Args:
            message (Optional[str]): 异常消息。如果未提供，则使用默认消息。
        """
        if not message:
            message = "对象存储操作失败。"
        super().__init__(message)


class ObjectNotPublicReadError(ObjectStorageError):
    """
    当尝试访问一个非公开的对象，但期望它是公开可读时引发。
    """

    def __init__(self, object_name: Optional[str] = None):
        """
        初始化 ObjectNotPublicReadError 实例。

        Args:
            object_name (Optional[str]): 尝试访问的非公开对象的名称。
        """
        if object_name:
            message = f"对象 '{object_name}' 不是公开可读的。"
        else:
            message = "对象不是公开可读的。"
        super().__init__(message)


class BucketNotFound(ObjectStorageError):
    """
    当指定的存储桶不存在时引发。
    """

    def __init__(self, bucket_name: Optional[str] = None):
        """
        初始化 BucketNotFound 实例。

        Args:
            bucket_name (Optional[str]): 未找到的存储桶的名称。
        """
        message = (
            f"存储桶 '{bucket_name}' 未找到。" if bucket_name else "存储桶未找到。"
        )
        super().__init__(message)


class ObjectNotFound(ObjectStorageError):
    """
    当指定的对象在存储桶中不存在时引发。
    """

    def __init__(
        self, object_name: Optional[str] = None, bucket_name: Optional[str] = None
    ):
        """
        初始化 ObjectNotFound 实例。

        Args:
            object_name (Optional[str]): 未找到的对象的名称。
            bucket_name (Optional[str]): 对象所属的存储桶的名称。
        """
        if bucket_name:
            message = f"对象 '{object_name}' 在存储桶 '{bucket_name}' 中未找到。"
        else:
            message = f"对象 '{object_name}' 未找到。"
        super().__init__(message)


class BucketNotExistedError(BucketNotFound):
    """
    存储桶不存在 (已弃用, 请使用 BucketNotFound)。
    此异常是为了兼容旧代码而保留。
    """

    def __init__(self, message: Optional[str] = None):
        """
        初始化 BucketNotExistedError 实例。

        Args:
            message (Optional[str]): 异常消息。如果未提供，则使用默认消息。
        """
        if not message:
            message = "存储桶不存在。"
        super().__init__(message)


class ObjectStorageHelperNotFoundError(ObjectStorageError):
    """
    未找到有效的对象存储帮助类时引发。
    这通常发生在尝试获取特定类型的对象存储客户端时。
    """

    def __init__(
        self, message: Optional[str] = None, object_storage_type: Optional[str] = None
    ):
        """
        初始化 ObjectStorageHelperNotFoundError 实例。

        Args:
            message (Optional[str]): 异常消息。
            object_storage_type (Optional[str]): 未找到帮助类的对象存储类型（例如 's3', 'cos'）。
        """
        if not message and not object_storage_type:
            message = "无法找到有效的对象存储帮助类。"
        elif not message and object_storage_type:
            message = f"无法找到类型为【{object_storage_type}】的对象存储帮助类。"
        super().__init__(message)


class InvalidObjectStorageHelperError(ObjectStorageError):
    """
    当提供的对象存储帮助类无效时引发。
    例如，如果一个类不符合预期的接口。
    """

    def __init__(self, message: Optional[str] = None):
        """
        初始化 InvalidObjectStorageHelperError 实例。

        Args:
            message (Optional[str]): 异常消息。如果未提供，则使用默认消息。
        """
        if not message:
            message = "无效的对象存储帮助类。"
        super().__init__(message)


class BucketNotEmptyError(ObjectStorageError):
    """
    当尝试删除一个非空的存储桶时引发。
    """

    def __init__(self, message: Optional[str] = None):
        """
        初始化 BucketNotEmptyError 实例。

        Args:
            message (Optional[str]): 异常消息。如果未提供，则使用默认消息。
        """
        if not message:
            message = "存储桶不为空。"
        super().__init__(message)
