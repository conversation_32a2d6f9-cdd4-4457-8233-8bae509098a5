# 基础设施对象存储库

## 介绍

本项目是对象存储库，主要包括以下内容

- s3: aws接口实现
- minio: minio实现
- oss: 阿里云对象存储实现
- cos: 腾讯云对象存储实现

### 使用
只是`poetry install infra_object_storage`是不行的，因为没有包含具体实现，依赖如下：

- infra_object_storage[s3]
- infra_object_storage[minio]
- infra_object_storage[cos]
- infra_object_storage[oss]
- infra_object_storage[full]

## 开发指南

### 安装

虚拟环境为poetry，运行`poetry install -E full -vv`即可
或者使用类似如下设置
```toml
infra-object-storage = { version = "^<version>", source = "nexus", extras = ["s3","minio","cos"] }
```

### 发布

1. 在`pyproject.toml`中修改`[tool.poetry]`中的`version`为最新版本
2. 在`CHANGELOG.md`中追加版本修改内容
3. 执行`scripts\publish.bat`即可