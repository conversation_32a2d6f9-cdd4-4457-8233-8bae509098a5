# 基础设施对象存储库

## 介绍

本项目是对象存储库，主要包括以下内容

- s3: aws接口实现
- minio: minio实现
- oss: 阿里云对象存储实现
- cos: 腾讯云对象存储实现

### 使用

基础包只包含核心功能，需要根据使用的存储服务安装对应的额外依赖：

- `infra_object_storage[s3]` - AWS S3支持
- `infra_object_storage[minio]` - MinIO支持
- `infra_object_storage[cos]` - 腾讯云COS支持
- `infra_object_storage[oss]` - 阿里云OSS支持
- `infra_object_storage[full]` - 包含所有存储服务支持

## 开发指南

### 环境准备

本项目使用 `uv` 作为包管理工具。

### 安装

#### 开发环境安装
```bash
# 安装所有依赖（包括开发依赖和所有存储服务支持）
uv sync --extra full --group dev

# 或者只安装特定存储服务支持
uv sync --extra s3 --extra minio --group dev
```

#### 生产环境安装
```toml
# 在 pyproject.toml 或 uv.toml 中配置
[dependency-groups]
production = [
    "infra-object-storage[s3,minio,cos]",
]
```

### 配置

#### 方式一：配置文件
1. 复制 `app.toml.example` 为 `app.toml`
2. 填入真实的配置信息
3. 确保 `app.toml` 不被提交到版本控制系统

#### 方式二：环境变量（推荐生产环境）
1. 复制 `.env.example` 为 `.env`
2. 设置相应的环境变量
3. 环境变量会覆盖配置文件中的设置

#### 使用示例
```python
from infra_object_storage.helper.object_storage_client import ObjectStorageClient

# 从配置文件创建客户端
client = ObjectStorageClient.from_config("app.toml")

# 或者使用环境变量（无需配置文件）
client = ObjectStorageClient.from_config()
```

### 发布

1. 在 `pyproject.toml` 中修改 `version` 为最新版本
2. 在 `CHANGELOG.md` 中追加版本修改内容
3. 设置环境变量：
   ```bash
   set TWINE_USERNAME=your_username
   set TWINE_PASSWORD=your_password
   ```
4. 执行 `scripts\publish.bat`

### 代码检查

运行代码格式化和静态检查：
```bash
scripts\lint.bat
```

## 安全注意事项

- **配置文件安全**：`app.toml` 包含敏感信息，已添加到 `.gitignore`，请勿提交到版本控制
- **环境变量**：生产环境建议使用环境变量或密钥管理系统管理敏感配置
- **密钥轮换**：定期轮换存储服务的访问密钥
- **权限控制**：为存储服务配置最小权限原则

## 测试

```bash
# 激活虚拟环境后运行测试
.\.venv\Scripts\activate.bat && python -m pytest tests\ -v
```