[project]
maintainers = [
  {name = "WebClerk", email = "<EMAIL>"}
]

license = "LicenseRef-Proprietary"
readme = {file = "README.md", content-type = "text/markdown"}

name = "infra-object-storage"
version = "2025.4.3.20250612"
description = "infra object storage"
keywords = ["infra", "object", "storage"]
authors = [
  {name = "WebClerk", email = "<EMAIL>"}
]

classifiers = [
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development",
]

requires-python = ">=3.11, <3.12"

dependencies = [
    "infra-database>=2025.4.13.20250609,<2025.5.0",
    "dependency-injector>=4.46.0",
    "minio>=7.1.13",
    "boto3>=1.38.36",
    "oss2>=2.16.0",
    "cos-python-sdk-v5>=1.9.22",
    "boto3-stubs[s3]>=1.38.36",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "hatchling>=1.27.0",
    "isort>=6.0.0",
    "mypy>=1.15.0",
    "pytest>=8.3.4",
    "ruff>=0.11.10",
    "twine>=6.1.0",
    "types-setuptools>=75.8.0.20250210",
]

[tool.uv.sources]
infra-utility = { index = "nexus" }

[project.urls]
Homepage = "https://www.qindingtech.com"
Documentation = "https://gitlab.qindingtech.com/qindingtech/infra-object-storage/-/blob/main/README.md"
Repository = "https://gitlab.qindingtech.com/qindingtech/infra-object-storage"
Issues = "https://gitlab.qindingtech.com"
Changelog = "https://gitlab.qindingtech.com/qindingtech/infra-object-storage/-/blob/main/CHANGELOG.md"

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
default = true

[[tool.uv.index]]
name = "tencent"
url = "https://mirrors.cloud.tencent.com/pypi/simple/"
explicit = true

[[tool.uv.index]]
name = "nexus"
url = "https://yundi:<EMAIL>/repository/pypi-public/simple/"


[[tool.uv.index]]
name = "releases"
url = "https://repository.qindingtech.com/repository/pypi-releases/simple/"
publish-url = "https://repository.qindingtech.com/repository/pypi-releases/"

[tool.black]
target-version = ['py311']
include = '\.pyi?$'
exclude = '''
/(
  | \.git
  | \.mypy_cache
  | \.pytest_cache
  | \.tox
  | \.venv
  | dist
  | tests
  | profiling
)/
'''

[tool.isort]
profile = "black"
py_version = 311
src_paths = ["infra_object_storage", "tests"]
extend_skip = [".md", ".json"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
namespace_packages = true
check_untyped_defs = true
plugins = []

[[tool.mypy.overrides]]
module = ["infra_utility.*", "infra_basic.*", "infra_database.*"]
ignore_missing_imports = true

[tool.pytest.ini_options]
addopts = "-p no:warnings --doctest-modules"
doctest_optionflags = "NORMALIZE_WHITESPACE ELLIPSIS"
log_cli = true
log_cli_level = "INFO"
log_cli_format = "[%(asctime)s] %(process)d [%(levelname)s]: %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

[tool.ruff]
target-version = "py311"
line-length = 120

[tool.hatchling]
include = ["CHANGELOG.md", "LICENSE.md"]
packages = [
    { include = "infra_object_storage" },
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
