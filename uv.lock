version = 1
revision = 2
requires-python = "==3.11.*"

[[package]]
name = "aliyun-python-sdk-core"
version = "2.16.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "cryptography" },
    { name = "jmespath" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/aliyun-python-sdk-core/2.16.0/aliyun-python-sdk-core-2.16.0.tar.gz", hash = "sha256:651caad597eb39d4fad6cf85133dffe92837d53bdf62db9d8f37dab6508bb8f9" }

[[package]]
name = "aliyun-python-sdk-kms"
version = "2.16.5"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "aliyun-python-sdk-core" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/aliyun-python-sdk-kms/2.16.5/aliyun-python-sdk-kms-2.16.5.tar.gz", hash = "sha256:f328a8a19d83ecbb965ffce0ec1e9930755216d104638cd95ecd362753b813b3" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/aliyun-python-sdk-kms/2.16.5/aliyun_python_sdk_kms-2.16.5-py2.py3-none-any.whl", hash = "sha256:24b6cdc4fd161d2942619479c8d050c63ea9cd22b044fe33b60bbb60153786f0" },
]

[[package]]
name = "annotated-types"
version = "0.7.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/annotated-types/0.7.0/annotated_types-0.7.0.tar.gz", hash = "sha256:aff07c09a53a08bc8cfccb9c85b05f1aa9a2a6f23728d790723543408344ce89" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/annotated-types/0.7.0/annotated_types-0.7.0-py3-none-any.whl", hash = "sha256:1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53" },
]

[[package]]
name = "argon2-cffi"
version = "25.1.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "argon2-cffi-bindings" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi/25.1.0/argon2_cffi-25.1.0.tar.gz", hash = "sha256:694ae5cc8a42f4c4e2bf2ca0e64e51e23a040c6a517a85074683d3959e1346c1" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi/25.1.0/argon2_cffi-25.1.0-py3-none-any.whl", hash = "sha256:fdc8b074db390fccb6eb4a3604ae7231f219aa669a2652e0f20e16ba513d5741" },
]

[[package]]
name = "argon2-cffi-bindings"
version = "21.2.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2-cffi-bindings-21.2.0.tar.gz", hash = "sha256:bb89ceffa6c791807d1305ceb77dbfacc5aa499891d2c55661c6459651fc39e3" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:ccb949252cb2ab3a08c02024acb77cfb179492d5701c7cbdbfd776124d4d2367" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9524464572e12979364b7d600abf96181d3541da11e23ddf565a32e70bd4dc0d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b746dba803a79238e925d9046a63aa26bf86ab2a2fe74ce6b009a1c3f5c8f2ae" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:58ed19212051f49a523abb1dbe954337dc82d947fb6e5a0da60f7c8471a8476c" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:bd46088725ef7f58b5a1ef7ca06647ebaf0eb4baff7d1d0d177c6cc8744abd86" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-musllinux_1_1_i686.whl", hash = "sha256:8cd69c07dd875537a824deec19f978e0f2078fdda07fd5c42ac29668dda5f40f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:f1152ac548bd5b8bcecfb0b0371f082037e47128653df2e8ba6e914d384f3c3e" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-win32.whl", hash = "sha256:603ca0aba86b1349b147cab91ae970c63118a0f30444d4bc80355937c950c082" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-win_amd64.whl", hash = "sha256:b2ef1c30440dbbcba7a5dc3e319408b59676e2e039e2ae11a8775ecf482b192f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp38-abi3-macosx_10_9_universal2.whl", hash = "sha256:e415e3f62c8d124ee16018e491a009937f8cf7ebf5eb430ffc5de21b900dad93" },
]

[[package]]
name = "arrow"
version = "1.3.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "python-dateutil" },
    { name = "types-python-dateutil" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/arrow/1.3.0/arrow-1.3.0.tar.gz", hash = "sha256:d4540617648cb5f895730f1ad8c82a65f2dad0166f57b75f3ca54759c4d67a85" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/arrow/1.3.0/arrow-1.3.0-py3-none-any.whl", hash = "sha256:c728b120ebc00eb84e01882a6f5e7927a53960aa990ce7dd2b10f39005a67f80" },
]

[[package]]
name = "asyncpg"
version = "0.30.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0.tar.gz", hash = "sha256:c551e9928ab6707602f44811817f82ba3c446e018bfe1d3abecc8ba5f3eac851" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:5e0511ad3dec5f6b4f7a9e063591d407eee66b88c14e2ea636f187da1dcfff6a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:915aeb9f79316b43c3207363af12d0e6fd10776641a7de8a01212afd95bdf0ed" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1c198a00cce9506fcd0bf219a799f38ac7a237745e1d27f0e1f66d3707c84a5a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3326e6d7381799e9735ca2ec9fd7be4d5fef5dcbc3cb555d8a463d8460607956" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:51da377487e249e35bd0859661f6ee2b81db11ad1f4fc036194bc9cb2ead5056" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:bc6d84136f9c4d24d358f3b02be4b6ba358abd09f80737d1ac7c444f36108454" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-win32.whl", hash = "sha256:574156480df14f64c2d76450a3f3aaaf26105869cad3865041156b38459e935d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/asyncpg/0.30.0/asyncpg-0.30.0-cp311-cp311-win_amd64.whl", hash = "sha256:3356637f0bd830407b5597317b3cb3571387ae52ddc3bca6233682be88bbbc1f" },
]

[[package]]
name = "backports-tarfile"
version = "1.2.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/backports-tarfile/1.2.0/backports_tarfile-1.2.0.tar.gz", hash = "sha256:d75e02c268746e1b8144c278978b6e98e85de6ad16f8e4b0844a154557eca991" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/backports-tarfile/1.2.0/backports.tarfile-1.2.0-py3-none-any.whl", hash = "sha256:77e284d754527b01fb1e6fa8a1afe577858ebe4e9dad8919e34c862cb399bc34" },
]

[[package]]
name = "black"
version = "25.1.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "click" },
    { name = "mypy-extensions" },
    { name = "packaging" },
    { name = "pathspec" },
    { name = "platformdirs" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/black/25.1.0/black-25.1.0.tar.gz", hash = "sha256:33496d5cd1222ad73391352b4ae8da15253c5de89b93a80b3e2c8d9a19ec2666" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/black/25.1.0/black-25.1.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:a39337598244de4bae26475f77dda852ea00a93bd4c728e09eacd827ec929df0" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/black/25.1.0/black-25.1.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:96c1c7cd856bba8e20094e36e0f948718dc688dba4a9d78c3adde52b9e6c2299" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/black/25.1.0/black-25.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:bce2e264d59c91e52d8000d507eb20a9aca4a778731a08cfff7e5ac4a4bb7096" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/black/25.1.0/black-25.1.0-cp311-cp311-win_amd64.whl", hash = "sha256:172b1dbff09f86ce6f4eb8edf9dede08b1fce58ba194c87d7a4f1a5aa2f5b3c2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/black/25.1.0/black-25.1.0-py3-none-any.whl", hash = "sha256:95e8176dae143ba9097f351d174fdaf0ccd29efb414b362ae3fd72bf0f710717" },
]

[[package]]
name = "boltons"
version = "25.0.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/boltons/25.0.0/boltons-25.0.0.tar.gz", hash = "sha256:e110fbdc30b7b9868cb604e3f71d4722dd8f4dcb4a5ddd06028ba8f1ab0b5ace" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/boltons/25.0.0/boltons-25.0.0-py3-none-any.whl", hash = "sha256:dc9fb38bf28985715497d1b54d00b62ea866eca3938938ea9043e254a3a6ca62" },
]

[[package]]
name = "boto3"
version = "1.38.36"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "botocore" },
    { name = "jmespath" },
    { name = "s3transfer" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/boto3/1.38.36/boto3-1.38.36.tar.gz", hash = "sha256:efe0aaa060f8fedd76e5c942055f051aee0432fc722d79d8830a9fd9db83593e" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/boto3/1.38.36/boto3-1.38.36-py3-none-any.whl", hash = "sha256:34c27d7317cadb62c0e9856e5d5aa0271ef47202d340584831048bc7ac904136" },
]

[[package]]
name = "boto3-stubs"
version = "1.38.36"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "botocore-stubs" },
    { name = "types-s3transfer" },
    { name = "typing-extensions" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/boto3-stubs/1.38.36/boto3_stubs-1.38.36.tar.gz", hash = "sha256:8de916b9433e9224f3bd4a79ed41e508dc532bfee7db34bfd3752901bcd7e69f" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/boto3-stubs/1.38.36/boto3_stubs-1.38.36-py3-none-any.whl", hash = "sha256:1f79b85f93772df94854e9e570a917f1d762f4080a88ed16b9f2e4b98b24f1f1" },
]

[package.optional-dependencies]
s3 = [
    { name = "mypy-boto3-s3" },
]

[[package]]
name = "botocore"
version = "1.38.36"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "jmespath" },
    { name = "python-dateutil" },
    { name = "urllib3" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/botocore/1.38.36/botocore-1.38.36.tar.gz", hash = "sha256:4a1ced1a4218bdff0ed5b46abb54570d473154ddefafa5d121a8d96e4b76ebc1" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/botocore/1.38.36/botocore-1.38.36-py3-none-any.whl", hash = "sha256:b6a50b853f6d23af9edfed89a59800c6bc1687a947cdd3492879f7d64e002d30" },
]

[[package]]
name = "botocore-stubs"
version = "1.38.30"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "types-awscrt" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/botocore-stubs/1.38.30/botocore_stubs-1.38.30.tar.gz", hash = "sha256:291d7bf39a316c00a8a55b7255489b02c0cea1a343482e7784e8d1e235bae995" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/botocore-stubs/1.38.30/botocore_stubs-1.38.30-py3-none-any.whl", hash = "sha256:2efb8bdf36504aff596c670d875d8f7dd15205277c15c4cea54afdba8200c266" },
]

[[package]]
name = "certifi"
version = "2025.6.15"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/certifi/2025.6.15/certifi-2025.6.15.tar.gz", hash = "sha256:d747aa5a8b9bbbb1bb8c22bb13e22bd1f18e9796defa16bab421f7f7a317323b" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/certifi/2025.6.15/certifi-2025.6.15-py3-none-any.whl", hash = "sha256:2e0c7ce7cb5d8f8634ca55d2ba7e6ec2689a2fd6537d8dec1296a477a4910057" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:a45e3c6913c5b87b3ff120dcdc03f6131fa0065027d0ed7ee6190736a74cd401" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:30c5e0cb5ae493c04c8b42916e52ca38079f1b235c2f8ae5f4527b963c401caf" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f75c7ab1f9e4aca5414ed4d8e5c0e303a34f4421f8a0d47a4d019ceff0ab6af4" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a1ed2dd2972641495a3ec98445e09766f077aee98a1c896dcb4ad0d303628e41" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:46bf43160c1a35f7ec506d254e5c890f3c03648a4dbac12d624e4490a7046cd1" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:a24ed04c8ffd54b0729c07cee15a81d964e6fee0e3d4d342a27b020d22959dc6" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:610faea79c43e44c71e1ec53a554553fa22321b65fae24889706c0a84d4ad86d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:a9b15d491f3ad5d692e11f6b71f7857e7835eb677955c00cc0aefcd0669adaf6" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-musllinux_1_1_i686.whl", hash = "sha256:de2ea4b5833625383e464549fec1bc395c1bdeeb5f25c4a3a82b5a8c756ec22f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:fc48c783f9c87e60831201f2cce7f3b2e4846bf4d8728eabe54d60700b318a0b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-win32.whl", hash = "sha256:85a950a4ac9c359340d5963966e3e0a94a676bd6245a4b55bc43949eee26a655" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cffi/1.17.1/cffi-1.17.1-cp311-cp311-win_amd64.whl", hash = "sha256:caaf0640ef5f5517f49bc275eca1406b0ffa6aa184892812030f04c2abf589a0" },
]

[[package]]
name = "chardet"
version = "5.2.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/chardet/5.2.0/chardet-5.2.0.tar.gz", hash = "sha256:1b3b6ff479a8c414bc3fa2c0852995695c4a026dcd6d0633b2dd092ca39c1cf7" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/chardet/5.2.0/chardet-5.2.0-py3-none-any.whl", hash = "sha256:e1cf59446890a00105fe7b7912492ea04b6e6f06d4b742b2c788469e34c82970" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:be1e352acbe3c78727a16a455126d9ff83ea2dfdcbc83148d2982305a04714c2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:aa88ca0b1932e93f2d961bf3addbb2db902198dca337d88c89e1559e066e7645" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:d524ba3f1581b35c03cb42beebab4a13e6cdad7b36246bd22541fa585a56cccd" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:28a1005facc94196e1fb3e82a3d442a9d9110b8434fc1ded7a24a2983c9888d8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fdb20a30fe1175ecabed17cbf7812f7b804b8a315a25f24678bcdf120a90077f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:0f5d9ed7f254402c9e7d35d2f5972c9bbea9040e99cd2861bd77dc68263277c7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:efd387a49825780ff861998cd959767800d54f8308936b21025326de4b5a42b9" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:f0aa37f3c979cf2546b73e8222bbfa3dc07a641585340179d768068e3455e544" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:e70e990b2137b29dc5564715de1e12701815dacc1d056308e2b17e9095372a82" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:0c8c57f84ccfc871a48a47321cfa49ae1df56cd1d965a09abe84066f6853b9c0" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:6b66f92b17849b85cad91259efc341dce9c1af48e2173bf38a85c6329f1033e5" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-win32.whl", hash = "sha256:daac4765328a919a805fa5e2720f3e94767abd632ae410a9062dff5412bae65a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp311-cp311-win_amd64.whl", hash = "sha256:e53efc7c7cee4c1e70661e2e112ca46a575f90ed9ae3fef200f2a25e954f4b28" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0" },
]

[[package]]
name = "click"
version = "8.2.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/click/8.2.1/click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/click/8.2.1/click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/colorama/0.4.6/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/colorama/0.4.6/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6" },
]

[[package]]
name = "cos-python-sdk-v5"
version = "1.9.37"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "crcmod" },
    { name = "pycryptodome" },
    { name = "requests" },
    { name = "six" },
    { name = "xmltodict" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cos-python-sdk-v5/1.9.37/cos_python_sdk_v5-1.9.37.tar.gz", hash = "sha256:59b34b39e55e3afbe9f94c396b57a13c7055c7e1013e5de873bd985cf7ca3858" }

[[package]]
name = "crcmod"
version = "1.7"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/crcmod/1.7/crcmod-1.7.tar.gz", hash = "sha256:dc7051a0db5f2bd48665a990d3ec1cc305a466a77358ca4492826f41f283601e" }

[[package]]
name = "cryptography"
version = "45.0.4"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "cffi", marker = "platform_python_implementation != 'PyPy'" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4.tar.gz", hash = "sha256:7405ade85c83c37682c8fe65554759800a4a8c54b2d96e0f8ad114d31b808d57" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-macosx_10_9_universal2.whl", hash = "sha256:425a9a6ac2823ee6e46a76a21a4e8342d8fa5c01e08b823c1f19a8b74f096069" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:680806cf63baa0039b920f4976f5f31b10e772de42f16310a6839d9f21a26b0d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:4ca0f52170e821bc8da6fc0cc565b7bb8ff8d90d36b5e9fdd68e8a86bdf72036" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:f3fe7a5ae34d5a414957cc7f457e2b92076e72938423ac64d215722f6cf49a9e" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-manylinux_2_28_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:25eb4d4d3e54595dc8adebc6bbd5623588991d86591a78c2548ffb64797341e2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:ce1678a2ccbe696cf3af15a75bb72ee008d7ff183c9228592ede9db467e64f1b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-manylinux_2_34_aarch64.whl", hash = "sha256:49fe9155ab32721b9122975e168a6760d8ce4cffe423bcd7ca269ba41b5dfac1" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-manylinux_2_34_x86_64.whl", hash = "sha256:2882338b2a6e0bd337052e8b9007ced85c637da19ef9ecaf437744495c8c2999" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:23b9c3ea30c3ed4db59e7b9619272e94891f8a3a5591d0b656a7582631ccf750" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:b0a97c927497e3bc36b33987abb99bf17a9a175a19af38a892dc4bbb844d7ee2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-win32.whl", hash = "sha256:e00a6c10a5c53979d6242f123c0a97cff9f3abed7f064fc412c36dc521b5f257" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp311-abi3-win_amd64.whl", hash = "sha256:817ee05c6c9f7a69a16200f0c90ab26d23a87701e2a284bd15156783e46dbcc8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-macosx_10_9_universal2.whl", hash = "sha256:964bcc28d867e0f5491a564b7debb3ffdd8717928d315d12e0d7defa9e43b723" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:6a5bf57554e80f75a7db3d4b1dacaa2764611ae166ab42ea9a72bcdb5d577637" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:46cf7088bf91bdc9b26f9c55636492c1cce3e7aaf8041bbf0243f5e5325cfb2d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:7bedbe4cc930fa4b100fc845ea1ea5788fcd7ae9562e669989c11618ae8d76ee" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-manylinux_2_28_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:eaa3e28ea2235b33220b949c5a0d6cf79baa80eab2eb5607ca8ab7525331b9ff" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:7ef2dde4fa9408475038fc9aadfc1fb2676b174e68356359632e980c661ec8f6" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-manylinux_2_34_aarch64.whl", hash = "sha256:6a3511ae33f09094185d111160fd192c67aa0a2a8d19b54d36e4c78f651dc5ad" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-manylinux_2_34_x86_64.whl", hash = "sha256:06509dc70dd71fa56eaa138336244e2fbaf2ac164fc9b5e66828fccfd2b680d6" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:5f31e6b0a5a253f6aa49be67279be4a7e5a4ef259a9f33c69f7d1b1191939872" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:944e9ccf67a9594137f942d5b52c8d238b1b4e46c7a0c2891b7ae6e01e7c80a4" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-win32.whl", hash = "sha256:c22fe01e53dc65edd1945a2e6f0015e887f84ced233acecb64b4daadb32f5c97" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-cp37-abi3-win_amd64.whl", hash = "sha256:627ba1bc94f6adf0b0a2e35d87020285ead22d9f648c7e75bb64f367375f3b22" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-pp311-pypy311_pp73-macosx_10_9_x86_64.whl", hash = "sha256:4828190fb6c4bcb6ebc6331f01fe66ae838bb3bd58e753b59d4b22eb444b996c" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-pp311-pypy311_pp73-manylinux_2_28_aarch64.whl", hash = "sha256:03dbff8411206713185b8cebe31bc5c0eb544799a50c09035733716b386e61a4" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-pp311-pypy311_pp73-manylinux_2_28_x86_64.whl", hash = "sha256:51dfbd4d26172d31150d84c19bbe06c68ea4b7f11bbc7b3a5e146b367c311349" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-pp311-pypy311_pp73-manylinux_2_34_aarch64.whl", hash = "sha256:0339a692de47084969500ee455e42c58e449461e0ec845a34a6a9b9bf7df7fb8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-pp311-pypy311_pp73-manylinux_2_34_x86_64.whl", hash = "sha256:0cf13c77d710131d33e63626bd55ae7c0efb701ebdc2b3a7952b9b23a0412862" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cryptography/45.0.4/cryptography-45.0.4-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:bbc505d1dc469ac12a0a064214879eac6294038d6b24ae9f71faae1448a9608d" },
]

[[package]]
name = "cytoolz"
version = "1.0.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "toolz" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1.tar.gz", hash = "sha256:89cc3161b89e1bb3ed7636f74ed2e55984fd35516904fc878cae216e42b2c7d6" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:2d958d4f04d9d7018e5c1850790d9d8e68b31c9a2deebca74b903706fdddd2b6" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:0f445b8b731fc0ecb1865b8e68a070084eb95d735d04f5b6c851db2daf3048ab" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1f546a96460a7e28eb2ec439f4664fa646c9b3e51c6ebad9a59d3922bbe65e30" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:0317681dd065532d21836f860b0563b199ee716f55d0c1f10de3ce7100c78a3b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:0c0ef52febd5a7821a3fd8d10f21d460d1a3d2992f724ba9c91fbd7a96745d41" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f5ebaf419acf2de73b643cf96108702b8aef8e825cf4f63209ceb078d5fbbbfd" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:5f7f04eeb4088947585c92d6185a618b25ad4a0f8f66ea30c8db83cf94a425e3" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:f61928803bb501c17914b82d457c6f50fe838b173fb40d39c38d5961185bd6c7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:d2960cb4fa01ccb985ad1280db41f90dc97a80b397af970a15d5a5de403c8c61" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:b2b407cc3e9defa8df5eb46644f6f136586f70ba49eba96f43de67b9a0984fd3" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:8245f929144d4d3bd7b972c9593300195c6cea246b81b4c46053c48b3f044580" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:e37385db03af65763933befe89fa70faf25301effc3b0485fec1c15d4ce4f052" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-win32.whl", hash = "sha256:50f9c530f83e3e574fc95c264c3350adde8145f4f8fc8099f65f00cc595e5ead" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/cytoolz/1.0.1/cytoolz-1.0.1-cp311-cp311-win_amd64.whl", hash = "sha256:b7f6b617454b4326af7bd3c7c49b0fc80767f134eb9fd6449917a058d17a0e3c" },
]

[[package]]
name = "dependency-injector"
version = "4.47.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1.tar.gz", hash = "sha256:b2681b054af767874bf57a17593257d303eec54950515887605e586bbe5c3f05" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-macosx_11_0_arm64.whl", hash = "sha256:69fc6fb277f3eb8635aa807a79fa0c7b0a57ba47179d5b24312db083aef3ce0f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:6798041de0f21194c721618c5d9bab254c9b7bc1fc7b2e80375482dd084b8000" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:3207090ee2d8d01588e0c1c967e19684653d7c36fa55558e8e4b8144cc1bc382" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:5656c7b3369caf37e13ac4714b96013de286066cec0bf9d72d5c59ba252485f7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:c3dad8274ad0f16d45743d5fb7cdcae2ecdfd5e9f6d5cf5a59211b6e7601691f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-musllinux_1_2_i686.whl", hash = "sha256:ae2f3bfbfbda19d73afa98001b91b2b50b3ce076adece1b23daa8633890b6345" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:07d7cc56df5994a0004dce0f54106a9ed0b0b779161ab58fe2d41014e0c9bc36" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-win32.whl", hash = "sha256:5a7a24f6ab6fee574002e36e7c7daa4a86041d2c02a95259beac38d0b46fcd7f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-cp38-abi3-win_amd64.whl", hash = "sha256:c04a897b761e2c55bb71986fdac721db6583d474699bc1d99c51764f11b592b6" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:d2b3fb6fa8a7c85536c11d78bad1a1ced9966e6ba6f35faf72e2e00d246114e4" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-pp311-pypy311_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c7819478442b01c9a38db97259272b0bccaf06fbbd5c6e432df07df6304f79dd" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-pp311-pypy311_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:275747b4858c19b50ba5a9b17f28702c15b12f6d68ac3722126d115d29d7ce00" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-pp311-pypy311_pp73-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f66b986de1b3c8ad9dd804a29ad8caa16a98262488dacdc2c7b3d59f34304528" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/dependency-injector/4.47.1/dependency_injector-4.47.1-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:9f112075d5fc924aba5c79a5ffabf62f9f468e04426463a46b7fb29f525ff2ba" },
]

[[package]]
name = "docutils"
version = "0.21.2"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/docutils/0.21.2/docutils-0.21.2.tar.gz", hash = "sha256:3a6b18732edf182daa3cd12775bbb338cf5691468f91eeeb109deff6ebfa986f" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/docutils/0.21.2/docutils-0.21.2-py3-none-any.whl", hash = "sha256:dafca5b9e384f0e419294eb4d2ff9fa826435bf15f15b7bd45723e8ad76811b2" },
]

[[package]]
name = "greenlet"
version = "3.2.3"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3.tar.gz", hash = "sha256:8b0dd8ae4c0d6f5e54ee55ba935eeb3d735a9b58a8a1e5b5cbab64e01a39f365" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-macosx_11_0_universal2.whl", hash = "sha256:784ae58bba89fa1fa5733d170d42486580cab9decda3484779f4759345b29822" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:0921ac4ea42a5315d3446120ad48f90c3a6b9bb93dd9b3cf4e4d84a66e42de83" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-manylinux2014_ppc64le.manylinux_2_17_ppc64le.whl", hash = "sha256:d2971d93bb99e05f8c2c0c2f4aa9484a18d98c4c3bd3c62b65b7e6ae33dfcfaf" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-manylinux2014_s390x.manylinux_2_17_s390x.whl", hash = "sha256:c667c0bf9d406b77a15c924ef3285e1e05250948001220368e039b6aa5b5034b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:592c12fb1165be74592f5de0d70f82bc5ba552ac44800d632214b76089945147" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:29e184536ba333003540790ba29829ac14bb645514fbd7e32af331e8202a62a5" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:93c0bb79844a367782ec4f429d07589417052e621aa39a5ac1fb99c5aa308edc" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:751261fc5ad7b6705f5f76726567375bb2104a059454e0226e1eef6c756748ba" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/greenlet/3.2.3/greenlet-3.2.3-cp311-cp311-win_amd64.whl", hash = "sha256:83a8761c75312361aa2b5b903b79da97f13f556164a7dd2d5448655425bd4c34" },
]

[[package]]
name = "hatchling"
version = "1.27.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "packaging" },
    { name = "pathspec" },
    { name = "pluggy" },
    { name = "trove-classifiers" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/hatchling/1.27.0/hatchling-1.27.0.tar.gz", hash = "sha256:971c296d9819abb3811112fc52c7a9751c8d381898f36533bb16f9791e941fd6" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/hatchling/1.27.0/hatchling-1.27.0-py3-none-any.whl", hash = "sha256:d3a2f3567c4f926ea39849cdf924c7e99e6686c9c8e288ae1037c8fa2a5d937b" },
]

[[package]]
name = "id"
version = "1.5.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "requests" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/id/1.5.0/id-1.5.0.tar.gz", hash = "sha256:292cb8a49eacbbdbce97244f47a97b4c62540169c976552e497fd57df0734c1d" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/id/1.5.0/id-1.5.0-py3-none-any.whl", hash = "sha256:f1434e1cef91f2cbb8a4ec64663d5a23b9ed43ef44c4c957d02583d61714c658" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/idna/3.10/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/idna/3.10/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3" },
]

[[package]]
name = "importlib-metadata"
version = "8.7.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "zipp" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/importlib-metadata/8.7.0/importlib_metadata-8.7.0.tar.gz", hash = "sha256:d13b81ad223b890aa16c5471f2ac3056cf76c5f10f82d6f9292f0b415f389000" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/importlib-metadata/8.7.0/importlib_metadata-8.7.0-py3-none-any.whl", hash = "sha256:e5dd1551894c77868a30651cef00984d50e1002d06942a7101d34870c5f02afd" },
]

[[package]]
name = "infra-basic"
version = "2025.4.27.20250614"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "infra-utility" },
    { name = "orjson" },
    { name = "pydantic" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-basic/2025.4.27.20250614/infra_basic-2025.4.27.20250614.tar.gz", hash = "sha256:ac0570817fe227a254b49d320ab9bb11d2c62da029243b9b47a60558c4827d9f" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-basic/2025.4.27.20250614/infra_basic-2025.4.27.20250614-py3-none-any.whl", hash = "sha256:162807e544989ee5e56b59f131a1257e4d3f93e57247391d89e6618a686bf233" },
]

[[package]]
name = "infra-database"
version = "2025.4.108.20250616"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "asyncpg" },
    { name = "infra-basic" },
    { name = "infra-geo" },
    { name = "lxml" },
    { name = "lxml-stubs" },
    { name = "numpy" },
    { name = "psycopg2" },
    { name = "pydantic" },
    { name = "shapely" },
    { name = "sqlalchemy" },
    { name = "types-psycopg2" },
    { name = "types-shapely" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-database/2025.4.108.20250616/infra_database-2025.4.108.20250616.tar.gz", hash = "sha256:fede5deddae6c220fbd7936951f36849366285ec050f32d532459436945d2b96" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-database/2025.4.108.20250616/infra_database-2025.4.108.20250616-py3-none-any.whl", hash = "sha256:fcd1d4850a897c2bb102f9b1ff89b07f37b71e24a90fd12fcc53e69c291d2afc" },
]

[[package]]
name = "infra-geo"
version = "2025.4.3.20250506"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "infra-basic" },
    { name = "shapely" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-geo/2025.4.3.20250506/infra_geo-2025.4.3.20250506.tar.gz", hash = "sha256:58c4f8885d4a8ed4c29e9f4af4d91736d70813db219df1ca6d33766f72760e67" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-geo/2025.4.3.20250506/infra_geo-2025.4.3.20250506-py3-none-any.whl", hash = "sha256:5a72ccde3aee04a6d62d2b87129734caa29975330903b59e168eed9c14a47aed" },
]

[[package]]
name = "infra-object-storage"
version = "2025.4.3.20250612"
source = { editable = "." }
dependencies = [
    { name = "boto3" },
    { name = "cos-python-sdk-v5" },
    { name = "dependency-injector" },
    { name = "infra-database" },
    { name = "minio" },
    { name = "mypy-boto3-s3" },
    { name = "oss2" },
]

[package.optional-dependencies]
cos = [
    { name = "cos-python-sdk-v5" },
]
full = [
    { name = "boto3" },
    { name = "boto3-stubs", extra = ["s3"] },
    { name = "cos-python-sdk-v5" },
    { name = "minio" },
    { name = "oss2" },
]
minio = [
    { name = "minio" },
]
oss = [
    { name = "oss2" },
]
s3 = [
    { name = "boto3" },
    { name = "boto3-stubs", extra = ["s3"] },
]

[package.dev-dependencies]
dev = [
    { name = "black" },
    { name = "hatchling" },
    { name = "isort" },
    { name = "mypy" },
    { name = "pytest" },
    { name = "ruff" },
    { name = "twine" },
    { name = "types-setuptools" },
]

[package.metadata]
requires-dist = [
    { name = "boto3", specifier = ">=1.38.36" },
    { name = "boto3", marker = "extra == 'full'", specifier = ">=1.38.36" },
    { name = "boto3", marker = "extra == 's3'", specifier = ">=1.38.36" },
    { name = "boto3-stubs", extras = ["s3"], marker = "extra == 'full'", specifier = ">=1.38.36" },
    { name = "boto3-stubs", extras = ["s3"], marker = "extra == 's3'", specifier = ">=1.38.36" },
    { name = "cos-python-sdk-v5", specifier = ">=1.9.37" },
    { name = "cos-python-sdk-v5", marker = "extra == 'cos'", specifier = ">=1.9.22" },
    { name = "cos-python-sdk-v5", marker = "extra == 'full'", specifier = ">=1.9.22" },
    { name = "dependency-injector", specifier = ">=4.46.0" },
    { name = "infra-database", specifier = ">=2025.4.13.20250609,<2025.5.0" },
    { name = "minio", specifier = ">=7.2.15" },
    { name = "minio", marker = "extra == 'full'", specifier = ">=7.1.13" },
    { name = "minio", marker = "extra == 'minio'", specifier = ">=7.1.13" },
    { name = "mypy-boto3-s3", specifier = ">=1.38.26" },
    { name = "oss2", specifier = ">=2.19.1" },
    { name = "oss2", marker = "extra == 'full'", specifier = ">=2.16.0" },
    { name = "oss2", marker = "extra == 'oss'", specifier = ">=2.16.0" },
]
provides-extras = ["s3", "minio", "cos", "oss", "full"]

[package.metadata.requires-dev]
dev = [
    { name = "black", specifier = ">=25.1.0" },
    { name = "hatchling", specifier = ">=1.27.0" },
    { name = "isort", specifier = ">=6.0.0" },
    { name = "mypy", specifier = ">=1.15.0" },
    { name = "pytest", specifier = ">=8.3.4" },
    { name = "ruff", specifier = ">=0.11.10" },
    { name = "twine", specifier = ">=6.1.0" },
    { name = "types-setuptools", specifier = ">=75.8.0.20250210" },
]

[[package]]
name = "infra-utility"
version = "2025.4.4.20250506"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "arrow" },
    { name = "boltons" },
    { name = "chardet" },
    { name = "cytoolz" },
    { name = "loguru" },
    { name = "pycryptodome" },
    { name = "shortuuid" },
    { name = "ulid-py" },
    { name = "uuid-utils" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-utility/2025.4.4.20250506/infra_utility-2025.4.4.20250506.tar.gz", hash = "sha256:86731dc46140622ff51bce3aceaa34956fc486b3599e589bd09f1dbf85aeb3a3" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/infra-utility/2025.4.4.20250506/infra_utility-2025.4.4.20250506-py3-none-any.whl", hash = "sha256:40dff1b964e8766eaf0f59b329956f74d1d7e3492b69c4e4e20ce9ce922f7c76" },
]

[[package]]
name = "iniconfig"
version = "2.1.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/iniconfig/2.1.0/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/iniconfig/2.1.0/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760" },
]

[[package]]
name = "isort"
version = "6.0.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/isort/6.0.1/isort-6.0.1.tar.gz", hash = "sha256:1cb5df28dfbc742e490c5e41bad6da41b805b0a8be7bc93cd0fb2a8a890ac450" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/isort/6.0.1/isort-6.0.1-py3-none-any.whl", hash = "sha256:2dc5d7f65c9678d94c88dfc29161a320eec67328bc97aad576874cb4be1e9615" },
]

[[package]]
name = "jaraco-classes"
version = "3.4.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "more-itertools" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jaraco-classes/3.4.0/jaraco.classes-3.4.0.tar.gz", hash = "sha256:47a024b51d0239c0dd8c8540c6c7f484be3b8fcf0b2d85c13825780d3b3f3acd" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jaraco-classes/3.4.0/jaraco.classes-3.4.0-py3-none-any.whl", hash = "sha256:f662826b6bed8cace05e7ff873ce0f9283b5c924470fe664fff1c2f00f581790" },
]

[[package]]
name = "jaraco-context"
version = "6.0.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "backports-tarfile" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jaraco-context/6.0.1/jaraco_context-6.0.1.tar.gz", hash = "sha256:9bae4ea555cf0b14938dc0aee7c9f32ed303aa20a3b73e7dc80111628792d1b3" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jaraco-context/6.0.1/jaraco.context-6.0.1-py3-none-any.whl", hash = "sha256:f797fc481b490edb305122c9181830a3a5b76d84ef6d1aef2fb9b47ab956f9e4" },
]

[[package]]
name = "jaraco-functools"
version = "4.1.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "more-itertools" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jaraco-functools/4.1.0/jaraco_functools-4.1.0.tar.gz", hash = "sha256:70f7e0e2ae076498e212562325e805204fc092d7b4c17e0e86c959e249701a9d" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jaraco-functools/4.1.0/jaraco.functools-4.1.0-py3-none-any.whl", hash = "sha256:ad159f13428bc4acbf5541ad6dec511f91573b90fba04df61dafa2a1231cf649" },
]

[[package]]
name = "jeepney"
version = "0.9.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jeepney/0.9.0/jeepney-0.9.0.tar.gz", hash = "sha256:cf0e9e845622b81e4a28df94c40345400256ec608d0e55bb8a3feaa9163f5732" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jeepney/0.9.0/jeepney-0.9.0-py3-none-any.whl", hash = "sha256:97e5714520c16fc0a45695e5365a2e11b81ea79bba796e26f9f1d178cb182683" },
]

[[package]]
name = "jmespath"
version = "0.10.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jmespath/0.10.0/jmespath-0.10.0.tar.gz", hash = "sha256:b85d0567b8666149a93172712e68920734333c0ce7e89b78b3e987f71e5ed4f9" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/jmespath/0.10.0/jmespath-0.10.0-py2.py3-none-any.whl", hash = "sha256:cdf6525904cc597730141d61b36f2e4b8ecc257c420fa2f4549bac2c2d0cb72f" },
]

[[package]]
name = "keyring"
version = "25.6.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "importlib-metadata" },
    { name = "jaraco-classes" },
    { name = "jaraco-context" },
    { name = "jaraco-functools" },
    { name = "jeepney", marker = "sys_platform == 'linux'" },
    { name = "pywin32-ctypes", marker = "sys_platform == 'win32'" },
    { name = "secretstorage", marker = "sys_platform == 'linux'" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/keyring/25.6.0/keyring-25.6.0.tar.gz", hash = "sha256:0b39998aa941431eb3d9b0d4b2460bc773b9df6fed7621c2dfb291a7e0187a66" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/keyring/25.6.0/keyring-25.6.0-py3-none-any.whl", hash = "sha256:552a3f7af126ece7ed5c89753650eec89c7eaae8617d0aa4d9ad2b75111266bd" },
]

[[package]]
name = "loguru"
version = "0.7.3"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "win32-setctime", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/loguru/0.7.3/loguru-0.7.3.tar.gz", hash = "sha256:19480589e77d47b8d85b2c827ad95d49bf31b0dcde16593892eb51dd18706eb6" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/loguru/0.7.3/loguru-0.7.3-py3-none-any.whl", hash = "sha256:31a33c10c8e1e10422bfd431aeb5d351c7cf7fa671e3c4df004162264b28220c" },
]

[[package]]
name = "lxml"
version = "5.4.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0.tar.gz", hash = "sha256:d12832e1dbea4be280b22fd0ea7c9b87f0d8fc51ba06e92dc62d52f804f78ebd" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:98a3912194c079ef37e716ed228ae0dcb960992100461b704aea4e93af6b0bb9" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:0ea0252b51d296a75f6118ed0d8696888e7403408ad42345d7dfd0d1e93309a7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:b92b69441d1bd39f4940f9eadfa417a25862242ca2c396b406f9272ef09cdcaa" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:20e16c08254b9b6466526bc1828d9370ee6c0d60a4b64836bc3ac2917d1e16df" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:7605c1c32c3d6e8c990dd28a0970a3cbbf1429d5b92279e37fda05fb0c92190e" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:ecf4c4b83f1ab3d5a7ace10bafcb6f11df6156857a3c418244cef41ca9fa3e44" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0cef4feae82709eed352cd7e97ae062ef6ae9c7b5dbe3663f104cd2c0e8d94ba" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_28_aarch64.whl", hash = "sha256:df53330a3bff250f10472ce96a9af28628ff1f4efc51ccba351a8820bca2a8ba" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_28_ppc64le.whl", hash = "sha256:aefe1a7cb852fa61150fcb21a8c8fcea7b58c4cb11fbe59c97a0a4b31cae3c8c" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_28_s390x.whl", hash = "sha256:ef5a7178fcc73b7d8c07229e89f8eb45b2908a9238eb90dcfc46571ccf0383b8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-manylinux_2_28_x86_64.whl", hash = "sha256:d2ed1b3cb9ff1c10e6e8b00941bb2e5bb568b307bfc6b17dffbbe8be5eecba86" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:72ac9762a9f8ce74c9eed4a4e74306f2f18613a6b71fa065495a67ac227b3056" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:f5cb182f6396706dc6cc1896dd02b1c889d644c081b0cdec38747573db88a7d7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:3a3178b4873df8ef9457a4875703488eb1622632a9cee6d76464b60e90adbfcd" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:e094ec83694b59d263802ed03a8384594fcce477ce484b0cbcd0008a211ca751" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-win32.whl", hash = "sha256:4329422de653cdb2b72afa39b0aa04252fca9071550044904b2e7036d9d97fe4" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml/5.4.0/lxml-5.4.0-cp311-cp311-win_amd64.whl", hash = "sha256:fd3be6481ef54b8cfd0e1e953323b7aa9d9789b94842d0e5b142ef4bb7999539" },
]

[[package]]
name = "lxml-stubs"
version = "0.5.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml-stubs/0.5.1/lxml-stubs-0.5.1.tar.gz", hash = "sha256:e0ec2aa1ce92d91278b719091ce4515c12adc1d564359dfaf81efa7d4feab79d" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/lxml-stubs/0.5.1/lxml_stubs-0.5.1-py3-none-any.whl", hash = "sha256:1f689e5dbc4b9247cb09ae820c7d34daeb1fdbd1db06123814b856dae7787272" },
]

[[package]]
name = "markdown-it-py"
version = "3.0.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "mdurl" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/markdown-it-py/3.0.0/markdown-it-py-3.0.0.tar.gz", hash = "sha256:e3f60a94fa066dc52ec76661e37c851cb232d92f9886b15cb560aaada2df8feb" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/markdown-it-py/3.0.0/markdown_it_py-3.0.0-py3-none-any.whl", hash = "sha256:355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1" },
]

[[package]]
name = "mdurl"
version = "0.1.2"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mdurl/0.1.2/mdurl-0.1.2.tar.gz", hash = "sha256:bb413d29f5eea38f31dd4754dd7377d4465116fb207585f97bf925588687c1ba" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mdurl/0.1.2/mdurl-0.1.2-py3-none-any.whl", hash = "sha256:84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8" },
]

[[package]]
name = "minio"
version = "7.2.15"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "argon2-cffi" },
    { name = "certifi" },
    { name = "pycryptodome" },
    { name = "typing-extensions" },
    { name = "urllib3" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/minio/7.2.15/minio-7.2.15.tar.gz", hash = "sha256:5247df5d4dca7bfa4c9b20093acd5ad43e82d8710ceb059d79c6eea970f49f79" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/minio/7.2.15/minio-7.2.15-py3-none-any.whl", hash = "sha256:c06ef7a43e5d67107067f77b6c07ebdd68733e5aa7eed03076472410ca19d876" },
]

[[package]]
name = "more-itertools"
version = "10.7.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/more-itertools/10.7.0/more_itertools-10.7.0.tar.gz", hash = "sha256:9fddd5403be01a94b204faadcff459ec3568cf110265d3c54323e1e866ad29d3" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/more-itertools/10.7.0/more_itertools-10.7.0-py3-none-any.whl", hash = "sha256:d43980384673cb07d2f7d2d918c616b30c659c089ee23953f601d6609c67510e" },
]

[[package]]
name = "mypy"
version = "1.16.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "mypy-extensions" },
    { name = "pathspec" },
    { name = "typing-extensions" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0.tar.gz", hash = "sha256:84b94283f817e2aa6350a14b4a8fb2a35a53c286f97c9d30f53b63620e7af8ab" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:9f826aaa7ff8443bac6a494cf743f591488ea940dd360e7dd330e30dd772a5ab" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:82d056e6faa508501af333a6af192c700b33e15865bda49611e3d7d8358ebea2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:089bedc02307c2548eb51f426e085546db1fa7dd87fbb7c9fa561575cf6eb1ff" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:6a2322896003ba66bbd1318c10d3afdfe24e78ef12ea10e2acd985e9d684a666" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:021a68568082c5b36e977d54e8f1de978baf401a33884ffcea09bd8e88a98f4c" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0-cp311-cp311-win_amd64.whl", hash = "sha256:54066fed302d83bf5128632d05b4ec68412e1f03ef2c300434057d66866cea4b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy/1.16.0/mypy-1.16.0-py3-none-any.whl", hash = "sha256:29e1499864a3888bca5c1542f2d7232c6e586295183320caa95758fc84034031" },
]

[[package]]
name = "mypy-boto3-s3"
version = "1.38.26"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy-boto3-s3/1.38.26/mypy_boto3_s3-1.38.26.tar.gz", hash = "sha256:38a45dee5782d5c07ddea07ea50965c4d2ba7e77617c19f613b4c9f80f961b52" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy-boto3-s3/1.38.26/mypy_boto3_s3-1.38.26-py3-none-any.whl", hash = "sha256:1129d64be1aee863e04f0c92ac8d315578f13ccae64fa199b20ad0950d2b9616" },
]

[[package]]
name = "mypy-extensions"
version = "1.1.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy-extensions/1.1.0/mypy_extensions-1.1.0.tar.gz", hash = "sha256:52e68efc3284861e772bbcd66823fde5ae21fd2fdb51c62a211403730b916558" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/mypy-extensions/1.1.0/mypy_extensions-1.1.0-py3-none-any.whl", hash = "sha256:1be4cccdb0f2482337c4743e60421de3a356cd97508abadd57d47403e94f5505" },
]

[[package]]
name = "nh3"
version = "0.2.21"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21.tar.gz", hash = "sha256:4990e7ee6a55490dbf00d61a6f476c9a3258e31e711e13713b2ea7d6616f670e" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl", hash = "sha256:a772dec5b7b7325780922dd904709f0f5f3a79fbf756de5291c01370f6df0967" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d002b648592bf3033adfd875a48f09b8ecc000abd7f6a8769ed86b6ccc70c759" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:2a5174551f95f2836f2ad6a8074560f261cf9740a48437d6151fd2d4d7d617ab" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", hash = "sha256:b8d55ea1fc7ae3633d758a92aafa3505cd3cc5a6e40470c9164d54dff6f96d42" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:6ae319f17cd8960d0612f0f0ddff5a90700fa71926ca800e9028e7851ce44a6f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:63ca02ac6f27fc80f9894409eb61de2cb20ef0a23740c7e29f9ec827139fa578" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:a5f77e62aed5c4acad635239ac1290404c7e940c81abe561fd2af011ff59f585" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:087ffadfdcd497658c3adc797258ce0f06be8a537786a7217649fc1c0c60c293" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:ac7006c3abd097790e611fe4646ecb19a8d7f2184b882f6093293b8d9b887431" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_armv7l.whl", hash = "sha256:6141caabe00bbddc869665b35fc56a478eb774a8c1dfd6fba9fe1dfdf29e6efa" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_i686.whl", hash = "sha256:20979783526641c81d2f5bfa6ca5ccca3d1e4472474b162c6256745fbfe31cd1" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:a7ea28cd49293749d67e4fcf326c554c83ec912cd09cd94aa7ec3ab1921c8283" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-win32.whl", hash = "sha256:6c9c30b8b0d291a7c5ab0967ab200598ba33208f754f2f4920e9343bdd88f79a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-win_amd64.whl", hash = "sha256:bb0014948f04d7976aabae43fcd4cb7f551f9f8ce785a4c9ef66e6c2590f8629" },
]

[[package]]
name = "numpy"
version = "2.3.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0.tar.gz", hash = "sha256:581f87f9e9e9db2cba2141400e160e9dd644ee248788d6f90636eeb8fd9260a6" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:c3c9fdde0fa18afa1099d6257eb82890ea4f3102847e692193b54e00312a9ae9" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:46d16f72c2192da7b83984aa5455baee640e33a9f1e61e656f29adf55e406c2b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-macosx_14_0_arm64.whl", hash = "sha256:a0be278be9307c4ab06b788f2a077f05e180aea817b3e41cebbd5aaf7bd85ed3" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-macosx_14_0_x86_64.whl", hash = "sha256:99224862d1412d2562248d4710126355d3a8db7672170a39d6909ac47687a8a4" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-manylinux_2_28_aarch64.whl", hash = "sha256:2393a914db64b0ead0ab80c962e42d09d5f385802006a6c87835acb1f58adb96" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-manylinux_2_28_x86_64.whl", hash = "sha256:7729c8008d55e80784bd113787ce876ca117185c579c0d626f59b87d433ea779" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:06d4fb37a8d383b769281714897420c5cc3545c79dc427df57fc9b852ee0bf58" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:c39ec392b5db5088259c68250e342612db82dc80ce044cf16496cf14cf6bc6f8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-win32.whl", hash = "sha256:ee9d3ee70d62827bc91f3ea5eee33153212c41f639918550ac0475e3588da59f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-win_amd64.whl", hash = "sha256:43c55b6a860b0eb44d42341438b03513cf3879cb3617afb749ad49307e164edd" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-cp311-cp311-win_arm64.whl", hash = "sha256:2e6a1409eee0cb0316cb64640a49a49ca44deb1a537e6b1121dc7c458a1299a8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-pp311-pypy311_pp73-macosx_10_15_x86_64.whl", hash = "sha256:80b46117c7359de8167cc00a2c7d823bdd505e8c7727ae0871025a86d668283b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-pp311-pypy311_pp73-macosx_14_0_arm64.whl", hash = "sha256:5814a0f43e70c061f47abd5857d120179609ddc32a613138cbb6c4e9e2dbdda5" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-pp311-pypy311_pp73-macosx_14_0_x86_64.whl", hash = "sha256:ef6c1e88fd6b81ac6d215ed71dc8cd027e54d4bf1d2682d362449097156267a2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-pp311-pypy311_pp73-manylinux_2_28_aarch64.whl", hash = "sha256:33a5a12a45bb82d9997e2c0b12adae97507ad7c347546190a18ff14c28bbca12" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-pp311-pypy311_pp73-manylinux_2_28_x86_64.whl", hash = "sha256:54dfc8681c1906d239e95ab1508d0a533c4a9505e52ee2d71a5472b04437ef97" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/numpy/2.3.0/numpy-2.3.0-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:e017a8a251ff4d18d71f139e28bdc7c31edba7a507f72b1414ed902cbe48c74d" },
]

[[package]]
name = "orjson"
version = "3.10.18"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18.tar.gz", hash = "sha256:e8da3947d92123eda795b68228cafe2724815621fe35e8e320a9e9593a4bcd53" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl", hash = "sha256:e0a183ac3b8e40471e8d843105da6fbe7c070faab023be3b08188ee3f85719b8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-macosx_15_0_arm64.whl", hash = "sha256:5ef7c164d9174362f85238d0cd4afdeeb89d9e523e4651add6a5d458d6f7d42d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:afd14c5d99cdc7bf93f22b12ec3b294931518aa019e2a147e8aa2f31fd3240f7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:7b672502323b6cd133c4af6b79e3bea36bad2d16bca6c1f645903fce83909a7a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:51f8c63be6e070ec894c629186b1c0fe798662b8687f3d9fdfa5e401c6bd7679" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:3f9478ade5313d724e0495d167083c6f3be0dd2f1c9c8a38db9a9e912cdaf947" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:187aefa562300a9d382b4b4eb9694806e5848b0cedf52037bb5c228c61bb66d4" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9da552683bc9da222379c7a01779bddd0ad39dd699dd6300abaf43eadee38334" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:e450885f7b47a0231979d9c49b567ed1c4e9f69240804621be87c40bc9d3cf17" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-musllinux_1_2_armv7l.whl", hash = "sha256:5e3c9cc2ba324187cd06287ca24f65528f16dfc80add48dc99fa6c836bb3137e" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:50ce016233ac4bfd843ac5471e232b865271d7d9d44cf9d33773bcd883ce442b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:b3ceff74a8f7ffde0b2785ca749fc4e80e4315c0fd887561144059fb1c138aa7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-win32.whl", hash = "sha256:fdba703c722bd868c04702cac4cb8c6b8ff137af2623bc0ddb3b3e6a2c8996c1" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-win_amd64.whl", hash = "sha256:c28082933c71ff4bc6ccc82a454a2bffcef6e1d7379756ca567c772e4fb3278a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/orjson/3.10.18/orjson-3.10.18-cp311-cp311-win_arm64.whl", hash = "sha256:a6c7c391beaedd3fa63206e5c2b7b554196f14debf1ec9deb54b5d279b1b46f5" },
]

[[package]]
name = "oss2"
version = "2.19.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "aliyun-python-sdk-core" },
    { name = "aliyun-python-sdk-kms" },
    { name = "crcmod" },
    { name = "pycryptodome" },
    { name = "requests" },
    { name = "six" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/oss2/2.19.1/oss2-2.19.1.tar.gz", hash = "sha256:a8ab9ee7eb99e88a7e1382edc6ea641d219d585a7e074e3776e9dec9473e59c1" }

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/packaging/25.0/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/packaging/25.0/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484" },
]

[[package]]
name = "pathspec"
version = "0.12.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pathspec/0.12.1/pathspec-0.12.1.tar.gz", hash = "sha256:a482d51503a1ab33b1c67a6c3813a26953dbdc71c31dacaef9a838c4e29f5712" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pathspec/0.12.1/pathspec-0.12.1-py3-none-any.whl", hash = "sha256:a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08" },
]

[[package]]
name = "platformdirs"
version = "4.3.8"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/platformdirs/4.3.8/platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/platformdirs/4.3.8/platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4" },
]

[[package]]
name = "pluggy"
version = "1.6.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pluggy/1.6.0/pluggy-1.6.0.tar.gz", hash = "sha256:7dcc130b76258d33b90f61b658791dede3486c3e6bfb003ee5c9bfb396dd22f3" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pluggy/1.6.0/pluggy-1.6.0-py3-none-any.whl", hash = "sha256:e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746" },
]

[[package]]
name = "psycopg2"
version = "2.9.10"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/psycopg2/2.9.10/psycopg2-2.9.10.tar.gz", hash = "sha256:12ec0b40b0273f95296233e8750441339298e6a572f7039da5b260e3c8b60e11" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/psycopg2/2.9.10/psycopg2-2.9.10-cp311-cp311-win32.whl", hash = "sha256:47c4f9875125344f4c2b870e41b6aad585901318068acd01de93f3677a6522c2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/psycopg2/2.9.10/psycopg2-2.9.10-cp311-cp311-win_amd64.whl", hash = "sha256:0435034157049f6846e95103bd8f5a668788dd913a7c30162ca9503fdf542cb4" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycparser/2.22/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycparser/2.22/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc" },
]

[[package]]
name = "pycryptodome"
version = "3.23.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0.tar.gz", hash = "sha256:447700a657182d60338bab09fdb27518f8856aecd80ae4c6bdddb67ff5da44ef" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-macosx_10_9_universal2.whl", hash = "sha256:187058ab80b3281b1de11c2e6842a357a1f71b42cb1e15bce373f3d238135c27" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-macosx_10_9_x86_64.whl", hash = "sha256:cfb5cd445280c5b0a4e6187a7ce8de5a07b5f3f897f235caa11f1f435f182843" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:67bd81fcbe34f43ad9422ee8fd4843c8e7198dd88dd3d40e6de42ee65fbe1490" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c8987bd3307a39bc03df5c8e0e3d8be0c4c3518b7f044b0f4c15d1aa78f52575" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:aa0698f65e5b570426fc31b8162ed4603b0c2841cbb9088e2b01641e3065915b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:53ecbafc2b55353edcebd64bf5da94a2a2cdf5090a6915bcca6eca6cc452585a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-musllinux_1_2_i686.whl", hash = "sha256:156df9667ad9f2ad26255926524e1c136d6664b741547deb0a86a9acf5ea631f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:dea827b4d55ee390dc89b2afe5927d4308a8b538ae91d9c6f7a5090f397af1aa" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-win32.whl", hash = "sha256:507dbead45474b62b2bbe318eb1c4c8ee641077532067fec9c1aa82c31f84886" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-win_amd64.whl", hash = "sha256:c75b52aacc6c0c260f204cbdd834f76edc9fb0d8e0da9fbf8352ef58202564e2" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-win_arm64.whl", hash = "sha256:11eeeb6917903876f134b56ba11abe95c0b0fd5e3330def218083c7d98bbcb3c" },
]

[[package]]
name = "pydantic"
version = "2.11.7"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "annotated-types" },
    { name = "pydantic-core" },
    { name = "typing-extensions" },
    { name = "typing-inspection" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic/2.11.7/pydantic-2.11.7.tar.gz", hash = "sha256:d989c3c6cb79469287b1569f7447a17848c998458d49ebe294e975b9baf0f0db" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic/2.11.7/pydantic-2.11.7-py3-none-any.whl", hash = "sha256:dde5df002701f6de26248661f6835bbe296a47bf73990135c7d07ce741b9623b" },
]

[[package]]
name = "pydantic-core"
version = "2.33.2"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2.tar.gz", hash = "sha256:7cb8bc3605c29176e1b105350d2e6474142d7c1bd1d9327c4a9bdb46bf827acc" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-macosx_10_12_x86_64.whl", hash = "sha256:4c5b0a576fb381edd6d27f0a85915c6daf2f8138dc5c267a57c08a62900758c7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:e799c050df38a639db758c617ec771fd8fb7a5f8eaaa4b27b101f266b216a246" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dc46a01bf8d62f227d5ecee74178ffc448ff4e5197c756331f71efcc66dc980f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:a144d4f717285c6d9234a66778059f33a89096dfb9b39117663fd8413d582dcc" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:73cf6373c21bc80b2e0dc88444f41ae60b2f070ed02095754eb5a01df12256de" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:3dc625f4aa79713512d1976fe9f0bc99f706a9dee21dfd1810b4bbbf228d0e8a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:881b21b5549499972441da4758d662aeea93f1923f953e9cbaff14b8b9565aef" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:bdc25f3681f7b78572699569514036afe3c243bc3059d3942624e936ec93450e" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:fe5b32187cbc0c862ee201ad66c30cf218e5ed468ec8dc1cf49dec66e160cc4d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-musllinux_1_1_armv7l.whl", hash = "sha256:bc7aee6f634a6f4a95676fcb5d6559a2c2a390330098dba5e5a5f28a2e4ada30" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:235f45e5dbcccf6bd99f9f472858849f73d11120d76ea8707115415f8e5ebebf" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-win32.whl", hash = "sha256:6368900c2d3ef09b69cb0b913f9f8263b03786e5b2a387706c5afb66800efd51" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-win_amd64.whl", hash = "sha256:1e063337ef9e9820c77acc768546325ebe04ee38b08703244c1309cccc4f1bab" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp311-cp311-win_arm64.whl", hash = "sha256:6b99022f1d19bc32a4c2a0d544fc9a76e3be90f0b3f4af413f87d38749300e65" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-macosx_10_12_x86_64.whl", hash = "sha256:dd14041875d09cc0f9308e37a6f8b65f5585cf2598a53aa0123df8b129d481f8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-macosx_11_0_arm64.whl", hash = "sha256:d87c561733f66531dced0da6e864f44ebf89a8fba55f31407b00c2f7f9449593" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2f82865531efd18d6e07a04a17331af02cb7a651583c418df8266f17a63c6612" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2bfb5112df54209d820d7bf9317c7a6c9025ea52e49f46b6a2060104bba37de7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:64632ff9d614e5eecfb495796ad51b0ed98c453e447a76bcbeeb69615079fc7e" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-musllinux_1_1_aarch64.whl", hash = "sha256:f889f7a40498cc077332c7ab6b4608d296d852182211787d4f3ee377aaae66e8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-musllinux_1_1_armv7l.whl", hash = "sha256:de4b83bb311557e439b9e186f733f6c645b9417c84e2eb8203f3f820a4b988bf" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-musllinux_1_1_x86_64.whl", hash = "sha256:82f68293f055f51b51ea42fafc74b6aad03e70e191799430b90c13d643059ebb" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp311-pypy311_pp73-win_amd64.whl", hash = "sha256:329467cecfb529c925cf2bbd4d60d2c509bc2fb52a20c1045bf09bb70971a9c1" },
]

[[package]]
name = "pygments"
version = "2.19.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pygments/2.19.1/pygments-2.19.1.tar.gz", hash = "sha256:61c16d2a8576dc0649d9f39e089b5f02bcd27fba10d8fb4dcc28173f7a45151f" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pygments/2.19.1/pygments-2.19.1-py3-none-any.whl", hash = "sha256:9ea1544ad55cecf4b8242fab6dd35a93bbce657034b0611ee383099054ab6d8c" },
]

[[package]]
name = "pytest"
version = "8.4.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
    { name = "pygments" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pytest/8.4.0/pytest-8.4.0.tar.gz", hash = "sha256:14d920b48472ea0dbf68e45b96cd1ffda4705f33307dcc86c676c1b5104838a6" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pytest/8.4.0/pytest-8.4.0-py3-none-any.whl", hash = "sha256:f40f825768ad76c0977cbacdf1fd37c6f7a468e460ea6a0636078f8972d4517e" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/python-dateutil/2.9.0.post0/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/python-dateutil/2.9.0.post0/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427" },
]

[[package]]
name = "pywin32-ctypes"
version = "0.2.3"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pywin32-ctypes/0.2.3/pywin32-ctypes-0.2.3.tar.gz", hash = "sha256:d162dc04946d704503b2edc4d55f3dba5c1d539ead017afa00142c38b9885755" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/pywin32-ctypes/0.2.3/pywin32_ctypes-0.2.3-py3-none-any.whl", hash = "sha256:8a1513379d709975552d202d942d9837758905c8d01eb82b8bcc30918929e7b8" },
]

[[package]]
name = "readme-renderer"
version = "44.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "docutils" },
    { name = "nh3" },
    { name = "pygments" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/readme-renderer/44.0/readme_renderer-44.0.tar.gz", hash = "sha256:8712034eabbfa6805cacf1402b4eeb2a73028f72d1166d6f5cb7f9c047c5d1e1" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/readme-renderer/44.0/readme_renderer-44.0-py3-none-any.whl", hash = "sha256:2fbca89b81a08526aadf1357a8c2ae889ec05fb03f5da67f9769c9a592166151" },
]

[[package]]
name = "requests"
version = "2.32.4"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/requests/2.32.4/requests-2.32.4.tar.gz", hash = "sha256:27d0316682c8a29834d3264820024b62a36942083d52caf2f14c0591336d3422" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/requests/2.32.4/requests-2.32.4-py3-none-any.whl", hash = "sha256:27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c" },
]

[[package]]
name = "requests-toolbelt"
version = "1.0.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "requests" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/requests-toolbelt/1.0.0/requests-toolbelt-1.0.0.tar.gz", hash = "sha256:7681a0a3d047012b5bdc0ee37d7f8f07ebe76ab08caeccfc3921ce23c88d5bc6" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/requests-toolbelt/1.0.0/requests_toolbelt-1.0.0-py2.py3-none-any.whl", hash = "sha256:cccfdd665f0a24fcf4726e690f65639d272bb0637b9b92dfd91a5568ccf6bd06" },
]

[[package]]
name = "rfc3986"
version = "2.0.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/rfc3986/2.0.0/rfc3986-2.0.0.tar.gz", hash = "sha256:97aacf9dbd4bfd829baad6e6309fa6573aaf1be3f6fa735c8ab05e46cecb261c" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/rfc3986/2.0.0/rfc3986-2.0.0-py2.py3-none-any.whl", hash = "sha256:50b1502b60e289cb37883f3dfd34532b8873c7de9f49bb546641ce9cbd256ebd" },
]

[[package]]
name = "rich"
version = "14.0.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "markdown-it-py" },
    { name = "pygments" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/rich/14.0.0/rich-14.0.0.tar.gz", hash = "sha256:82f1bc23a6a21ebca4ae0c45af9bdbc492ed20231dcb63f297d6d1021a9d5725" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/rich/14.0.0/rich-14.0.0-py3-none-any.whl", hash = "sha256:1c9491e1951aac09caffd42f448ee3d04e58923ffe14993f6e83068dc395d7e0" },
]

[[package]]
name = "ruff"
version = "0.11.13"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13.tar.gz", hash = "sha256:26fa247dc68d1d4e72c179e08889a25ac0c7ba4d78aecfc835d49cbfd60bf514" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-linux_armv6l.whl", hash = "sha256:4bdfbf1240533f40042ec00c9e09a3aade6f8c10b6414cf11b519488d2635d46" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-macosx_10_12_x86_64.whl", hash = "sha256:aef9c9ed1b5ca28bb15c7eac83b8670cf3b20b478195bd49c8d756ba0a36cf48" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-macosx_11_0_arm64.whl", hash = "sha256:53b15a9dfdce029c842e9a5aebc3855e9ab7771395979ff85b7c1dedb53ddc2b" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ab153241400789138d13f362c43f7edecc0edfffce2afa6a68434000ecd8f69a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:6c51f93029d54a910d3d24f7dd0bb909e31b6cd989a5e4ac513f4eb41629f0dc" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1808b3ed53e1a777c2ef733aca9051dc9bf7c99b26ece15cb59a0320fbdbd629" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", hash = "sha256:d28ce58b5ecf0f43c1b71edffabe6ed7f245d5336b17805803312ec9bc665933" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:55e4bc3a77842da33c16d55b32c6cac1ec5fb0fbec9c8c513bdce76c4f922165" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:633bf2c6f35678c56ec73189ba6fa19ff1c5e4807a78bf60ef487b9dd272cc71" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4ffbc82d70424b275b089166310448051afdc6e914fdab90e08df66c43bb5ca9" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-musllinux_1_2_aarch64.whl", hash = "sha256:4a9ddd3ec62a9a89578c85842b836e4ac832d4a2e0bfaad3b02243f930ceafcc" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-musllinux_1_2_armv7l.whl", hash = "sha256:d237a496e0778d719efb05058c64d28b757c77824e04ffe8796c7436e26712b7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-musllinux_1_2_i686.whl", hash = "sha256:26816a218ca6ef02142343fd24c70f7cd8c5aa6c203bca284407adf675984432" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-musllinux_1_2_x86_64.whl", hash = "sha256:51c3f95abd9331dc5b87c47ac7f376db5616041173826dfd556cfe3d4977f492" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-win32.whl", hash = "sha256:96c27935418e4e8e77a26bb05962817f28b8ef3843a6c6cc49d8783b5507f250" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-win_amd64.whl", hash = "sha256:29c3189895a8a6a657b7af4e97d330c8a3afd2c9c8f46c81e2fc5a31866517e3" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ruff/0.11.13/ruff-0.11.13-py3-none-win_arm64.whl", hash = "sha256:b4385285e9179d608ff1d2fb9922062663c658605819a6876d8beef0c30b7f3b" },
]

[[package]]
name = "s3transfer"
version = "0.13.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "botocore" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/s3transfer/0.13.0/s3transfer-0.13.0.tar.gz", hash = "sha256:f5e6db74eb7776a37208001113ea7aa97695368242b364d73e91c981ac522177" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/s3transfer/0.13.0/s3transfer-0.13.0-py3-none-any.whl", hash = "sha256:0148ef34d6dd964d0d8cf4311b2b21c474693e57c2e069ec708ce043d2b527be" },
]

[[package]]
name = "secretstorage"
version = "3.3.3"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "cryptography" },
    { name = "jeepney" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/secretstorage/3.3.3/SecretStorage-3.3.3.tar.gz", hash = "sha256:2403533ef369eca6d2ba81718576c5e0f564d5cca1b58f73a8b23e7d4eeebd77" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/secretstorage/3.3.3/SecretStorage-3.3.3-py3-none-any.whl", hash = "sha256:f356e6628222568e3af06f2eba8df495efa13b3b63081dafd4f7d9a7b7bc9f99" },
]

[[package]]
name = "shapely"
version = "2.1.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1.tar.gz", hash = "sha256:500621967f2ffe9642454808009044c21e5b35db89ce69f8a2042c2ffd0e2772" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:587a1aa72bc858fab9b8c20427b5f6027b7cbc92743b8e2c73b9de55aa71c7a7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:9fa5c53b0791a4b998f9ad84aad456c988600757a96b0a05e14bba10cebaaaea" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:aabecd038841ab5310d23495253f01c2a82a3aedae5ab9ca489be214aa458aa7" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:586f6aee1edec04e16227517a866df3e9a2e43c1f635efc32978bb3dc9c63753" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:b9878b9e37ad26c72aada8de0c9cfe418d9e2ff36992a1693b7f65a075b28647" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:d9a531c48f289ba355e37b134e98e28c557ff13965d4653a5228d0f42a09aed0" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-win32.whl", hash = "sha256:4866de2673a971820c75c0167b1f1cd8fb76f2d641101c23d3ca021ad0449bab" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shapely/2.1.1/shapely-2.1.1-cp311-cp311-win_amd64.whl", hash = "sha256:20a9d79958b3d6c70d8a886b250047ea32ff40489d7abb47d01498c704557a93" },
]

[[package]]
name = "shortuuid"
version = "1.0.13"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shortuuid/1.0.13/shortuuid-1.0.13.tar.gz", hash = "sha256:3bb9cf07f606260584b1df46399c0b87dd84773e7b25912b7e391e30797c5e72" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/shortuuid/1.0.13/shortuuid-1.0.13-py3-none-any.whl", hash = "sha256:a482a497300b49b4953e15108a7913244e1bb0d41f9d332f5e9925dba33a3c5a" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/six/1.17.0/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/six/1.17.0/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274" },
]

[[package]]
name = "sqlalchemy"
version = "2.0.41"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "greenlet", marker = "platform_machine == 'AMD64' or platform_machine == 'WIN32' or platform_machine == 'aarch64' or platform_machine == 'amd64' or platform_machine == 'ppc64le' or platform_machine == 'win32' or platform_machine == 'x86_64'" },
    { name = "typing-extensions" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41.tar.gz", hash = "sha256:edba70118c4be3c2b1f90754d308d0b79c6fe2c0fdc52d8ddf603916f83f4db9" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:6375cd674fe82d7aa9816d1cb96ec592bac1726c11e0cafbf40eeee9a4516b5f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:9f8c9fdd15a55d9465e590a402f42082705d66b05afc3ffd2d2eb3c6ba919560" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:32f9dc8c44acdee06c8fc6440db9eae8b4af8b01e4b1aee7bdd7241c22edff4f" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:90c11ceb9a1f482c752a71f203a81858625d8df5746d787a4786bca4ffdf71c6" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:911cc493ebd60de5f285bcae0491a60b4f2a9f0f5c270edd1c4dbaef7a38fc04" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:03968a349db483936c249f4d9cd14ff2c296adfa1290b660ba6516f973139582" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-win32.whl", hash = "sha256:293cd444d82b18da48c9f71cd7005844dbbd06ca19be1ccf6779154439eec0b8" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-cp311-cp311-win_amd64.whl", hash = "sha256:3d3549fc3e40667ec7199033a4e40a2f669898a00a7b18a931d3efb4c7900504" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/sqlalchemy/2.0.41/sqlalchemy-2.0.41-py3-none-any.whl", hash = "sha256:57df5dc6fdb5ed1a88a1ed2195fd31927e705cad62dedd86b46972752a80f576" },
]

[[package]]
name = "toolz"
version = "1.0.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/toolz/1.0.0/toolz-1.0.0.tar.gz", hash = "sha256:2c86e3d9a04798ac556793bced838816296a2f085017664e4995cb40a1047a02" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/toolz/1.0.0/toolz-1.0.0-py3-none-any.whl", hash = "sha256:292c8f1c4e7516bf9086f8850935c799a874039c8bcf959d47b600e4c44a6236" },
]

[[package]]
name = "trove-classifiers"
version = "2025.5.9.12"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/trove-classifiers/2025.5.9.12/trove_classifiers-2025.5.9.12.tar.gz", hash = "sha256:7ca7c8a7a76e2cd314468c677c69d12cc2357711fcab4a60f87994c1589e5cb5" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/trove-classifiers/2025.5.9.12/trove_classifiers-2025.5.9.12-py3-none-any.whl", hash = "sha256:e381c05537adac78881c8fa345fd0e9970159f4e4a04fcc42cfd3129cca640ce" },
]

[[package]]
name = "twine"
version = "6.1.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "id" },
    { name = "keyring", marker = "platform_machine != 'ppc64le' and platform_machine != 's390x'" },
    { name = "packaging" },
    { name = "readme-renderer" },
    { name = "requests" },
    { name = "requests-toolbelt" },
    { name = "rfc3986" },
    { name = "rich" },
    { name = "urllib3" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/twine/6.1.0/twine-6.1.0.tar.gz", hash = "sha256:be324f6272eff91d07ee93f251edf232fc647935dd585ac003539b42404a8dbd" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/twine/6.1.0/twine-6.1.0-py3-none-any.whl", hash = "sha256:a47f973caf122930bf0fbbf17f80b83bc1602c9ce393c7845f289a3001dc5384" },
]

[[package]]
name = "types-awscrt"
version = "0.27.2"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-awscrt/0.27.2/types_awscrt-0.27.2.tar.gz", hash = "sha256:acd04f57119eb15626ab0ba9157fc24672421de56e7bd7b9f61681fedee44e91" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-awscrt/0.27.2/types_awscrt-0.27.2-py3-none-any.whl", hash = "sha256:49a045f25bbd5ad2865f314512afced933aed35ddbafc252e2268efa8a787e4e" },
]

[[package]]
name = "types-psycopg2"
version = "2.9.21.20250516"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-psycopg2/2.9.21.20250516/types_psycopg2-2.9.21.20250516.tar.gz", hash = "sha256:6721018279175cce10b9582202e2a2b4a0da667857ccf82a97691bdb5ecd610f" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-psycopg2/2.9.21.20250516/types_psycopg2-2.9.21.20250516-py3-none-any.whl", hash = "sha256:2a9212d1e5e507017b31486ce8147634d06b85d652769d7a2d91d53cb4edbd41" },
]

[[package]]
name = "types-python-dateutil"
version = "2.9.0.20250516"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-python-dateutil/2.9.0.20250516/types_python_dateutil-2.9.0.20250516.tar.gz", hash = "sha256:13e80d6c9c47df23ad773d54b2826bd52dbbb41be87c3f339381c1700ad21ee5" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-python-dateutil/2.9.0.20250516/types_python_dateutil-2.9.0.20250516-py3-none-any.whl", hash = "sha256:2b2b3f57f9c6a61fba26a9c0ffb9ea5681c9b83e69cd897c6b5f668d9c0cab93" },
]

[[package]]
name = "types-s3transfer"
version = "0.13.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-s3transfer/0.13.0/types_s3transfer-0.13.0.tar.gz", hash = "sha256:203dadcb9865c2f68fb44bc0440e1dc05b79197ba4a641c0976c26c9af75ef52" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-s3transfer/0.13.0/types_s3transfer-0.13.0-py3-none-any.whl", hash = "sha256:79c8375cbf48a64bff7654c02df1ec4b20d74f8c5672fc13e382f593ca5565b3" },
]

[[package]]
name = "types-setuptools"
version = "80.9.0.20250529"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-setuptools/80.9.0.20250529/types_setuptools-80.9.0.20250529.tar.gz", hash = "sha256:79e088ba0cba2186c8d6499cbd3e143abb142d28a44b042c28d3148b1e353c91" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-setuptools/80.9.0.20250529/types_setuptools-80.9.0.20250529-py3-none-any.whl", hash = "sha256:00dfcedd73e333a430e10db096e4d46af93faf9314f832f13b6bbe3d6757e95f" },
]

[[package]]
name = "types-shapely"
version = "2.1.0.20250512"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-shapely/2.1.0.20250512/types_shapely-2.1.0.20250512.tar.gz", hash = "sha256:15f08bdb7fa937dca0039510c6d8fda72e8464bfd43bd9ac59127a61a38ce7c8" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/types-shapely/2.1.0.20250512/types_shapely-2.1.0.20250512-py3-none-any.whl", hash = "sha256:e57b6d30758d717a218e682bc4e32c5a46e3c948008c79f0a653216ca93b65fb" },
]

[[package]]
name = "typing-extensions"
version = "4.14.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/typing-extensions/4.14.0/typing_extensions-4.14.0.tar.gz", hash = "sha256:8676b788e32f02ab42d9e7c61324048ae4c6d844a399eebace3d4979d75ceef4" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/typing-extensions/4.14.0/typing_extensions-4.14.0-py3-none-any.whl", hash = "sha256:a1514509136dd0b477638fc68d6a91497af5076466ad0fa6c338e44e359944af" },
]

[[package]]
name = "typing-inspection"
version = "0.4.1"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/typing-inspection/0.4.1/typing_inspection-0.4.1.tar.gz", hash = "sha256:6ae134cc0203c33377d43188d4064e9b357dba58cff3185f22924610e70a9d28" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/typing-inspection/0.4.1/typing_inspection-0.4.1-py3-none-any.whl", hash = "sha256:389055682238f53b04f7badcb49b989835495a96700ced5dab2d8feae4b26f51" },
]

[[package]]
name = "ulid-py"
version = "1.1.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ulid-py/1.1.0/ulid-py-1.1.0.tar.gz", hash = "sha256:dc6884be91558df077c3011b9fb0c87d1097cb8fc6534b11f310161afd5738f0" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/ulid-py/1.1.0/ulid_py-1.1.0-py2.py3-none-any.whl", hash = "sha256:b56a0f809ef90d6020b21b89a87a48edc7c03aea80e5ed5174172e82d76e3987" },
]

[[package]]
name = "urllib3"
version = "2.4.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/urllib3/2.4.0/urllib3-2.4.0.tar.gz", hash = "sha256:414bc6535b787febd7567804cc015fee39daab8ad86268f1310a9250697de466" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/urllib3/2.4.0/urllib3-2.4.0-py3-none-any.whl", hash = "sha256:4e16665048960a0900c702d4a66415956a584919c03361cac9f1df5c5dd7e813" },
]

[[package]]
name = "uuid-utils"
version = "0.11.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0.tar.gz", hash = "sha256:18cf2b7083da7f3cca0517647213129eb16d20d7ed0dd74b3f4f8bff2aa334ea" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl", hash = "sha256:094445ccd323bc5507e28e9d6d86b983513efcf19ab59c2dd75239cef765631a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-macosx_10_12_x86_64.whl", hash = "sha256:6430b53d343215f85269ffd74e1d1f4b25ae1031acf0ac24ff3d5721f6a06f48" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:be2e6e4318d23195887fa74fa1d64565a34f7127fdcf22918954981d79765f68" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:d37289ab72aa30b5550bfa64d91431c62c89e4969bdf989988aa97f918d5f803" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:1012595220f945fe09641f1365a8a06915bf432cac1b31ebd262944934a9b787" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:35cd3fc718a673e4516e87afb9325558969eca513aa734515b9031d1b651bbb1" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:ed325e0c40e0f59ae82b347f534df954b50cedf12bf60d025625538530e1965d" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:5c8b7cf201990ee3140956e541967bd556a7365ec738cb504b04187ad89c757a" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-musllinux_1_2_i686.whl", hash = "sha256:9966df55bed5d538ba2e9cc40115796480f437f9007727116ef99dc2f42bd5fa" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:cb04b6c604968424b7e6398d54debbdd5b771b39fc1e648c6eabf3f1dc20582e" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-win32.whl", hash = "sha256:18420eb3316bb514f09f2da15750ac135478c3a12a704e2c5fb59eab642bb255" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-win_amd64.whl", hash = "sha256:37c4805af61a7cce899597d34e7c3dd5cb6a8b4b93a90fbca3826b071ba544df" },
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/uuid-utils/0.11.0/uuid_utils-0.11.0-cp39-abi3-win_arm64.whl", hash = "sha256:4065cf17bbe97f6d8ccc7dc6a0bae7d28fd4797d7f32028a5abd979aeb7bf7c9" },
]

[[package]]
name = "win32-setctime"
version = "1.2.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/win32-setctime/1.2.0/win32_setctime-1.2.0.tar.gz", hash = "sha256:ae1fdf948f5640aae05c511ade119313fb6a30d7eabe25fef9764dca5873c4c0" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/win32-setctime/1.2.0/win32_setctime-1.2.0-py3-none-any.whl", hash = "sha256:95d644c4e708aba81dc3704a116d8cbc974d70b3bdb8be1d150e36be6e9d1390" },
]

[[package]]
name = "xmltodict"
version = "0.14.2"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/xmltodict/0.14.2/xmltodict-0.14.2.tar.gz", hash = "sha256:201e7c28bb210e374999d1dde6382923ab0ed1a8a5faeece48ab525b7810a553" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/xmltodict/0.14.2/xmltodict-0.14.2-py2.py3-none-any.whl", hash = "sha256:20cc7d723ed729276e808f26fb6b3599f786cbc37e06c65e192ba77c40f20aac" },
]

[[package]]
name = "zipp"
version = "3.23.0"
source = { registry = "https://repository.qindingtech.com/repository/pypi-public/simple/" }
sdist = { url = "https://repository.qindingtech.com/repository/pypi-public/packages/zipp/3.23.0/zipp-3.23.0.tar.gz", hash = "sha256:a07157588a12518c9d4034df3fbbee09c814741a33ff63c05fa29d26a2404166" }
wheels = [
    { url = "https://repository.qindingtech.com/repository/pypi-public/packages/zipp/3.23.0/zipp-3.23.0-py3-none-any.whl", hash = "sha256:071652d6115ed432f5ce1d34c336c0adfd6a884660d1e9712a256d3d3bd4b14e" },
]
