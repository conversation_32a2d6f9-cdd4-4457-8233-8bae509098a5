"""
对象存储容器
"""
from dependency_injector import containers, providers
from infra_database.database import SyncDatabase
from infra_database.uow.sync_sql_alchemy_unit_of_work import SyncSqlAlchemyUnitOfWork

from infra_object_storage.helper.object_storage_client import ObjectStorageClient
from infra_object_storage.repository.object_storage_repository import (
    ObjectStorageRepository,
)
from infra_object_storage.service.object_storage_service import ObjectStorageService


class ObjectStorageContainer(containers.DeclarativeContainer):
    # type: ignore
    database = providers.Singleton(SyncDatabase)

    # type: ignore
    uow = providers.Singleton(
        SyncSqlAlchemyUnitOfWork, engine=database.provided.engine)

    # type: ignore
    object_storage_client = providers.ThreadLocalSingleton(ObjectStorageClient)

    # type: ignore
    object_storage_repository = providers.ThreadLocalSingleton(
        ObjectStorageRepository, db_session=uow.provided.db_session
    )

    # type: ignore
    storage_service = providers.ThreadLocalSingleton(
        ObjectStorageService,
        object_storage_repository=object_storage_repository,
        object_storage_client=object_storage_client,
    )
