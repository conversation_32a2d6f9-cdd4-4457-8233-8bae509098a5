"""
对象存储服务测试
"""
from infra_object_storage.service.object_storage_service import ObjectStorageService


def test_service_initialization(object_storage_service):
    """测试服务初始化"""
    assert object_storage_service is not None
    assert isinstance(object_storage_service, ObjectStorageService)


def test_upload_file_basic(object_storage_service, prepare_transaction):
    """测试基本文件上传功能"""
    # 创建测试文件内容
    test_content = b"This is a test file content for unit testing"
    file_name = "test_upload.txt"

    # 上传文件
    file_info = object_storage_service.upload_file(
        file_name=file_name,
        file_blob=test_content,
        transaction=prepare_transaction,
        summary="Unit test file upload",
    )

    # 验证返回结果
    assert file_info is not None
    assert file_info.id is not None
    assert file_info.original_name == file_name


def test_upload_file_with_resource(object_storage_service, prepare_transaction, prepare_handler):
    """测试文件上传并关联资源"""
    # 创建测试文件内容
    test_content = b"This is a test file content for resource linking"
    file_name = "test_upload_with_resource.txt"

    # 上传文件并关联资源
    file_info = object_storage_service.upload_file_with_resource(
        file_name=file_name,
        file_blob=test_content,
        resource=prepare_handler,
        relationship="attachment",
        transaction=prepare_transaction,
        summary="Unit test file upload with resource",
    )

    # 验证返回结果
    assert file_info is not None
    assert file_info.id is not None
    assert file_info.original_name == file_name
#
#
# def test_get_download_url(prepare_storage_container):
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     url = service.get_file_url(
#         file_id="03af6d33-db15-4891-8d16-f5c2eae50b19")
#     print(url)
#
#
# def test_build_url(prepare_storage_container):
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     url = service.build_url(bucket_name='os-gov-smart-npc',
#                             object_name='20220813105106-me1t8o-upload_ext.jpg')
#     print(url)
#
#
# def test_unlink_file_and_resource(
#         prepare_storage_container, prepare_transaction, prepare_handler
# ):
#     uow = prepare_storage_container.uow()
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     with uow:
#         service.unlink_file_and_resource(
#             file_id="8c83fecb-5991-45c9-81a9-2863de008670",
#             resource=prepare_handler,
#             relationship="link",
#             transaction=prepare_transaction,
#         )
#
#
# def test_link_file_and_resource(
#         prepare_storage_container, prepare_transaction, prepare_handler
# ):
#     uow = prepare_storage_container.uow()
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     with uow:
#         relationship_id = service.link_file_and_resource(
#             file_id="8c83fecb-5991-45c9-81a9-2863de008670",
#             resource=prepare_handler,
#             relationship="link",
#             transaction=prepare_transaction,
#         )
#         print(relationship_id)
#
#
# def test_get_related_files(prepare_storage_container, prepare_handler):
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     all_files = service.get_resource_related_file_list(resource=prepare_handler)
#     assert len(all_files) == 2
#     bind_files = service.get_resource_related_file_list(
#         resource=prepare_handler, relationship="link"
#     )
#     assert len(bind_files) == 1
