# from infra_basic.resource.basic_resource import BasicResourceRelationship
# from infra_utility.datetime_helper import local_now
#
# from infra_object_storage.service.object_storage_service import ObjectStorageService
#
#
# def test_local_now():
#     t = local_now()
#     print(t)
#
#
# def test_delete_file(prepare_storage_container, prepare_handler):
#     uow = prepare_storage_container.uow()
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     trans = uow.log_transaction(handler=prepare_handler, action="delete file")
#     with uow:
#         service.delete_file(file_id='b0e1e5a0-0aca-4cd1-955d-b422d2303f6e', transaction=trans)
#
#
# def test_upload_file_with_resource(prepare_storage_container,
#                                    prepare_transaction, prepare_handler):
#     uow = prepare_storage_container.uow()
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     with uow:
#         with open("upload_file7.jpg", "rb") as f:
#             obj_data = f.read()
#             resource_relationship = BasicResourceRelationship(
#                 category=prepare_handler.res_category,
#                 id=prepare_handler.res_id, relationship="link"
#             )
#             file_info = service.upload_file_with_resource(
#                 file_name="upload_file777.jpg",
#                 file_blob=obj_data,
#                 resource=resource_relationship,
#                 relationship=resource_relationship.relationship,
#                 transaction=prepare_transaction,
#                 summary="res ext", )
#             print(file_info)
#
#
# def test_upload_file(prepare_storage_container, prepare_transaction,prepare_handler):
#     uow = prepare_storage_container.uow()
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     with uow:
#         uow.log_transaction(handler=prepare_handler, action="test_upload_file")
#         with open("upload_file7.jpg", "rb") as f:
#             obj_data = f.read()
#             file_info = service.upload_file(
#                 file_name="upload_cos666.jpg",
#                 file_blob=obj_data,
#                 transaction=prepare_transaction,
#                 summary="cos test",
#             )
#             print(file_info)
#
#
# def test_get_download_url(prepare_storage_container):
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     url = service.get_file_url(
#         file_id="03af6d33-db15-4891-8d16-f5c2eae50b19")
#     print(url)
#
#
# def test_build_url(prepare_storage_container):
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     url = service.build_url(bucket_name='os-gov-smart-npc',
#                             object_name='20220813105106-me1t8o-upload_ext.jpg')
#     print(url)
#
#
# def test_unlink_file_and_resource(
#         prepare_storage_container, prepare_transaction, prepare_handler
# ):
#     uow = prepare_storage_container.uow()
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     with uow:
#         service.unlink_file_and_resource(
#             file_id="8c83fecb-5991-45c9-81a9-2863de008670",
#             resource=prepare_handler,
#             relationship="link",
#             transaction=prepare_transaction,
#         )
#
#
# def test_link_file_and_resource(
#         prepare_storage_container, prepare_transaction, prepare_handler
# ):
#     uow = prepare_storage_container.uow()
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     with uow:
#         relationship_id = service.link_file_and_resource(
#             file_id="8c83fecb-5991-45c9-81a9-2863de008670",
#             resource=prepare_handler,
#             relationship="link",
#             transaction=prepare_transaction,
#         )
#         print(relationship_id)
#
#
# def test_get_related_files(prepare_storage_container, prepare_handler):
#     service: ObjectStorageService = prepare_storage_container.storage_service()
#     all_files = service.get_resource_related_file_list(resource=prepare_handler)
#     assert len(all_files) == 2
#     bind_files = service.get_resource_related_file_list(
#         resource=prepare_handler, relationship="link"
#     )
#     assert len(bind_files) == 1
