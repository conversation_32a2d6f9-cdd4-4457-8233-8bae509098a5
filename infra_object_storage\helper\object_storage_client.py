"""
对象存储客户端
"""

from threading import Lock
from typing import Dict, List, Union

from infra_object_storage.error import InvalidObjectStorageHelperError
from infra_object_storage.helper.cos_helper import <PERSON>sHelper
from infra_object_storage.helper.minio_helper import <PERSON><PERSON><PERSON>elper
from infra_object_storage.helper.object_storage_interface import ObjectStorageInterface
from infra_object_storage.helper.oss_helper import OssHelper
from infra_object_storage.helper.s3_helper import S3Helper
from infra_object_storage.settings import (
    CosAccount,
    MinioAccount,
    OssAccount,
    S3Account,
    StorageAccount,
)


class ObjectStorageClient:
    """
    对象存储客户端管理器
    根据账户ID动态创建并缓存对应的存储助手实例
    """

    def __init__(self, accounts: List[StorageAccount], default_account_id: str):
        """
        初始化对象存储客户端

        Args:
            accounts: 存储账户列表
            default_account_id: 默认使用的账户ID，必须存在于accounts中的某个账户的id字段
        """
        if not accounts:
            raise ValueError("Storage accounts list cannot be empty")

        # 构建账户索引，方便按ID查找
        self._accounts_map = {account.id: account for account in accounts}

        if default_account_id not in self._accounts_map:
            raise ValueError(
                f"Default account ID '{default_account_id}' not found in accounts"
            )

        self._helpers: Dict[str, ObjectStorageInterface] = (
            {}
        )  # 用于缓存已创建的 helper 实例
        self._lock = Lock()  # 初始化一个锁对象
        self._default_account_id = default_account_id

    def get_helper(self, account_id: str) -> ObjectStorageInterface:
        """
        根据账户ID获取对应的存储助手实例 (线程安全版本)。
        使用双重检查锁定模式确保性能和线程安全。
        """
        # 第一次检查（无锁），提高缓存命中时的性能
        if account_id in self._helpers:
            return self._helpers[account_id]

        # 缓存未命中，进入加锁的关键区域
        with self._lock:
            # 第二次检查（有锁），防止在等待锁期间其他线程已创建实例
            if account_id in self._helpers:
                return self._helpers[account_id]

            account = self._accounts_map.get(account_id)
            if not account:
                raise ValueError(
                    f"Storage account with id '{account_id}' not found in configuration."
                )

            if isinstance(account, S3Account):
                helper = S3Helper(account)
            elif isinstance(account, CosAccount):
                helper = CosHelper(account)
            elif isinstance(account, MinioAccount):
                helper = MinioHelper(account)
            elif isinstance(account, OssAccount):
                helper = OssHelper(account)
            else:
                raise InvalidObjectStorageHelperError(
                    f"Unsupported account provider: {getattr(account, 'provider', 'unknown')}"
                )

            # 确保返回的对象是 ObjectStorageInterface 类型
            if not isinstance(helper, ObjectStorageInterface):
                raise TypeError(f"Helper must be an instance of ObjectStorageInterface, got {type(helper)}")

            self._helpers[account_id] = helper
            return helper

    def get_default_helper(self) -> ObjectStorageInterface:
        """
        获取默认存储助手实例

        Returns:
            默认的ObjectStorageInterface实例
        """
        return self.get_helper(self._default_account_id)
