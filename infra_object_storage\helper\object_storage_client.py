"""
存储客户端
"""

from typing import Optional

from infra_object_storage.error import InvalidObjectStorageHelperError
from infra_object_storage.helper.object_storage_interface import (
    AccountInfo,
    ObjectStorageInterface,
)
from infra_object_storage.settings import (
    CosSetting,
    MinioSetting,
    OssSetting,
    S3Setting,
    StorageClientSetting,
)


class ObjectStorageClient(ObjectStorageInterface):
    """
    存储客户端
    """

    def get_account_info(self) -> AccountInfo:
        """返回账户信息"""
        return self.__client.get_account_info()

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获取目标篮子名称"""
        return self.__client.get_target_bucket_name(bucket_name=bucket_name)

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """篮子是否存在"""
        return self.__client.bucket_exists(bucket_name=bucket_name)

    def remove_bucket(self, bucket_name: Optional[str]):
        """删除篮子"""
        return self.__client.remove_bucket(bucket_name=bucket_name)

    def make_bucket(self, bucket_name: Optional[str]):
        """创建篮子"""
        return self.__client.make_bucket(bucket_name=bucket_name)

    def download_file(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """下载文件"""
        return self.__client.download_file(
            object_name=object_name, bucket_name=bucket_name
        )

    def upload_file(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """上传文件"""
        return self.__client.upload_file(
            object_name=object_name,
            object_data=object_data,
            bucket_name=bucket_name,
            is_public=is_public,
        )

    def get_file_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """获取文件下载链接"""
        return self.__client.get_file_url(
            object_name=object_name,
            bucket_name=bucket_name,
            expires_seconds=expires_seconds,
        )

    def get_object_policy(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        return self.__client.get_object_policy(object_name, bucket_name)

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        return self.__client.get_bucket_policy(bucket_name)

    def set_object_public_read(self, object_name: str, bucket_name: Optional[str] = None):
        return self.__client.set_object_public_read(object_name, bucket_name)

    def get_object_acl(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        return self.__client.get_object_acl(object_name, bucket_name)

    def __init__(
        self,
        settings: Optional[StorageClientSetting] = None,
        s3_settings: Optional[S3Setting] = None,
        minio_settings: Optional[MinioSetting] = None,
        cos_settings: Optional[CosSetting] = None,
        oss_settings: Optional[OssSetting] = None,
    ):
        """初始化"""
        if settings is None:
            settings = StorageClientSetting()
        self.__settings = settings
        self.__client = self.__init_client(
            s3_settings=s3_settings,
            minio_settings=minio_settings,
            cos_settings=cos_settings,
            oss_settings=oss_settings,
        )

    def __init_client(
        self,
        s3_settings: Optional[S3Setting] = None,
        minio_settings: Optional[MinioSetting] = None,
        cos_settings: Optional[CosSetting] = None,
        oss_settings: Optional[OssSetting] = None,
    ) -> ObjectStorageInterface:
        """初始化客户端"""
        if self.__settings.helper_type == "s3":
            from infra_object_storage.helper.s3_helper import (  # pylint: disable=import-outside-toplevel
                S3Helper,
            )

            return S3Helper(s3_settings=s3_settings)
        if self.__settings.helper_type == "minio":
            from infra_object_storage.helper.minio_helper import (  # pylint: disable=import-outside-toplevel
                MinioHelper,
            )

            return MinioHelper(minio_settings=minio_settings)
        if self.__settings.helper_type == "cos":
            from infra_object_storage.helper.cos_helper import (  # pylint: disable=import-outside-toplevel
                CosHelper,
            )

            return CosHelper(cos_settings=cos_settings)
        if self.__settings.helper_type == "oss":
            from infra_object_storage.helper.oss_helper import (  # pylint: disable=import-outside-toplevel
                OssHelper,
            )

            return OssHelper(oss_settings=oss_settings)
        raise InvalidObjectStorageHelperError(
            f"[{self.__settings.helper_type}] is not correct storage helper type"
        )
