"""
数据库基类模块

本模块提供了数据库连接和扩展注册的基础功能，包括：
1. 数据库连接管理
2. 数据库扩展注册
3. 表创建支持
4. 资源释放
5. 连接池配置验证
6. 连接字符串构建

这些功能通过抽象基类实现，为具体的数据库实现提供基础框架。
"""

import abc
from typing import Any, Optional

from infra_database.settings import DatabaseSettings


class Database(abc.ABC):
    """
    数据库基类

    提供数据库连接和扩展注册的基础功能，包括：
    1. 数据库连接管理
    2. 数据库扩展注册
    3. 表创建支持
    4. 资源释放
    5. 连接池配置验证
    6. 连接字符串构建

    这是一个抽象基类，具体的数据库实现需要继承此类并实现抽象方法。

    属性:
        setting: 数据库配置对象
        _engine: 数据库引擎对象，由具体实现类初始化
    """

    def __init__(self, setting: DatabaseSettings):
        """
        初始化数据库对象

        参数:
            setting: 数据库配置对象，包含连接信息和其他设置
        """
        self.setting = setting
        self._engine: Optional[Any] = None
        self._validate_pool_settings()

    def _validate_pool_settings(self) -> None:
        """
        验证连接池配置

        检查连接池相关的配置参数是否合法，包括：
        1. 连接池大小
        2. 最大溢出连接数
        3. 连接池超时时间
        4. 连接回收时间
        5. 端口号有效性

        异常:
            ValueError: 当连接池配置参数不合法时抛出
        """
        if not (1 <= self.setting.port <= 65535):
            raise ValueError("端口号必须在1~65535之间")
        if self.setting.pool_size < 1:
            raise ValueError("连接池大小必须大于0")
        if self.setting.max_overflow < 0:
            raise ValueError("最大溢出连接数不能为负数")
        if self.setting.pool_timeout < 0:
            raise ValueError("连接池超时时间不能为负数")
        if self.setting.pool_recycle < 0:
            raise ValueError("连接回收时间不能为负数")

    def _build_connection_string(self) -> str:
        """
        构建数据库连接字符串

        根据配置信息构建标准的数据库连接字符串。

        返回:
            str: 格式化的数据库连接字符串
        """
        return f"{self.setting.driver}://{self.setting.user}:{self.setting.password}@{self.setting.host}:{self.setting.port}/{self.setting.database}"

    @abc.abstractmethod
    def get_engine(self) -> Any:
        """
        获取数据库引擎

        返回:
            Any: 数据库引擎对象，具体类型由实现类决定
        """
        pass

    @abc.abstractmethod
    def dispose(self) -> None:
        """
        释放数据库连接资源

        此方法应该释放所有数据库相关的资源，包括连接池等。
        在应用程序关闭时调用此方法。
        """
        pass

    @abc.abstractmethod
    def create_tables(self, entity_class_list: list[type]) -> None:
        """
        根据传入的实体类列表，在数据库中创建对应的表

        此方法会根据SQLAlchemy实体类的定义创建相应的数据库表。
        如果表已存在，则不会进行任何操作。

        参数:
            entity_class_list: 需要建表的SQLAlchemy实体类列表
        """
        pass
