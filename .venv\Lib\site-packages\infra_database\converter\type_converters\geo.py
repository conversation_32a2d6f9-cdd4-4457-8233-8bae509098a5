"""
地理信息类型转换器

这个模块提供了地理信息类型和字符串之间的转换功能。
"""

import binascii
from typing import Optional

from infra_geo.geo_helper import str_is_wkb
from infra_geo.model.geo_point_model import GeoPointModel
from shapely import wkb, wkt
from shapely.errors import GEOSException
from shapely.geometry import Point


def geo_point_model_to_wkt(value: Optional[GeoPointModel]) -> Optional[str]:
    """将GeoPointModel对象转换为WKT字符串

    Args:
        value: 要转换的GeoPointModel对象

    Returns:
        Optional[str]: 转换后的WKT字符串，如果输入为None则返回None
    """
    if value is None:
        return None
    # 使用shapely的Point对象生成WKT字符串
    point = Point(value.longitude, value.latitude)
    return point.wkt


def wkt_or_wkb_to_geo_point_model(value: Optional[str]) -> Optional[GeoPointModel]:
    """将WKT或WKB字符串转换为GeoPointModel对象

    Args:
        value: 数据库字段值，WKT或WKB字符串

    Returns:
        Optional[GeoPointModel]: 转换后的GeoPointModel对象，如果输入为None或格式非法则返回None
    """
    if not value:  # 处理None和空字符串
        return None
    try:
        # 判断是否为十六进制编码的WKB格式
        if str_is_wkb(value):
            # WKB字符串通常是十六进制编码的，需要先用unhexlify解码成字节串，再用wkb.loads解析
            geometry = wkb.loads(binascii.unhexlify(value))
        else:
            # WKT直接用wkt.loads解析
            geometry = wkt.loads(value)

        # 确保解析出的几何体是Point类型
        if isinstance(geometry, Point):
            return GeoPointModel(longitude=geometry.x, latitude=geometry.y)

        # 如果是其他几何体类型（如LineString, Polygon等），则不进行转换，返回None
        return None
    except (ValueError, TypeError, binascii.Error, GEOSException):
        # 在解析过程中可能出现各种异常，例如格式错误、解码失败等
        # 在这种情况下，我们认为输入值无法转换为GeoPointModel，返回None
        return None
