"""
文件关系
"""

from typing import Optional

from infra_basic.resource.resource_interface import Resource
from infra_database.model.versioned.versioned_model import VersionedModel


class FileRelationshipModel(VersionedModel, Resource):
    """文件关系"""

    file_id: str
    resource_category: str
    resource_id: str
    relationship: str
    summary: Optional[str] = None

    @property
    def res_category(self) -> str:
        return self.resource_category

    @property
    def res_id(self) -> str:
        return self.resource_id
