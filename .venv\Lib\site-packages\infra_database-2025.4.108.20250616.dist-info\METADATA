Metadata-Version: 2.4
Name: infra_database
Version: 2025.4.108.20250616
Summary: Infra Database
Author-email: WebClerk <<EMAIL>>
Maintainer-email: WebClerk <<EMAIL>>
License-Expression: LicenseRef-Proprietary
License-File: LICENSE.md
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: <3.12,>=3.11
Requires-Dist: asyncpg>=0.30.0
Requires-Dist: infra-basic<2025.5.0,>=2025.4.1
Requires-Dist: infra-geo<2025.5.0,>=2025.4.1
Requires-Dist: lxml-stubs>=0.5.1
Requires-Dist: lxml>=5.3.1
Requires-Dist: numpy>=2.2.5
Requires-Dist: psycopg2>=2.9.10
Requires-Dist: pydantic>=2.0
Requires-Dist: shapely>=2.1.0
Requires-Dist: sqlalchemy>=2.0.40
Requires-Dist: types-psycopg2>=2.9.21.20250318
Requires-Dist: types-shapely>=2.1.0.20250512
Description-Content-Type: text/markdown

# Infra Database Code

## 介绍

本项目用于数据库交互，由以下模块组成

- infra_database

## 进度

异步数据库晚点实现，没这么急迫

## 使用方法

```bash
uv add infra_database
```

## 发布

1. 在`pyproject.toml`中修改`[project]`中的`version`为最新版本
2. 在`CHANGELOG.md`中追加版本修改内容
3. 执行`scripts\publish.bat`即可

## 进度

[x] adapter
[x] converter
[x] database
[x] entity
[x] model
[ ] repo
    [ ] sync
        [ ]
    [x] orm_helper
    [x] raw_sql_helper
    [x] versioned_helper
[x] settings
[x] uow
