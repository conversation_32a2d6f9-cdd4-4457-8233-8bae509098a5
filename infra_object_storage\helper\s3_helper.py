"""
S3帮助类
"""

from io import Bytes<PERSON>
from typing import Optional

import boto3
from boto3.s3.transfer import TransferConfig
from botocore.config import Config
from infra_object_storage.error import BucketNotExistedError, ObjectNotPublicReadError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
    ObjectStorageTypeEnum,
)
from infra_object_storage.helper.object_storage_interface import (
    AccountInfo,
    ObjectStorageInterface,
)
from infra_object_storage.settings import S3Setting


class S3Helper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """S3帮助类"""

    def get_account_info(self) -> AccountInfo:
        """获取账户信息"""
        return AccountInfo(
            type=ObjectStorageTypeEnum.S3,
            site=self.__settings.endpoint_url,
            unified_id=self.__settings.aws_access_key_id,
        )

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获取目标篮子"""
        return self._prepare_bucket_name(bucket_name=bucket_name)

    def __init__(self, s3_settings: Optional[S3Setting] = None):
        if s3_settings is None:
            s3_settings = S3Setting()
        self.__settings = s3_settings
        super().__init__(default_bucket=s3_settings.default_bucket)
        client_config = Config(
            connect_timeout=s3_settings.connect_timeout,
            read_timeout=s3_settings.read_timeout,
            s3={"addressing_style": "path"},
        )
        options = {
            "service_name": "s3",
            "config": client_config,
            "aws_access_key_id": s3_settings.aws_access_key_id,
            "aws_secret_access_key": s3_settings.aws_secret_access_key,
            "region_name": s3_settings.region_name,
            "endpoint_url": s3_settings.endpoint_url,
            "verify": s3_settings.verify,
        }
        self.__s3_client = boto3.client(**options)
        self.__s3_resource = boto3.resource(**options)

    def get_file_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """
        生成一个文件URL
        :param object_name: 对象名称
        :param bucket_name: 篮子名字不传入则从默认篮子里面捞
        :param expires_seconds: 签名URL的有效期（秒），默认1天。
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name=bucket_name)
        signed_url = self.__s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": target_bucket_name, "Key": object_name},
            ExpiresIn=expires_seconds,
        )
        return signed_url

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """
        判断篮子是否存在
        :param bucket_name:
        :return:
        """
        response = self.__s3_client.list_buckets()
        for bucket in response["Buckets"]:
            if bucket["Name"] == bucket_name:
                return True
        return False

    def remove_bucket(self, bucket_name: Optional[str]):
        """
        移除篮子
        :param bucket_name:
        :return:
        """
        response = self.__s3_client.list_buckets()
        is_succeed = False
        for bucket in response["Buckets"]:
            if bucket["Name"] == bucket_name:
                s3_bucket = self.__s3_resource.Bucket(bucket_name)
                s3_bucket.objects.all().delete()
                s3_bucket.delete()
                is_succeed = True
        if not is_succeed:
            raise BucketNotExistedError()

    def make_bucket(self, bucket_name: Optional[str]):
        """
        创建篮子
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if not self.bucket_exists(target_bucket_name):
            self.__s3_client.create_bucket(
                Bucket=target_bucket_name,
                CreateBucketConfiguration={
                    "LocationConstraint": self.__settings.region_name
                },
            )
            # 默认就是不许访问的
            # self.__s3_client.put_bucket_acl(
            #     Bucket=target_bucket_name,
            #     ACL='private'
            # )

    def download_file(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """
        下载文件
        :param object_name: 对象名
        :param bucket_name: 篮子名
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        s3_data = self.__s3_client.get_object(
            Bucket=target_bucket_name, Key=object_name
        )
        return s3_data.get("Body").read()

    def upload_file(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """
        上传文件
        :param object_name: 对象名
        :param object_data: 对象的数据
        :param bucket_name: 传入的篮子名，None的话则放入默认篮子
        :param is_public: 是否公开
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        transfer_config = TransferConfig(
            multipart_threshold=1024 * 1024 * 6,
        )
        self.__s3_client.upload_fileobj(
            Fileobj=BytesIO(object_data),
            Bucket=target_bucket_name,
            Key=object_name,
            Config=transfer_config,
        )
        if is_public:
            self.__s3_client.put_object_acl(
                ACL="public-read", Bucket=target_bucket_name, Key=object_name
            )

    def __is_object_public_read(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bool:
        """
        判断对象是否公开读取
        :param object_name:
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name=bucket_name)
        acl = self.__s3_client.get_object_acl(
            Bucket=target_bucket_name, Key=object_name
        )
        for grant in acl["Grants"]:
            if (
                grant["Grantee"]["Type"] == "Group"
                and grant["Grantee"].get("URI", "")
                == "http://acs.amazonaws.com/groups/global/AllUsers"
                and grant["Permission"] == "READ"
            ):
                return True
        return False


    def get_object_policy(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        raise NotImplementedError

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__s3_client.get_bucket_policy(Bucket=target_bucket_name)

    def set_object_public_read(self, object_name: str, bucket_name: Optional[str] = None):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__s3_client.put_object_acl(
            ACL="public-read", Bucket=target_bucket_name, Key=object_name
        )

    def get_object_acl(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__s3_client.get_object_acl(
            Bucket=target_bucket_name, Key=object_name
        )
