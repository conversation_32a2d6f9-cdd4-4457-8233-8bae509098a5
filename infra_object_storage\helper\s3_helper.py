"""
S3帮助类
"""

import json
from typing import List, Dict, TypedDict, Optional

from boto3 import Session
from boto3.s3.transfer import TransferConfig
from io import BytesIO
from typing import Iterable, List, Optional

from botocore.exceptions import ClientError
from botocore.config import Config

from infra_object_storage.error import BucketNotExistedError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
)
from infra_object_storage.helper.object_storage_interface import ObjectStorageInterface
from infra_object_storage.settings import S3Account


class S3Helper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """S3帮助类"""

    def get_account_info(self) -> S3Account:
        """获取账户信息"""
        return self.__account

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获取目标存储桶"""
        return self._prepare_bucket_name(bucket_name=bucket_name)

    def __init__(self, s3_account: S3Account):
        self.__account = s3_account
        super().__init__(default_bucket=s3_account.default_bucket)
        client_config = Config(
            connect_timeout=s3_account.connect_timeout,
            read_timeout=s3_account.read_timeout,
            s3={"addressing_style": "path"},
        )
        session = Session(
            aws_access_key_id=s3_account.aws_access_key_id,
            aws_secret_access_key=s3_account.aws_secret_access_key,
            region_name=s3_account.region_name
        )
        self.__s3_client = session.client(
            service_name="s3",
            config=client_config,
            endpoint_url=s3_account.endpoint_url,
            verify=s3_account.verify
        )
        self.__s3_resource = session.resource(
            service_name="s3",
            config=client_config,
            endpoint_url=s3_account.endpoint_url,
            verify=s3_account.verify
        )

    def get_object_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """
        生成一个文件URL
        :param object_name: 对象名称
        :param bucket_name: 存储桶名字不传入则从默认存储桶里面捞
        :param expires_seconds: 签名URL的有效期（秒），默认1天。
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name=bucket_name)
        signed_url = self.__s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": target_bucket_name, "Key": object_name},
            ExpiresIn=expires_seconds,
        )
        return signed_url

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """
        判断存储桶是否存在
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        try:
            self.__s3_client.head_bucket(Bucket=target_bucket_name)
            return True
        except ClientError as e:
            if e.response.get("Error", {}).get("Code") == "404":
                return False
            raise

    def remove_bucket(self, bucket_name: Optional[str]):
        """
        移除存储桶
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if not self.bucket_exists(target_bucket_name):
            raise BucketNotExistedError()

        # Note: S3 buckets must be empty before they can be deleted.
        # This implementation will delete all objects and versions in the bucket first.
        s3_bucket = self.__s3_resource.Bucket(target_bucket_name)
        s3_bucket.objects.all().delete()
        s3_bucket.object_versions.all().delete()
        s3_bucket.delete()

    def make_bucket(self, bucket_name: Optional[str]) -> None:
        """
        创建存储桶
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if not self.bucket_exists(target_bucket_name):
            # Only specify LocationConstraint if not in us-east-1
            if self.__account.region_name != "us-east-1":
                self.__s3_client.create_bucket(
                    Bucket=target_bucket_name,
                    CreateBucketConfiguration={
                        "LocationConstraint": self.__account.region_name
                    },
                )
            else:
                # For us-east-1, don't specify LocationConstraint
                self.__s3_client.create_bucket(Bucket=target_bucket_name)
            # 默认就是不许访问的
            # self.__s3_client.put_bucket_acl(
            #     Bucket=target_bucket_name,
            #     ACL='private'
            # )

    def download_object(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """
        下载文件
        :param object_name: 对象名
        :param bucket_name: 存储桶名
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        s3_data = self.__s3_client.get_object(
            Bucket=target_bucket_name, Key=object_name
        )
        body = s3_data.get("Body")
        if body is None:
            raise ValueError(f"Object not found: {target_bucket_name}/{object_name}")
        return body.read()

    def upload_object(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ) -> None:
        """
        上传文件
        :param object_name: 对象名
        :param object_data: 对象的数据
        :param bucket_name: 传入的存储桶名，None的话则放入默认存储桶
        :param is_public: 是否公开
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        transfer_config = TransferConfig(
            multipart_threshold=1024 * 1024 * 6,
        )
        self.__s3_client.upload_fileobj(
            Fileobj=BytesIO(object_data),
            Bucket=target_bucket_name,
            Key=object_name,
            Config=transfer_config,
        )
        if is_public:
            self.__s3_client.put_object_acl(
                ACL="public-read", Bucket=target_bucket_name, Key=object_name
            )

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        try:
            policy = self.__s3_client.get_bucket_policy(Bucket=target_bucket_name)
            return json.loads(policy["Policy"])
        except ClientError as e:
            if e.response.get("Error", {}).get("Code") == "NoSuchBucketPolicy":
                return {}
            raise

    def set_bucket_policy(self, policy: dict, bucket_name: Optional[str] = None):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__s3_client.put_bucket_policy(
            Bucket=target_bucket_name, Policy=json.dumps(policy)
        )

    def set_object_public_read(
        self, object_name: str, bucket_name: Optional[str] = None
    ):
        target_bucket_name = self._prepare_bucket_name(bucket_name)

        # 获取当前对象的ACL
        current_acl_response = self.__s3_client.get_object_acl(
            Bucket=target_bucket_name, Key=object_name
        )

        # 检查是否已经有public-read权限
        # S3的ACL响应中，Grants是一个列表，每个元素是一个字典，包含Grantee和Permission
        has_public_read = False
        if "Grants" in current_acl_response:
            for grant in current_acl_response["Grants"]:
                if (
                    "Grantee" in grant
                    and "Type" in grant["Grantee"]
                    and grant["Grantee"]["Type"] == "Group"
                    and "URI" in grant["Grantee"]
                    and grant["Grantee"]["URI"]
                    == "http://acs.amazonaws.com/groups/global/AllUsers"
                    and "Permission" in grant
                    and grant["Permission"] == "READ"
                ):
                    has_public_read = True
                    break

        # 如果没有public-read权限，则添加
        if not has_public_read:
            self.__s3_client.put_object_acl(
                ACL="public-read", Bucket=target_bucket_name, Key=object_name
            )

    def get_object_acl(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> dict:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        response = self.__s3_client.get_object_acl(
            Bucket=target_bucket_name, Key=object_name
        )
        return dict(response)

    def object_exists(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bool:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        try:
            self.__s3_client.head_object(Bucket=target_bucket_name, Key=object_name)
            return True
        except ClientError as e:
            if e.response.get("Error", {}).get("Code") == "404":
                return False
            raise

    def delete_object(self, object_name: str, bucket_name: Optional[str] = None):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__s3_client.delete_object(Bucket=target_bucket_name, Key=object_name)

    def list_objects(
        self, bucket_name: Optional[str] = None, prefix: Optional[str] = None
    ) -> Iterable[str]:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        paginator = self.__s3_client.get_paginator("list_objects_v2")
        pages = paginator.paginate(Bucket=target_bucket_name, Prefix=prefix or "")
        for page in pages:
            if "Contents" in page:
                for obj in page["Contents"]:
                    yield obj["Key"]

    def delete_objects(
        self, object_names: List[str], bucket_name: Optional[str] = None
    ):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        delete_keys = {
            "Objects": [{"Key": key} for key in object_names],
            "Quiet": False
        }
        self.__s3_client.delete_objects(Bucket=target_bucket_name, Delete=delete_keys)
