"""
同步版本化管理
"""

import copy
from datetime import datetime
from typing import Any, Dict, List, Optional, Type, cast

from infra_utility.credential_helper import generate_uuid_id
from infra_utility.string_helper import is_blank
from pydantic import BaseModel

from infra_database.converter.convert_helper import model_to_flat_dict
from infra_database.entity.versioned.versioned_entity import (
    GenericVersionedEntity,
    VersionedEntity,
)
from infra_database.error import (
    EntityLackIdError,
    EntityNotFoundError,
)
from infra_database.model.transaction_model import SystemTransactionModel
from infra_database.model.versioned.versioned_model import VersionedModel
from infra_database.repository.orm_helper import (
    entity_row_to_dict,
    prepare_update_data,
)
from infra_database.repository.sync.sync_basic_repository import SyncBasicBaseRepository
from infra_database.repository.versioned_helper import prepare_handler_info


class SyncVersionedRepository(SyncBasicBaseRepository):
    """
    同步版本化管理
    """

    # No __init__ needed, session is handled by SyncBaseRepository

    def __create_new_active_history(
        self,
        original_entity_cls: Type[GenericVersionedEntity],
        dict_data: Dict[str, Any],
    ) -> None:
        """
        根据实体数据创建一个新的、活跃的历史记录。
        这条记录的 `ceased_on` 为无限，表示它是当前版本。
        :param original_entity_cls: 原始实体类
        :param dict_data: 实体的数据字典
        """
        history_entity_cls = original_entity_cls.get_history_entity()
        insert_his_data = copy.deepcopy(dict_data)

        # commence_* 字段来自创建这个版本的事务
        insert_his_data["commence_transaction_id"] = dict_data["handler_id"]
        insert_his_data["commenced_on"] = dict_data["handled_on"]

        # 对于新的活跃历史记录，cease_* 字段留空，以使用数据库默认的 "infinity"
        insert_his_data.pop("cease_transaction_id", None)
        insert_his_data.pop("ceased_on", None)

        insert_his_data["history_id"] = generate_uuid_id()

        self._insert_entity_by_dict(entity_cls=history_entity_cls, dict_data=insert_his_data)

    def __terminate_active_history(
        self,
        original_entity_cls: Type[GenericVersionedEntity],
        entity_id: str,
        transaction: SystemTransactionModel,
    ) -> None:
        """
        终止指定实体的当前活跃历史记录。
        :param original_entity_cls: 原始实体类
        :param entity_id: 实体ID
        :param transaction: 导致终止的事务
        """
        history_entity_cls = original_entity_cls.get_history_entity()

        # 找到当前活跃的历史记录 (ceased_on is infinity)
        # 注意：在PostgreSQL中，'infinity'是一个特殊的timestamptz值
        active_history_record = (
            self.get_session()
            .query(history_entity_cls)
            .filter(history_entity_cls.id == entity_id, history_entity_cls.ceased_on == datetime.max)
            .one_or_none()
        )

        if active_history_record:
            # 使用当前事务信息来“终止”这条历史记录
            active_history_record.cease_transaction_id = transaction.id
            assert transaction.handled_on is not None
            active_history_record.ceased_on = transaction.handled_on
            self.get_session().flush()

    def __insert_versioned_entity(
        self,
        entity_cls: Type[GenericVersionedEntity],
        dict_data: Dict[str, Any],
        transaction: SystemTransactionModel,
    ) -> GenericVersionedEntity:
        # 准备需要必要的背景数据
        prepare_handler_info(
            entity_cls=entity_cls,
            dict_data=dict_data,
            transaction=transaction,
        )

        # 插入原始数据，并获得id
        inserted_entity = self._insert_entity_by_dict_get_entity(entity_cls=entity_cls, dict_data=dict_data)
        self.get_session().flush()

        # 从插入的实体中生成历史数据，以确保版本号等字段正确
        history_data = entity_row_to_dict(inserted_entity)

        # 创建第一条历史记录
        self.__create_new_active_history(
            original_entity_cls=entity_cls,
            dict_data=history_data,
        )

        return cast(GenericVersionedEntity, inserted_entity)

    def _insert_versioned_entity_by_dict(
        self,
        entity_cls: Type[GenericVersionedEntity],
        dict_data: Dict[str, Any],
        transaction: SystemTransactionModel,
    ) -> str:
        """
        根据字典信息来插入数据库数据
        :param entity_cls:
        :param dict_data:
        :param transaction:
        :return:
        """
        return cast(
            str,
            self.__insert_versioned_entity(
                entity_cls=entity_cls,
                dict_data=dict_data,
                transaction=transaction,
            ).id,
        )

    def _insert_versioned_entity_by_model(
        self,
        entity_cls: Type[GenericVersionedEntity],
        model_data: BaseModel,
        transaction: SystemTransactionModel,
    ) -> str:
        """
        根据模型来插入Versioned数据
        :param entity_cls:
        :param model_data:
        :param transaction:
        :return:
        """
        dict_data = model_to_flat_dict(model_data)
        return self._insert_versioned_entity_by_dict(
            entity_cls=entity_cls,
            dict_data=dict_data,
            transaction=transaction,
        )

    def _delete_versioned_entity_by_id(
        self,
        entity_cls: Type[VersionedEntity],
        entity_id: str,
        transaction: SystemTransactionModel,
    ) -> None:
        """
        根据类和id来删除对应的数据, 并创建历史记录
        :param entity_cls:
        :param entity_id:
        :param transaction:
        :return:
        """
        # 查找要删除的实体
        entity_to_delete = self.get_session().query(entity_cls).filter(entity_cls.id == entity_id).one_or_none()

        if entity_to_delete:
            # 终止当前活跃的历史记录
            self.__terminate_active_history(
                original_entity_cls=entity_cls,
                entity_id=entity_id,
                transaction=transaction,
            )

            # 现在删除实体
            self.get_session().delete(entity_to_delete)
        else:
            raise EntityNotFoundError(cls=entity_cls, params={"id": entity_id})

    def _update_versioned_entity_by_dict(
        self,
        entity_cls: Type[VersionedEntity],
        entity_id: str,
        version: int,
        update_data: Dict[str, Any],
        transaction: SystemTransactionModel,
        limited_col_list: Optional[List[str]] = None,
    ) -> None:
        """
        根据字典信息来更新数据库数据
        :param entity_cls:
        :param entity_id:
        :param version:
        :param update_data:
        :param transaction:
        :param limited_col_list:
        :return:
        """
        input_data = prepare_update_data(
            entity_cls=entity_cls,
            update_data=update_data,
            limited_col_list=limited_col_list,
        )
        prepare_handler_info(
            entity_cls=entity_cls,
            dict_data=input_data,
            transaction=transaction,
        )

        found_row = (
            self.get_session()
            .query(entity_cls)
            .filter(entity_cls.id == entity_id)
            .filter(entity_cls.version == version)
            .first()
        )
        if found_row:
            # 检查是否有实际的数据变更
            is_changed = False
            for key, value in input_data.items():
                if getattr(found_row, key) != value:
                    is_changed = True
                    break

            if not is_changed:
                return  # 如果没有变更，则不执行任何操作

            # 1. 终止当前版本的历史记录
            self.__terminate_active_history(
                original_entity_cls=entity_cls,
                entity_id=entity_id,
                transaction=transaction,
            )

            # 2. 更新主实体字段
            for key, value in input_data.items():
                setattr(found_row, key, value)
            self.get_session().flush()  # flush后，found_row.version 会自动递增

            # 3. 从更新后的实体创建新的活跃历史记录
            updated_history_data = entity_row_to_dict(found_row)
            self.__create_new_active_history(
                original_entity_cls=entity_cls,
                dict_data=updated_history_data,
            )
        else:
            raise EntityNotFoundError(cls=entity_cls, params={"id": entity_id, "version": version})

    def _update_versioned_entity_by_model(
        self,
        entity_cls: Type[VersionedEntity],
        update_model: VersionedModel,
        transaction: SystemTransactionModel,
        limited_col_list: Optional[List[str]] = None,
    ) -> None:
        """
        根据模型信息来更新数据库数据
        :param entity_cls:
        :param update_model:
        :param transaction:
        :param limited_col_list:
        :return:
        """
        if is_blank(update_model.id):
            raise EntityLackIdError()
        assert update_model.id is not None  # 类型断言，保证类型检查通过
        # 去掉系统字段
        update_dict = model_to_flat_dict(update_model)
        remove_cols = ["version", "handler_category", "handler_id", "handled_on"]
        for remove_col in remove_cols:
            if remove_col in update_dict:
                del update_dict[remove_col]

        return self._update_versioned_entity_by_dict(
            entity_cls=entity_cls,
            entity_id=update_model.id,
            version=update_model.version,
            update_data=update_dict,
            transaction=transaction,
            limited_col_list=limited_col_list,
        )
