# 脚本使用说明

## 发布脚本 (publish.bat)

### 使用前准备

在运行发布脚本前，需要设置以下环境变量：

```bash
# Windows
set TWINE_USERNAME=your_username
set TWINE_PASSWORD=your_password

# 或者在PowerShell中
$env:TWINE_USERNAME="your_username"
$env:TWINE_PASSWORD="your_password"

# Linux/macOS
export TWINE_USERNAME=your_username
export TWINE_PASSWORD=your_password
```

### 运行发布

```bash
scripts\publish.bat
```

### 安全注意事项

- 不要在脚本中硬编码用户名和密码
- 使用环境变量或密钥管理系统
- 在CI/CD环境中使用加密的环境变量

## 代码检查脚本 (lint.bat)

运行代码格式化和静态检查：

```bash
scripts\lint.bat
```

此脚本会依次执行：
- isort: 导入排序
- black: 代码格式化
- ruff: 代码检查
- mypy: 类型检查
