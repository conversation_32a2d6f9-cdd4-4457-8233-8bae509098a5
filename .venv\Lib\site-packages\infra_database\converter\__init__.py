"""
类型转换器模块

这个模块提供了在不同数据结构（如 Pydantic 模型、SQLAlchemy 实体和普通字典）之间
进行转换的实用函数。它旨在简化应用程序不同层之间的数据映射。

主要功能包括：
- 将 Pydantic 模型转换为适用于数据库操作的字典。
- 将 SQLAlchemy 实体转换为适用于 API 响应的字典或 Pydantic 模型。
- 自动处理枚举（Enum）类型与字符串之间的转换。
- 提供辅助函数以检查和提取模型与实体的字段类型信息。

公共接口:
    - model_to_flat_dict: 将 Pydantic 模型浅转换为字典。
    - entity_to_flat_dict: 将 SQLAlchemy 实体浅转换为字典。
    - get_entity_field_type_dict: 获取实体类的字段类型映射。
    - get_model_field_type_dict: 获取 Pydantic 模型的字段类型映射。
    - model_to_entity_dict: 将 Pydantic 模型实例转换为适用于实体的字典。
    - entity_to_model_dict: 将 SQLAlchemy 实体转换为适用于模型的字典。
    - prepare_dict_for_entity: 准备字典以创建 SQLAlchemy 实体。
    - prepare_dict_for_model: 准备字典以创建 Pydantic 模型。
"""

from .convert_helper import (
    entity_to_flat_dict,
    entity_to_model_dict,
    get_entity_field_type_dict,
    get_model_field_type_dict,
    model_to_entity_dict,
    model_to_flat_dict,
    prepare_dict_for_entity,
    prepare_dict_for_model,
)

__all__ = [
    "model_to_flat_dict",
    "entity_to_flat_dict",
    "get_entity_field_type_dict",
    "get_model_field_type_dict",
    "model_to_entity_dict",
    "entity_to_model_dict",
    "prepare_dict_for_entity",
    "prepare_dict_for_model",
]
