"""
异步数据库类
提供异步数据库连接和操作功能
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Dict, List, Optional, cast

from loguru import logger
from sqlalchemy import TextClause, text
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.pool import QueuePool

from infra_database.database.base_database import Database
from infra_database.error import EntityError
from infra_database.settings import (
    DatabaseSettings,
    DriverType,
    PostgreSQLDatabaseSettings,
)
from infra_database.uow.async_sql_alchemy_unit_of_work import AsyncSqlAlchemyUnitOfWork


class AsyncDatabase(Database):
    """
    异步数据库类

    提供异步数据库连接和操作功能
    """

    def __init__(self, setting: DatabaseSettings):
        """
        初始化异步数据库对象

        参数：
            setting: 数据库配置
        """
        super().__init__(setting)
        self._engine: Optional[AsyncEngine] = None

    def get_engine(self) -> AsyncEngine:
        """
        获取异步数据库引擎

        如果引擎不存在，则创建新的引擎

        返回：
            AsyncEngine: SQLAlchemy异步引擎对象
        """
        if self._engine is None:
            # 构建连接字符串
            connection_string = self._build_connection_string()

            # 获取引擎参数
            engine_kwargs = self._get_engine_kwargs()

            # 创建异步引擎
            self._engine = create_async_engine(connection_string, **engine_kwargs)

        return self._engine

    def get_session_factory(self) -> async_sessionmaker[AsyncSession]:
        """
        获取异步数据库会话工厂

        返回：
            async_sessionmaker[AsyncSession]: SQLAlchemy异步会话工厂对象
        """
        return async_sessionmaker(self.get_engine(), expire_on_commit=False)

    def get_uow(self) -> AsyncSqlAlchemyUnitOfWork:
        """
        获取异步工作单元

        返回:
            AsyncSqlAlchemyUnitOfWork: 异步工作单元实例
        """
        return AsyncSqlAlchemyUnitOfWork(
            async_session_factory=self.get_session_factory(),
            log_enabled=self.setting.uow_log_enabled,
        )

    def _get_engine_kwargs(self) -> Dict[str, Any]:
        """
        获取引擎参数

        返回：
            Dict[str, Any]: 引擎参数字典
        """
        # 基本参数
        kwargs = self._get_basic_engine_kwargs()

        # 数据库特有参数
        if isinstance(self.setting, PostgreSQLDatabaseSettings):
            self._add_postgres_specific_kwargs(kwargs)

        return kwargs

    def _get_basic_engine_kwargs(self) -> Dict[str, Any]:
        """
        获取基本引擎参数

        返回：
            Dict[str, Any]: 基本引擎参数字典
        """
        return {
            "echo": self.setting.echo,
            "pool_size": self.setting.pool_size,
            "max_overflow": self.setting.max_overflow,
            "pool_timeout": self.setting.pool_timeout,
            "pool_recycle": self.setting.pool_recycle,
            "pool_pre_ping": self.setting.pool_pre_ping,
            "connect_args": {
                "timeout": 30,  # 连接超时时间（秒）
                "command_timeout": 30,  # 命令超时时间（秒）
            },
        }

    def _add_postgres_specific_kwargs(self, kwargs: Dict[str, Any]) -> None:
        """
        添加PostgreSQL特有的引擎参数

        参数：
            kwargs: 引擎参数字典
        """
        # 检查是否使用 asyncpg 驱动
        is_asyncpg = DriverType.ASYNCPG in self.setting.driver

        # 将 setting 转换为 PostgreSQLDatabaseSettings 类型
        pg_setting = cast(PostgreSQLDatabaseSettings, self.setting)
        if pg_setting.enable_jit:
            self._add_jit_settings(kwargs, is_asyncpg)

        # 添加其他PostgreSQL特有参数
        self._add_postgres_connection_settings(kwargs, is_asyncpg)

    def _add_jit_settings(self, kwargs: Dict[str, Any], is_asyncpg: bool) -> None:
        """
        添加JIT设置

        参数：
            kwargs: 引擎参数字典
            is_asyncpg: 是否使用asyncpg驱动
        """
        if is_asyncpg:
            # asyncpg 驱动使用 command_timeout 和 server_settings
            if "connect_args" not in kwargs:
                kwargs["connect_args"] = {}

            if "server_settings" not in kwargs["connect_args"]:
                kwargs["connect_args"]["server_settings"] = {}

            # 设置 JIT 相关参数
            kwargs["connect_args"]["server_settings"].update(
                {
                    "jit": "on",
                    "jit_above_cost": "0",
                    "jit_inline_above_cost": "0",
                    "jit_optimize_above_cost": "0",
                }
            )
        else:
            # psycopg2 驱动使用 options
            if "connect_args" not in kwargs:
                kwargs["connect_args"] = {}

            kwargs["connect_args"][
                "options"
            ] = "-c jit=on -c jit_above_cost=0 -c jit_inline_above_cost=0 -c jit_optimize_above_cost=0"

    def _add_postgres_connection_settings(self, kwargs: Dict[str, Any], is_asyncpg: bool) -> None:
        """
        添加PostgreSQL连接设置

        参数：
            kwargs: 引擎参数字典
            is_asyncpg: 是否使用asyncpg驱动
        """
        for attr in ["application_name", "client_encoding"]:
            if hasattr(self.setting, attr) and getattr(self.setting, attr) is not None:
                if "connect_args" not in kwargs:
                    kwargs["connect_args"] = {}

                if is_asyncpg and attr == "client_encoding":
                    # asyncpg 使用 server_settings 设置 client_encoding
                    if "server_settings" not in kwargs["connect_args"]:
                        kwargs["connect_args"]["server_settings"] = {}
                    kwargs["connect_args"]["server_settings"]["client_encoding"] = getattr(self.setting, attr)
                else:
                    # 其他参数直接设置
                    kwargs["connect_args"][attr] = getattr(self.setting, attr)

    async def dispose(self) -> None:  # type: ignore[override]
        """
        释放异步数据库连接资源
        """
        if self._engine is not None:
            await self._engine.dispose()
            self._engine = None

    async def _retry_connection(self, max_retries: int = 3, retry_delay: float = 1.0) -> None:
        """
        重试连接

        参数：
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
        """
        for i in range(max_retries):
            try:
                async with self.get_engine().connect() as conn:
                    await conn.execute(text("SELECT 1"))
                return
            except Exception as e:
                if i == max_retries - 1:
                    raise e
                await asyncio.sleep(retry_delay)

    @asynccontextmanager
    async def transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """
        获取数据库事务的上下文管理器

        使用示例：
            asynchronous with db.transaction() as session:
                await session.execute(text("INSERT INTO table VALUES (1, 2)"))
                await session.commit()

        返回：
            AsyncSession: 数据库会话对象
        """
        async with AsyncSession(self.get_engine()) as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise

    async def execute_batch(self, statements: List[TextClause], params: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        批量执行SQL语句

        参数：
            statements: SQL语句列表
            params: 参数列表
        """
        if params is None:
            params = [{}] * len(statements)

        if len(statements) != len(params):
            raise ValueError("SQL语句数量与参数数量不匹配")

        async with self.transaction() as session:
            for i, statement in enumerate(statements):
                await session.execute(statement, params[i])

    def get_pool_status(self) -> Dict[str, Any]:
        """
        获取连接池状态

        返回：
            Dict[str, Any]: 连接池状态信息
        """
        if not self._engine:
            return {
                "status": "not_initialized",
                "pool": None,
            }

        pool = self._engine.pool
        if not isinstance(pool, QueuePool):
            return {
                "status": "unknown_pool_type",
                "pool": str(pool),
            }

        return {
            "status": "initialized",
            "pool": {
                "size": pool.size(),
                "checkedin": pool.checkedin(),
                "checkedout": pool.checkedout(),
                "overflow": pool.overflow(),
            },
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        返回：
            Dict[str, Any]: 健康状态
        """
        try:
            # 获取连接池状态
            pool_status = self.get_pool_status()

            # 测试连接
            async with self.get_engine().connect() as conn:
                await conn.execute(text("SELECT 1"))

            return {"status": "healthy", "pool_status": pool_status}
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "pool_status": self.get_pool_status(),
            }

    async def create_tables(self, entity_class_list: list[type]) -> None:  # type: ignore[override]
        """
        根据传入的实体类列表，在数据库中创建对应的表（异步实现）

        参数：
            entity_class_list: 需要建表的SQLAlchemy实体类列表
        """
        engine = self.get_engine()
        async with engine.begin() as conn:
            for entity_cls in entity_class_list:
                if not hasattr(entity_cls, "__table__"):
                    raise EntityError(f"{entity_cls} 不是有效的SQLAlchemy实体类")
                try:
                    # 使用 run_sync 在异步上下文中运行同步的 create 方法
                    await conn.run_sync(entity_cls.__table__.create, checkfirst=True)
                except Exception as e:
                    # 捕获其他建表异常并打印错误信息
                    logger.error(f"创建表失败: {entity_cls}，错误: {e}")
