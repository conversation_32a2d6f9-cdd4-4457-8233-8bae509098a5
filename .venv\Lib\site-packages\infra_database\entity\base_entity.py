"""
基础实体模块

该模块定义了所有SQLAlchemy实体的基类，提供了核心的序列化和反序列化功能。
"""

from typing import Any, Dict, Type, TypeVar

from sqlalchemy.orm import ColumnProperty, DeclarativeBase, class_mapper

# 定义泛型实体类型，用于类型注解
GenericSqlalchemyEntity = TypeVar("GenericSqlalchemyEntity", bound="SqlalchemyEntity")


class Base(DeclarativeBase):
    pass


class SqlalchemyEntity(Base):
    """
    SQLAlchemy 实体基类

    为所有实体提供通用的功能，包括：
    - `to_dict()`: 将实体实例安全地序列化为字典。
    - `from_dict()`: 从字典安全地反序列化为实体实例。

    该基类确保了只有在实体中明确定义的属性才能被序列化和反序列化，
    从而提高了代码的健壮性和安全性。
    """

    __abstract__ = True

    def to_dict(self) -> Dict[str, Any]:
        """
        将实体对象转换为字典。

        此方法会遍历实体所有通过 SQLAlchemy `Column` 定义的属性，
        并将其键值对填充到一个新的字典中。
        非 `Column` 属性（如 relationship）将被忽略。

        Returns:
            一个包含实体所有列属性的字典。
        """
        result: Dict[str, Any] = {}
        for prop in class_mapper(type(self)).iterate_properties:
            if isinstance(prop, ColumnProperty):
                result[str(prop.key)] = getattr(self, prop.key)
        return result

    @classmethod
    def from_dict(cls: Type[GenericSqlalchemyEntity], data: Dict[str, Any]) -> GenericSqlalchemyEntity:
        """
        从字典安全地创建并填充实体对象。

        此方法利用 SQLAlchemy 默认的 `__init__` 行为，该行为接受关键字参数。
        它会过滤输入字典，只保留那些在实体中被定义为列的键，
        以防止意外或恶意的属性注入（Mass Assignment Vulnerability）。

        Args:
            data: 包含实体属性的字典。

        Returns:
            一个填充了数据的新实体实例。
        """
        # 获取实体所有已映射的列名
        mapper = class_mapper(cls)
        allowed_keys = {p.key for p in mapper.iterate_properties if isinstance(p, ColumnProperty)}

        # 过滤输入字典，只保留实体中存在的列
        filtered_data = {k: v for k, v in data.items() if k in allowed_keys}

        # 使用过滤后的数据创建实体实例
        return cls(**filtered_data)
