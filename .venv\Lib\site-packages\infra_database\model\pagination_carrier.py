"""
分页数据载体模型

本模块定义了用于承载分页查询结果的通用数据结构。
"""

from typing import Generic, List, Optional, TypeVar

from infra_basic.model.base_plus_model import BasePlusModel

# 定义一个类型变量 T，用于表示分页数据中的项目类型
T = TypeVar("T")


class PaginationCarrier(BasePlusModel, Generic[T]):
    """
    分页数据载体 (Pagination Carrier)

    一个通用的、可承载任何类型 `T` 的列表数据的分页模型。
    通常用于API响应，向前端返回结构化的分页数据。

    属性:
        search_text (Optional[str]): 用于过滤的搜索文本。默认为空字符串。
        total_count (int): 分页前的总记录数。
        filter_count (int): 应用过滤条件后的记录数。
        page_index (int): 当前页码 (0-based)。
        page_size (int): 每页的记录数。
        draw (Optional[int]): DataTables 等前端库使用的请求绘制计数器，用于确保响应的顺序正确。默认为 None。
        data (List[T]): 当前页的数据列表，列表中的每个元素都是 `T` 类型的实例。默认为空列表。
    """

    search_text: Optional[str] = ""
    total_count: int
    filter_count: int
    page_index: int
    page_size: int
    draw: Optional[int] = None
    data: List[T] = []
