"""
时间的无穷表示
"""

from datetime import datetime
from typing import Any

from infra_utility.datetime_helper import datetime_to_utc
from psycopg2 import extensions


class InfinityDateTimeAdapter:
    """
    为 psycopg2 提供的 datetime 对象适配器，用于处理无穷大/无穷小时间。

    这个适配器在将 Python 的 datetime 对象写入 PostgreSQL 数据库时被调用。
    它的主要功能是：
    - 将 `datetime.max` 转换为 PostgreSQL 的 `'infinity'`::timestamp。
    - 将 `datetime.min` 转换为 PostgreSQL 的 `'-infinity'`::timestamp。
    - 对于其他常规的 datetime 对象，则委托给 psycopg2 默认的 `TimestampFromPy` 适配器处理。

    注意：此适配器仅用于写入（Python -> DB）方向。读取（DB -> Python）方向由
    psycopg2 驱动自动完成，它能将数据库的 'infinity' 和 '-infinity'
    正确地转换回 `datetime.max` 和 `datetime.min`。
    """

    def __init__(self, wrapped: Any) -> None:
        """
        初始化适配器。

        :param wrapped: 被包装的 datetime 对象，由 psycopg2 传入。
        """
        self.wrapped = wrapped

    def getquoted(self) -> Any:
        """
        生成用于 SQL 查询的、带引号的字符串表示。

        这是 psycopg2 适配器协议的核心方法。它返回一个字节串，该字节串将
        直接嵌入到最终的 SQL 命令中。

        :return: 表示时间的字节串，例如 b"'infinity'::timestamp" 或 b"'2023-01-01'::timestamp"。
                 如果 self.wrapped 为 None，则返回 b"NULL"。
        """
        if self.wrapped is None:
            return b"NULL"

        # 为了安全比较，将输入时间统一转换为 UTC 时区的 naive datetime 对象。
        # datetime.max 和 datetime.min 都是 naive 的，直接与 aware 时间比较会出错。
        check_time = datetime_to_utc(self.wrapped).replace(tzinfo=None)

        # 检查是否为无穷大时间
        if check_time >= datetime.max:
            return b"'infinity'::timestamp"

        # 检查是否为无穷小时间
        if check_time <= datetime.min:
            return b"'-infinity'::timestamp"

        # 对于常规时间，使用 psycopg2 内置的适配器来获取标准的带引号格式
        return extensions.TimestampFromPy(self.wrapped).getquoted()
