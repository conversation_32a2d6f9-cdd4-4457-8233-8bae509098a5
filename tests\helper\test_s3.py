import os
import pytest
from infra_object_storage.error import ObjectNotPublicReadError


@pytest.mark.parametrize("config_path", ["s3.toml"], indirect=True)
def test_s3_upload(object_storage_client, config_path):
    with open("tests/helper/s3.png", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(object_name="upload.png", object_data=obj_data)


@pytest.mark.parametrize("config_path", ["s3.toml"], indirect=True)
def test_s3_public_upload(object_storage_client, config_path):
    with open("tests/helper/s3-public.jpg", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(object_name="s3-public.jpg", object_data=obj_data, is_public=True)


@pytest.mark.parametrize("config_path", ["s3.toml"], indirect=True)
def test_s3_url(object_storage_client, config_path):
    # test public url
    url = object_storage_client.get_file_url("s3-public.jpg")
    print(url)
    assert url is not None
    url = object_storage_client.get_file_url("upload.png")
    print(url)
    assert url is not None


@pytest.mark.parametrize("config_path", ["s3.toml"], indirect=True)
def test_s3_download(object_storage_client, config_path):
    obj_blob = object_storage_client.download_file(object_name="upload.png")
    with open("tests/helper/s3_download.png", "wb") as f:
        f.write(obj_blob)


@pytest.mark.parametrize("config_path", ["s3.toml"], indirect=True)
def test_s3_bucket(object_storage_client, config_path):
    test_bucket_name = f"s3-py-{os.urandom(4).hex()}"
    object_storage_client.make_bucket(test_bucket_name)
    assert object_storage_client.bucket_exists(test_bucket_name) is True
    object_storage_client.remove_bucket(test_bucket_name)
    assert object_storage_client.bucket_exists(test_bucket_name) is False



