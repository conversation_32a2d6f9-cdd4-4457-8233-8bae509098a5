import json
import pytest
from infra_object_storage.error import ObjectNotPublicReadError

@pytest.mark.parametrize("config_path", ["minio.toml"], indirect=True)
def test_minio_download(object_storage_client, config_path):
    obj_blob = object_storage_client.download_file(
        object_name="minio1.png"
    )
    with open("tests/helper/minio.png", "wb") as f:
        f.write(obj_blob)


@pytest.mark.parametrize("config_path", ["minio.toml"], indirect=True)
def test_minio_upload(object_storage_client, config_path):
    with open("tests/helper/minio.png", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(object_name="upload.png", object_data=obj_data)


@pytest.mark.parametrize("config_path", ["minio.toml"], indirect=True)
def test_minio_bucket(object_storage_client, config_path):
    test_bucket_name = "minio-py"
    object_storage_client.make_bucket(test_bucket_name)
    assert object_storage_client.bucket_exists(test_bucket_name) is True
    policy = object_storage_client.get_bucket_policy(test_bucket_name)
    print(policy)
    # object_storage_client.remove_bucket(test_bucket_name)
    # assert object_storage_client.bucket_exists(test_bucket_name) is False


@pytest.mark.parametrize("config_path", ["minio.toml"], indirect=True)
def test_minio_url(object_storage_client, config_path):
    # test public url
    url = object_storage_client.get_file_url("minio_public.png")
    print(url)
    assert url is not None
    url = object_storage_client.get_file_url("minio1.png")
    print(url)
    assert url is not None


@pytest.mark.parametrize("config_path", ["minio.toml"], indirect=True)
def test_minio_set_object_public_read(object_storage_client, config_path):
    object_storage_client.set_object_public_read(object_name="20220425171835-mcwfc5-002.jpg")
    object_storage_client.set_object_public_read(object_name="minio1.png")
    object_storage_client.set_object_public_read(object_name="upload.png")
    object_storage_client.set_object_public_read(object_name="minio_public.png")


def test_json_to_dict():
    json_data = """{
            "Effect": "Allow",
            "Principal": {
                "AWS": [
                    "*"
                ]
            },
            "Action": [
                "s3:GetObject"
            ],
            "Resource": [
                "arn:aws:s3:::minio-test/minio1.png",
                "arn:aws:s3:::minio-test/20220430204458-1hjn27-upload_file.jpg",
                "arn:aws:s3:::minio-test/minio_public.png"
                ]
        }"""
    dict_data = json.loads(json_data)
    print(dict_data)


@pytest.mark.parametrize("config_path", ["minio.toml"], indirect=True)
def test_minio_public_upload(object_storage_client, config_path):
    with open("tests/helper/minio_public.png", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(object_name="minio_public.png", object_data=obj_data, is_public=True)
