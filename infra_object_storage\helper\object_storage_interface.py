"""
对象存储
"""

from abc import ABC, abstractmethod
from typing import Optional

from infra_basic.model.base_plus_model import BasePlusModel


class AccountInfo(BasePlusModel):
    """账号信息"""

    type: str
    site: str
    unified_id: str


class ObjectStorageInterface(ABC):
    """
    对象存储的接口
    """

    @abstractmethod
    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """
        获取目标篮子的名称
        :param bucket_name:
        :return:
        """

    @abstractmethod
    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """
        检查篮子是否存在
        :param bucket_name: 待检查篮子名
        :return:
        """

    @abstractmethod
    def remove_bucket(self, bucket_name: Optional[str]):
        """
        移除篮子
        :param bucket_name: 待移除篮子名
        :return:
        """

    @abstractmethod
    def make_bucket(self, bucket_name: Optional[str]):
        """
        创建篮子
        :param bucket_name: 篮子名
        :return:
        """

    @abstractmethod
    def download_file(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """
        下载文件
        :param object_name: 对象名称
        :param bucket_name: 篮子名
        :return:
        """

    @abstractmethod
    def upload_file(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """
        上传文件
        :param object_name: 文件名
        :param object_data: IO对象
        :param bucket_name: 篮子名
        :param is_public: 是否公开
        :return:
        """

    @abstractmethod
    def get_file_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """
        生成一个文件URL
        :param object_name: 对象名称
        :param bucket_name: 篮子名称
        :param expires_seconds: 签名URL的有效期（秒），默认1天。
        :return:
        """

    @abstractmethod
    def get_account_info(self) -> AccountInfo:
        """
        返回账号信息
        """

    @abstractmethod
    def get_object_policy(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        """
        获取对象的策略
        """

    @abstractmethod
    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        """
        获取篮子的策略
        """

    @abstractmethod
    def set_object_public_read(self, object_name: str, bucket_name: Optional[str] = None):
        """
        将对象设置为公开可读
        """

    @abstractmethod
    def get_object_acl(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        """
        获取对象的ACL
        """
