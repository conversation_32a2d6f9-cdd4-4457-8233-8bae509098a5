"""
对象存储接口
"""

from abc import ABC, abstractmethod
from typing import Iterable, List, Optional, TypeVar, Union

from infra_object_storage.settings import (
    CosAccount,
    MinioAccount,
    OssAccount,
    S3Account,
)

# 定义一个类型变量，表示所有可能的存储账户类型
StorageAccountType = TypeVar(
    "StorageAccountType", CosAccount, MinioAccount, OssAccount, S3Account
)


class ObjectStorageInterface(ABC):
    """
    对象存储的统一接口定义。

    该抽象基类 (ABC) 定义了所有对象存储实现必须遵循的通用方法，
    以确保不同服务（如 COS, Minio, S3）之间行为的一致性。
    """

    @abstractmethod
    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """
        获取最终要操作的存储桶名称。

        如果 `bucket_name` 为 None，通常会返回一个默认的存储桶名称。

        :param bucket_name: 用户指定的存储桶名称，可以为 None。
        :return: 最终确定的存储桶名称。
        """

    @abstractmethod
    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """
        检查指定的存储桶是否存在。

        :param bucket_name: 待检查的存储桶名称。
        :return: 如果存储桶存在，返回 True；否则返回 False。
        """

    @abstractmethod
    def remove_bucket(self, bucket_name: Optional[str]):
        """
        移除一个空的存储桶。

        注意：通常只有在存储桶为空时才能成功删除。

        :param bucket_name: 待移除的存储桶名称。
        """

    @abstractmethod
    def make_bucket(self, bucket_name: Optional[str]):
        """
        创建一个新的存储桶。

        :param bucket_name: 要创建的存储桶名称。
        """

    @abstractmethod
    def download_object(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """
        从存储桶下载一个对象，并将其内容作为字节返回。

        注意：此方法会将整个文件加载到内存中。

        :param object_name: 要下载的对象的名称。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        :return: 对象的字节数据。
        """

    @abstractmethod
    def upload_object(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """
        将字节数据上传为一个新的对象。

        注意：此方法会将整个文件加载到内存中。

        :param object_name: 要创建的对象的名称。
        :param object_data: 要上传的字节数据。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        :param is_public: 如果为 True，则将对象设置为公开可读。
        """

    @abstractmethod
    def get_object_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """
        为存储桶中的对象生成一个可访问的 URL。

        对于私有对象，这通常是一个预签名的 URL，具有指定的有效期。
        对于公共对象，可能返回一个永久的公共 URL。

        :param object_name: 对象的名称。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        :param expires_seconds: 预签名 URL 的有效期（秒）。默认为 1 天。
        :return: 可访问的文件 URL。
        """

    @abstractmethod
    def get_account_info(
        self,
    ) -> Union[CosAccount, MinioAccount, OssAccount, S3Account]:
        """
        返回当前对象存储实例所使用的账户配置信息。

        :return: 包含账户凭证和配置的数据模型实例。
        """

    @abstractmethod
    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        """
        获取指定存储桶的访问策略。

        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        :return: 一个表示存储桶策略的字典。
        """

    @abstractmethod
    def set_bucket_policy(self, policy: dict, bucket_name: Optional[str] = None):
        """
        设置指定存储桶的访问策略。

        :param policy: 一个表示存储桶策略的字典。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        """

    @abstractmethod
    def set_object_public_read(
        self, object_name: str, bucket_name: Optional[str] = None
    ):
        """
        将指定对象设置为公开可读。

        这通常通过修改对象的 ACL (访问控制列表) 或策略来实现。

        :param object_name: 对象的名称。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        """

    @abstractmethod
    def get_object_acl(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> dict:
        """
        获取指定对象的访问控制列表 (ACL)。

        :param object_name: 对象的名称。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        :return: 一个表示对象 ACL 的字典。
        """

    @abstractmethod
    def object_exists(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bool:
        """
        检查指定的对象是否存在于存储桶中。

        :param object_name: 待检查的对象的名称。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        :return: 如果对象存在，返回 True；否则返回 False。
        """

    @abstractmethod
    def delete_object(self, object_name: str, bucket_name: Optional[str] = None):
        """
        从存储桶中删除指定的对象。

        :param object_name: 要删除的对象的名称。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        """

    @abstractmethod
    def list_objects(
        self, bucket_name: Optional[str] = None, prefix: Optional[str] = None
    ) -> Iterable[str]:
        """
        列出存储桶中的对象。

        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        :param prefix: 只列出具有指定前缀的对象。
        :return: 一个迭代器，产生对象的名称。
        """

    @abstractmethod
    def delete_objects(
        self, object_names: List[str], bucket_name: Optional[str] = None
    ):
        """
        从存储桶中批量删除对象。

        :param object_names: 要删除的对象名称列表。
        :param bucket_name: 存储桶名称。如果为 None，则使用默认存储桶。
        """
