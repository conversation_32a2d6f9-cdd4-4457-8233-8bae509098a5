"""
原生 SQL 辅助函数模块

本模块提供用于构建和执行原生SQL查询的工具。
为了从根本上防止SQL注入，本模块中的所有函数都应遵循以下原则：
- 不直接拼接SQL字符串。
- 返回 sqlalchemy.text() 对象和对应的参数字典，以实现参数化查询。

这些工具主要用于处理复杂的、需要手写SQL进行性能优化的场景。
"""

from typing import Any, Dict, List, Optional, Tuple

from loguru import logger
from sqlalchemy import text
from sqlalchemy.engine import Result
from sqlalchemy.sql.elements import TextClause

from infra_database.model.database_query_model import (
    FilterCondition,
    OrderCondition,
    QueryCondition,
)


def cursor_result_to_list(execute_result: Result[Any]) -> List[Dict[str, Any]]:
    """
    将SqlAlchemy的Result结果集转换为字典列表

    参数:
        execute_result: Result[Any] - SQLAlchemy执行结果

    返回:
        List[Dict[str, Any]]: 转换后的字典列表
    """
    try:
        # SQLAlchemy 2.0+ 的 Result 对象可以直接通过 .mappings() 方法转换为字典列表
        # .mappings() 返回一个迭代器，每个元素都是一个类似字典的 RowMapping 对象
        return [dict(row) for row in execute_result.mappings()]
    except Exception as e:
        logger.error(f"转换结果集时发生错误: {str(e)}")
        return []


def build_where_clause(
    params: List[FilterCondition],
) -> Tuple[str, Dict[str, Any]]:
    """
    根据过滤条件列表，安全地构建where子句的SQL片段和参数
    (不含where关键字)

    参数:
        params: List[FilterCondition] - 过滤条件对象列表

    返回:
        Tuple[str, Dict[str, Any]]: (where子句SQL片段, 参数字典)
    """
    if not params:
        return "1=1", {}

    sql_parts = []
    query_params = {}
    for i, cond in enumerate(params):
        col = cond.column_name
        op = cond.operator or "="
        val = cond.value
        param_name = f"{col}_{i}"

        # 白名单验证，防止SQL注入
        if op.lower() not in ["=", "!=", ">", "<", ">=", "<=", "in", "like"]:
            logger.warning(f"不支持的操作符: {op}，跳过该条件")
            continue

        sql_parts.append(f"{col} {op} :{param_name}")
        query_params[param_name] = val

    if not sql_parts:
        return "1=1", {}

    return " AND ".join(sql_parts), query_params


def build_pagination_clause(page_size: int, page_index: int) -> str:
    """
    获取分页sql后缀，页码从0开始

    参数:
        page_size (int): 每页数据量
        page_index (int): 页码，从0开始

    返回:
        str: 分页SQL片段，例如 "LIMIT 10 OFFSET 0"

    注意:
        此函数返回的是安全的SQL片段，因为它只处理整数。
        PostgreSQL/SQLite 使用 LIMIT/OFFSET。
    """
    if not isinstance(page_size, int):
        raise TypeError(f"page_size参数类型错误，期望int，实际为{type(page_size).__name__}")
    if not isinstance(page_index, int):
        raise TypeError(f"page_index参数类型错误，期望int，实际为{type(page_index).__name__}")

    offset = page_index * page_size
    return f"LIMIT {page_size} OFFSET {offset}"


def build_bulk_insert_sql(
    table_name: str,
    dict_list: List[Dict[str, Any]],
) -> Tuple[TextClause, List[Dict[str, Any]]]:
    """
    安全地构建批量插入的SQLAlchemy text对象和参数列表

    参数:
        table_name: 数据库表名
        dict_list: 待插入的数据字典列表

    返回:
        tuple[TextClause, List[Dict[str, Any]]]: (SQLAlchemy text 对象, 参数列表)
    """
    if not dict_list:
        return text(""), []

    # 所有字典应有相同的键，使用第一个作为参考
    column_names = sorted(dict_list[0].keys())
    fields_sql = ", ".join(column_names)
    # values子句中的键必须与字典中的键匹配
    values_sql = ", ".join(f":{col}" for col in column_names)

    sql_str = f"INSERT INTO {table_name} ({fields_sql}) VALUES ({values_sql}) RETURNING id"

    return text(sql_str), dict_list


def build_full_text_search_clause(
    search_segment_list: List[str], filter_columns: Optional[List[str]]
) -> Tuple[str, Dict[str, Any]]:
    """
    安全地构建文本搜索过滤条件的SQL片段和参数

    Args:
        search_segment_list: 搜索文本分词列表
        filter_columns: 可过滤的列名列表

    Returns:
        tuple: (SQL片段, 参数字典)
    """
    if not search_segment_list or not filter_columns:
        return "", {}

    filter_conditions = []
    params = {}
    param_idx = 0

    for segment in search_segment_list:
        segment_conditions = []
        for col in filter_columns:
            param_key = f"s_{param_idx}"
            param_idx += 1
            segment_conditions.append(f"{col} LIKE :{param_key}")
            params[param_key] = f"%{segment}%"
        if segment_conditions:
            filter_conditions.append(f"({' OR '.join(segment_conditions)})")

    if filter_conditions:
        return " AND ".join(filter_conditions), params
    return "", {}


def build_extra_query_clause(
    extra_params: Optional[List[QueryCondition]],
) -> Tuple[str, Dict[str, Any]]:
    """
    安全地构建额外查询条件的SQL片段和参数

    Args:
        extra_params: 额外查询条件列表

    Returns:
        tuple: (SQL片段, 参数字典)
    """
    if not extra_params:
        return "", {}

    conditions = []
    params = {}

    for idx, condition in enumerate(extra_params):
        param_key = f"p{idx}"
        # 白名单验证，防止SQL注入
        if condition.operator.lower() not in ["=", "!=", ">", "<", ">=", "<="]:
            logger.warning(f"不支持的操作符: {condition.operator}，跳过该条件")
            continue
        conditions.append(f"{condition.column_name} {condition.operator} :{param_key}")
        params[param_key] = condition.value

    if conditions:
        return " AND ".join(conditions), params
    return "", {}


def build_order_by_clause(order_columns: Optional[List[OrderCondition]]) -> str:
    """
    安全地构建排序 (ORDER BY) SQL子句

    Args:
        order_columns: 排序条件列表

    Returns:
        str: ORDER BY SQL语句

    注意:
        此函数被认为是“安全的”，因为列名和方向(ASC/DESC)
        通常不是由用户直接提供。如果是，则应在调用此函数前
        通过白名单进行验证。
    """
    if not order_columns:
        return ""

    order_segments = []
    for col in order_columns:
        # col.build_order_sql() 内部应执行验证
        order_segments.append(col.build_order_sql())

    if order_segments:
        return f"ORDER BY {', '.join(order_segments)}"
    return ""
