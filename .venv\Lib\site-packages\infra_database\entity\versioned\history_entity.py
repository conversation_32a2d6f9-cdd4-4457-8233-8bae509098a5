"""
历史实体类，用于记录实体的历史版本
"""

from datetime import datetime
from typing import Optional, TypeVar

from sqlalchemy import DateTime, Integer, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column

from infra_database.entity.base_entity import SqlalchemyEntity

# 定义泛型类型
GenericHistoryEntity = TypeVar("GenericHistoryEntity", bound="HistoryEntity")


class HistoryEntity(SqlalchemyEntity):
    """
    历史实体基类

    用于记录实体的历史版本，包括：
    1. 历史记录ID
    2. 事务ID（开始和结束）
    3. 时间戳（开始和结束）
    4. 主表对应字段的副本
    """

    __abstract__ = True
    history_id: Mapped[str] = mapped_column(
        String(40),
        comment="history_id",
        server_default=text("uuid_generate_v7()"),
        primary_key=True,
    )
    commence_transaction_id: Mapped[str] = mapped_column(String(40), index=True, comment="开始事务id", nullable=False)
    commenced_on: Mapped[datetime] = mapped_column(DateTime(timezone=True), comment="开始于", nullable=False)

    cease_transaction_id: Mapped[Optional[str]] = mapped_column(String(40), index=True, comment="结束事务id")
    ceased_on: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        comment="结束于",
        nullable=False,
        server_default=text("'infinity'::timestamptz"),
    )

    # 主表的对应字段
    id: Mapped[str] = mapped_column(String(40), comment="id", nullable=False)
    handler_category: Mapped[str] = mapped_column(String(255), comment="操作者类型", nullable=False)
    handler_id: Mapped[str] = mapped_column(String(40), comment="操作者id", nullable=False)
    handled_on: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        comment="操作于",
        nullable=False,
    )
    remark: Mapped[Optional[str]] = mapped_column(Text, comment="备注", nullable=True)
    version: Mapped[int] = mapped_column(Integer, comment="版本", server_default=text("1"), nullable=False)
