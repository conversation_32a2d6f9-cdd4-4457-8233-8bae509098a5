"""
工作单元能力扩展基类（Mixin）

此基类旨在为具体的工作单元（UOW）实现提供事务日志记录的功能。
它遵循单一职责原则，只关心 `SystemTransactionModel` 的创建和管理。
"""

from abc import ABC, abstractmethod
from types import TracebackType
from typing import (
    Any,
    AsyncContextManager,
    ContextManager,
    Dict,
    List,
    Optional,
    Type,
    TypeVar,
)

# 请确保这些导入路径在你的项目中是正确的
from infra_basic.error.error_info import ErrorInfo
from infra_basic.resource.resource_interface import Resource
from loguru import logger

from infra_database.model.transaction_model import SystemTransactionModel

# 定义一个类型变量，以便上下文管理器返回正确的具体类型
T_UOW = TypeVar("T_UOW", bound="BaseUnitOfWork")


class BaseUnitOfWork(ContextManager[T_UOW], AsyncContextManager[T_UOW], ABC):
    """
    一个为具体UOW提供事务日志功能的抽象基类（Mixin）。

    它本身不实现事务逻辑，而是依赖于子类提供的
    `session_stack_level` 属性来管理事务日志。
    """

    def __init__(self) -> None:
        """初始化事务日志相关的存储。"""
        self._transaction_logs: Dict[int, SystemTransactionModel] = {}
        self._parent_log_map: Dict[int, int] = {}

    @property
    @abstractmethod
    def session_stack_level(self) -> int:
        """
        [契约] 子类必须实现此属性。

        它应返回当前数据库会话/事务栈的真实深度。
        - 0 表示不在任何事务中。
        - 1 表示在顶层事务中。
        - 2+ 表示在嵌套事务（SAVEPOINT）中。
        """
        raise NotImplementedError

    def log_transaction(
        self,
        handler: Resource,
        action: str,
        action_params: Optional[Dict[str, Any]] = None,
        initiator: Optional[Resource] = None,
    ) -> SystemTransactionModel:
        """
        记录一条新的事务日志，与当前事务层级关联。
        此方法应在具体的UOW实现中，每次开启一个新的数据库事务层级（包括顶层和嵌套）时，
        在相应层级事务动作完成后（或进入时）调用。
        """
        current_db_level = self.session_stack_level
        if current_db_level == 0:
            raise RuntimeError("事务日志只能在活跃的工作单元（'with' 或 'asynchronous with' 块）中记录。")
        if current_db_level in self._transaction_logs:
            raise RuntimeError(f"层级 {current_db_level} 的事务日志已被记录，一个事务层级只允许记录一条主日志。")

        parent_log_id = None
        # 如果当前层级 > 1，则其父层级为 current_db_level - 1
        parent_db_level = current_db_level - 1
        if parent_db_level > 0 and parent_db_level in self._transaction_logs:
            self._parent_log_map[current_db_level] = parent_db_level
            parent_log = self._transaction_logs[parent_db_level]
            if parent_log and parent_log.id:  # 确认父日志已经有了ID
                parent_log_id = parent_log.id

        log_entry = SystemTransactionModel(
            handler_category=handler.resource_info.category,
            handler_id=handler.resource_info.id or "",
            action=action,
            action_params=action_params,
            is_succeed=True,  # 默认成功，后续通过 _complete_log_entry 更新
            parent_transaction_id=parent_log_id,
        )

        if initiator:
            log_entry.initiator_category = initiator.resource_info.category
            log_entry.initiator_id = initiator.resource_info.id

        logger.debug(f"记录事务日志: {action}, 层级: {current_db_level}, Log ID: {log_entry.id}")
        self._transaction_logs[current_db_level] = log_entry
        return log_entry

    def _complete_log_entry(self, log_level: int, is_succeed: bool, error_message: Optional[str] = None) -> None:
        """
        [内部方法] 完成指定层级的事务日志记录。
        此方法应由具体的UOW实现在其 `__exit__` 或 `__aexit__` 退出逻辑中调用，
        并且传入的 `log_level` 应该是该UOW实例退出前的 `session_stack_level`。
        """
        if log_level not in self._transaction_logs:
            # 如果该层级没有日志，可能是因为 log_transaction 未被调用。
            # 在DEBUG级别记录此信息，因为它在开发过程中可能有用，但在生产中不应视为警告。
            logger.debug(f"尝试完成层级 {log_level} 的事务日志，但未找到对应记录（这通常是正常的）。")
            return

        log_entry = self._transaction_logs[log_level]
        log_entry.is_succeed = is_succeed
        # 假设 SystemTransactionModel 有一个更新时间戳的方法或字段
        # log_entry.end_time = datetime.utcnow() # 示例，具体取决于你的模型实现

        if not is_succeed and error_message:
            log_entry.error_detail = ErrorInfo(message=error_message)

        status = "成功" if is_succeed else f"失败: {error_message}"
        logger.debug(f"完成事务日志: {log_entry.action}, 层级: {log_level}, 状态: {status}, Log ID: {log_entry.id}")

    def _cleanup_logs(self) -> None:
        """
        [内部方法] 清理所有事务日志记录。
        此方法应由具体的UOW实现在最外层事务结束后被调用。
        """
        logger.debug("清理所有事务日志资源...")
        self._transaction_logs.clear()
        self._parent_log_map.clear()

    @property
    def all_transaction_logs(self) -> List[SystemTransactionModel]:
        """
        获取本次工作单元实例（从顶级事务开始到结束）中所有已记录的事务日志。
        注意：这返回的是当前UOW生命周期内的日志，不是全局的。
        """
        return list(self._transaction_logs.values())

    # --- 上下文管理器协议 ---
    # 子类必须实现这些异步和同步上下文管理器方法
    @abstractmethod
    def __enter__(self) -> T_UOW:
        """同步上下文管理器入口点。"""
        raise NotImplementedError

    @abstractmethod
    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[TracebackType]
    ) -> Optional[bool]:
        """同步上下文管理器退出点。"""
        raise NotImplementedError

    @abstractmethod
    async def __aenter__(self) -> T_UOW:
        """异步上下文管理器入口点。"""
        raise NotImplementedError

    @abstractmethod
    async def __aexit__(
        self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[TracebackType]
    ) -> Optional[bool]:
        """异步上下文管理器退出点。"""
        raise NotImplementedError
