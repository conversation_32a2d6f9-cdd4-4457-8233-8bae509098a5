"""
阿里云帮助类
"""

from typing import Optional

from oss2 import BUCKET_ACL_PRIVATE, OBJECT_ACL_PUBLIC_READ, Auth, Bucket
from oss2.exceptions import NoSuchBucket

from infra_object_storage.error import ObjectNotPublicReadError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
    ObjectStorageTypeEnum,
)
from infra_object_storage.helper.object_storage_interface import (
    AccountInfo,
    ObjectStorageInterface,
)
from infra_object_storage.settings import OssSetting


class OssHelper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """
    阿里云帮助类
    """

    def get_account_info(self) -> AccountInfo:
        """获取账号信息"""
        return AccountInfo(
            type=ObjectStorageTypeEnum.OSS,
            site=self.__settings.endpoint,
            unified_id=self.__settings.access_key_id,
        )

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获得目标篮子"""
        return self._prepare_bucket_name(bucket_name=bucket_name)

    def __init__(self, oss_settings: Optional[OssSetting] = None):
        """初始化"""
        if oss_settings is None:
            oss_settings = OssSetting()
        self.__settings = oss_settings
        super().__init__(default_bucket=self.__settings.default_bucket)
        self.__oss_auth = Auth(
            access_key_id=self.__settings.access_key_id,
            access_key_secret=self.__settings.access_key_secret,
        )

    def __prepare_bucket(self, bucket_name: Optional[str]) -> Bucket:
        """准备篮子"""
        return Bucket(
            auth=self.__oss_auth,
            endpoint=self.__settings.endpoint,
            bucket_name=bucket_name,
        )

    def make_bucket(self, bucket_name: Optional[str]):
        """构建篮子"""
        bucket = self.__prepare_bucket(bucket_name=bucket_name)
        bucket.create_bucket()
        bucket.put_bucket_acl(BUCKET_ACL_PRIVATE)

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """检测篮子是否存在"""
        try:
            self.__prepare_bucket(bucket_name=bucket_name).get_bucket_info()
        except NoSuchBucket:
            return False
        except Exception as err:
            raise err
        return True

    def remove_bucket(self, bucket_name: Optional[str]):
        """删除篮子"""
        bucket = self.__prepare_bucket(bucket_name=bucket_name)
        bucket.delete_bucket()

    def download_file(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """下载文件"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        bucket = self.__prepare_bucket(bucket_name=target_bucket_name)
        object_stream = bucket.get_object(key=object_name)
        data = object_stream.read()
        if isinstance(data, str):
            return data.encode("utf-8")
        return data

    def upload_file(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """上传文件"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        bucket = self.__prepare_bucket(bucket_name=target_bucket_name)
        bucket.put_object(key=object_name, data=object_data)
        if is_public:
            bucket.put_object_acl(key=object_name, permission=OBJECT_ACL_PUBLIC_READ)

    def get_file_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """获取文件下载链接"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        bucket = self.__prepare_bucket(bucket_name=target_bucket_name)
        return bucket.sign_url(
            method="GET", key=object_name, expires=expires_seconds
        )

    def __is_object_public_read(self, object_name: str, bucket_name) -> bool:
        """
        判断对象是否公开读取
        :param object_name:
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        bucket = self.__prepare_bucket(bucket_name=target_bucket_name)
        acl = bucket.get_object_acl(object_name)
        if acl.acl in ["public-read-write", "public-read"]:
            return True
        return False



    def get_object_policy(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        raise NotImplementedError

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        raise NotImplementedError

    def set_object_public_read(self, object_name: str, bucket_name: Optional[str] = None):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        bucket = self.__prepare_bucket(bucket_name=target_bucket_name)
        bucket.put_object_acl(key=object_name, permission=OBJECT_ACL_PUBLIC_READ)

    def get_object_acl(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        bucket = self.__prepare_bucket(bucket_name=target_bucket_name)
        acl = bucket.get_object_acl(object_name)
        return {"acl": acl.acl}
