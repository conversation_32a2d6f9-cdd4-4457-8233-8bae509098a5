"""
阿里云帮助类
"""

import json
from typing import cast
from typing import Any, Iterable, List, Optional

from oss2 import (
    BUCKET_ACL_PRIVATE,
    OBJECT_ACL_PUBLIC_READ,
    Auth,
    Bucket,
    ObjectIterator,
)
from oss2.exceptions import NoSuchBucket

from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
)
from infra_object_storage.helper.object_storage_interface import ObjectStorageInterface
from infra_object_storage.settings import OssAccount


class OssHelper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """
    阿里云 OSS 帮助类
    """

    def get_account_info(self) -> OssAccount:
        """获取账号信息"""
        return self.__account

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获得目标存储桶"""
        return self._prepare_bucket_name(bucket_name=bucket_name)

    def __init__(self, oss_account: OssAccount):
        """初始化"""
        self.__account = oss_account
        super().__init__(default_bucket=self.__account.default_bucket)
        self.__oss_auth = Auth(
            access_key_id=self.__account.access_key_id,
            access_key_secret=self.__account.access_key_secret,
        )

    def __prepare_bucket(self, bucket_name: Optional[str]) -> Bucket:
        """准备存储桶"""
        target_bucket_name: str = self.get_target_bucket_name(bucket_name)
        bucket: Bucket = Bucket(
            auth=self.__oss_auth,
            endpoint=self.__account.endpoint,
            bucket_name=target_bucket_name,
        )
        return bucket

    def make_bucket(self, bucket_name: Optional[str]) -> None:
        """创建存储桶"""
        if not self.bucket_exists(bucket_name):
            bucket: Bucket = self.__prepare_bucket(bucket_name)
            bucket.create_bucket(permission=BUCKET_ACL_PRIVATE)
        return None

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """检测存储桶是否存在"""
        bucket = self.__prepare_bucket(bucket_name)
        try:
            bucket.get_bucket_info()
            return True
        except NoSuchBucket:
            return False

    def remove_bucket(self, bucket_name: Optional[str]):
        """删除存储桶"""
        bucket = self.__prepare_bucket(bucket_name)
        bucket.delete_bucket()

    def download_object(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """下载文件"""
        bucket = self.__prepare_bucket(bucket_name)
        object_stream = bucket.get_object(key=object_name)
        data = object_stream.read()
        if not isinstance(data, bytes):
            # Should not happen with modern oss2, but as a safeguard.
            return str(data).encode("utf-8")
        return data

    def upload_object(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """上传文件"""
        bucket = self.__prepare_bucket(bucket_name)
        bucket.put_object(key=object_name, data=object_data)
        if is_public:
            self.set_object_public_read(object_name, bucket_name)

    def get_object_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """获取文件下载链接"""
        bucket = self.__prepare_bucket(bucket_name)
        url: str = bucket.sign_url(method="GET", key=object_name, expires=expires_seconds)
        return url

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict[str, Any]:
        bucket = self.__prepare_bucket(bucket_name)
        result = bucket.get_bucket_policy()
        if result.policy is not None:
            return cast(dict[str, Any], json.loads(result.policy))
        return {}

    def set_bucket_policy(self, policy: dict, bucket_name: Optional[str] = None):
        bucket = self.__prepare_bucket(bucket_name)
        bucket.put_bucket_policy(json.dumps(policy))

    def set_object_public_read(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> None:
        bucket = self.__prepare_bucket(bucket_name)

        # 获取当前对象的ACL
        current_acl_response = bucket.get_object_acl(object_name)

        # 检查是否已经有public-read权限
        # OSS的ACL响应中，acl属性直接表示了权限
        if current_acl_response.acl != OBJECT_ACL_PUBLIC_READ:
            bucket.put_object_acl(object_name, OBJECT_ACL_PUBLIC_READ)
            return None
        return None

    def get_object_acl(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> dict[str, Any]:
        bucket = self.__prepare_bucket(bucket_name)
        acl = bucket.get_object_acl(object_name)
        return {"acl": acl.acl}

    def object_exists(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bool:
        bucket = self.__prepare_bucket(bucket_name)
        exists = bucket.object_exists(object_name)
        assert isinstance(exists, bool), "object_exists should return a boolean"
        return exists

    def delete_object(self, object_name: str, bucket_name: Optional[str] = None):
        bucket = self.__prepare_bucket(bucket_name)
        bucket.delete_object(object_name)

    def list_objects(
        self, bucket_name: Optional[str] = None, prefix: Optional[str] = None
    ) -> Iterable[str]:
        bucket = self.__prepare_bucket(bucket_name)
        for obj in ObjectIterator(bucket, prefix=prefix or ""):
            yield obj.key

    def delete_objects(
        self, object_names: List[str], bucket_name: Optional[str] = None
    ):
        bucket = self.__prepare_bucket(bucket_name)
        bucket.batch_delete_objects(object_names)
