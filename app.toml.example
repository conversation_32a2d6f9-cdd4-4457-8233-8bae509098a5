# 应用配置示例文件
# 复制此文件为 app.toml 并填入真实的配置信息
# 注意：app.toml 文件包含敏感信息，不应提交到版本控制系统

[database]
driver = "postgresql+psycopg2"
host = "your_database_host"
port = 5432
user = "your_database_user"
password = "your_database_password"
database = "your_database_name"
echo = true
# 连接池配置
pool_size = 5
max_overflow = 10
pool_timeout = 30
pool_recycle = 1800
pool_pre_ping = true
# PostgreSQL 特有的 JIT 配置
enable_jit = true

# 存储账户配置
# 使用 TOML 的表数组（Array of Tables）格式
# 每个 [[storage.accounts]] 块代表一个独立的账户配置

[[storage.accounts]]
id = "your_s3_account_id"
provider = "s3"
default_bucket = "your-s3-bucket"
aws_access_key_id = "your_aws_access_key_id"
aws_secret_access_key = "your_aws_secret_access_key"
region_name = "ap-northeast-1"
endpoint_url = "https://s3.ap-northeast-1.amazonaws.com" # 可选，例如用于 MinIO

[[storage.accounts]]
id = "your_cos_account_id"
provider = "cos"
default_bucket = "your-cos-bucket-appid"
secret_id = "your_cos_secret_id"
secret_key = "your_cos_secret_key"
region = "ap-nanjing"
endpoint_url = 'https://cos.ap-nanjing.myqcloud.com'

[[storage.accounts]]
id = "your_minio_account_id"
provider = "minio"
default_bucket = "your-minio-bucket"
host = "127.0.0.1"
port = 9000
access_key = "minioadmin"
secret_key = "minioadmin"
secure = false # 如果 MinIO 使用 HTTPS，请设置为 true

[[storage.accounts]]
id = "your_oss_account_id"
provider = "oss"
default_bucket = "your-oss-bucket"
access_key_id = "your_oss_access_key_id"
access_key_secret = "your_oss_access_key_secret"
endpoint = "oss-cn-shanghai.aliyuncs.com"
