# 应用配置示例文件
# 复制此文件为 app.toml 并填入真实的配置信息
# 注意：app.toml 文件包含敏感信息，不应提交到版本控制系统

[database]
driver = "postgresql+psycopg2"
host = "your_database_host"
port = 5432
user = "your_database_user"
password = "your_database_password"
database = "your_database_name"
echo = true
# 连接池配置
pool_size = 5
max_overflow = 10
pool_timeout = 30
pool_recycle = 1800
pool_pre_ping = true
# PostgreSQL 特有的 JIT 配置
enable_jit = true

[storage_client]
helper_type = 'cos'  # 可选值: s3, minio, cos, oss

[cos]
# 腾讯云COS配置
default_bucket = 'your-bucket-name-appid'
secret_id = 'your_cos_secret_id'
secret_key = 'your_cos_secret_key'
region = 'your_region'  # 例如: ap-nanjing
endpoint_url = 'https://cos.your-region.myqcloud.com'

[oss]
# 阿里云OSS配置
default_bucket = 'your-bucket-name'
access_key_id = 'your_oss_access_key_id'
access_key_secret = 'your_oss_access_key_secret'
endpoint = 'oss-your-region.aliyuncs.com'  # 例如: oss-cn-shanghai.aliyuncs.com

[s3]
# AWS S3配置
aws_access_key_id = 'your_aws_access_key_id'
aws_secret_access_key = 'your_aws_secret_access_key'
default_bucket = 'your-s3-bucket-name'
region_name = 'your_aws_region'  # 例如: ap-northeast-1
endpoint_url = 'https://s3.your-region.amazonaws.com'

[minio]
# MinIO配置
default_bucket = 'your-minio-bucket'
host = 'your-minio-host'
port = 9000
secure = true  # 是否使用HTTPS
access_key = 'your_minio_access_key'
secret_key = 'your_minio_secret_key'
