"""
系统事务实体模块

该模块定义了 `SystemTransactionEntity`，用于在数据库中记录详细的系统操作事务。
"""

from typing import Type, TypeVar

from sqlalchemy import Boolean, Column, DateTime, Index, String, Text, func
from sqlalchemy.dialects.postgresql import JSONB

from infra_database.entity.basic_entity import BasicEntity
from infra_database.model.transaction_model import SystemTransactionModel

GenericSystemTransactionEntity = TypeVar("GenericSystemTransactionEntity", bound="SystemTransactionEntity")


class SystemTransactionEntity(BasicEntity):
    """
    系统事务实体

    该实体用于记录系统中的每一次操作事务，提供了完整的审计追踪能力。
    它继承自 `BasicEntity`，因此拥有一个自动生成的 `id` 主键。

    Attributes:
        parent_transaction_id (str, optional): 父事务ID，用于构建事务的层级关系。
        handler_category (str): 执行操作的处理者类型（例如：'user', 'system'）。
        handler_id (str): 执行操作的处理者唯一标识。
        handled_on (datetime): 操作的执行时间，数据库会自动记录创建和更新时间。
        action (str): 操作的具体动作名称（例如：'USER_LOGIN', 'CREATE_ORDER'）。
        action_params (dict, optional): 执行操作时传入的参数，以 JSONB 格式存储。
        initiator_category (str): 触发该事务的发起者类型（例如：'user', 'robot', 'schedule_job'）。
        initiator_id (str): 触发该事务的发起者唯一标识。
        commenced_on (datetime): 事务的开始时间。
        ceased_on (datetime, optional): 事务的结束时间，如果事务仍在进行中，则为 NULL。
        is_succeed (bool): 事务是否成功完成。
        error_detail (dict, optional): 如果事务失败，记录详细的错误信息，以 JSONB 格式存储。
        remark (str, optional): 对该事务的额外备注信息。
    """

    __tablename__ = "st_system_transaction"
    __table_args__ = {"comment": "记录系统在特定时间段内由谁以何种参数执行的事务"}

    # 父事务ID，用于追踪事务链
    parent_transaction_id = Column(String(40), comment="父事务ID", nullable=True)

    # 操作执行者信息
    handler_category = Column(String(255), comment="操作者类型", nullable=False)
    handler_id = Column(String(40), comment="操作者ID", nullable=False)

    # 操作时间，在创建和更新时自动设置
    handled_on = Column(
        DateTime(timezone=True),
        comment="操作执行时间",
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # 操作详情
    action = Column(String(255), comment="操作动作（如：用户登录、创建订单）", nullable=False)
    action_params = Column(JSONB, comment="操作参数（JSON格式）", nullable=True)

    # 事务发起者信息
    initiator_category = Column(
        String(255),
        comment="发起者类型（如：user, robot, schedule_job）",
        nullable=False,
    )
    initiator_id = Column(String(40), comment="发起者ID", nullable=False)

    # 事务时间范围
    commenced_on = Column(DateTime(timezone=True), comment="事务开始时间", nullable=False)
    ceased_on = Column(DateTime(timezone=True), comment="事务结束时间", nullable=True)

    # 事务结果
    is_succeed = Column(Boolean, comment="事务是否成功", nullable=False)
    error_detail = Column(JSONB, comment="错误详情（JSON格式）", nullable=True)

    # 附加信息
    remark = Column(Text, comment="备注信息", nullable=True)

    @classmethod
    def from_model(
        cls: Type[GenericSystemTransactionEntity], model: SystemTransactionModel
    ) -> GenericSystemTransactionEntity:
        """
        从数据模型转换到实体。
        """
        return cls(
            id=model.id,
            parent_transaction_id=model.parent_transaction_id,
            handler_category=model.handler_category,
            handler_id=model.handler_id,
            action=model.action,
            action_params=model.action_params,
            initiator_category=model.initiator_category,
            initiator_id=model.initiator_id,
            commenced_on=model.commenced_on,
            ceased_on=model.ceased_on,
            is_succeed=model.is_succeed,
            error_detail=model.error_detail,
            remark=model.remark,
        )


# 创建索引，用于优化查询性能
Index(
    "idx_st_transaction_initiator",
    SystemTransactionEntity.initiator_id,
    SystemTransactionEntity.initiator_category,
)
