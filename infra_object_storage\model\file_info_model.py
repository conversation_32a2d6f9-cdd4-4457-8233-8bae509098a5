"""
文件信息
"""

from typing import Optional

from infra_database.model.versioned.versioned_model import VersionedModel


class FileInfoModel(VersionedModel):
    """文件信息"""

    storage_info_id: str
    original_name: str
    summary: Optional[str] = None


class FileExtInfoModel(FileInfoModel):
    """带下载链接的文件信息"""

    bucket_name: str
    object_name: str
    checksum: str
    size: int
    url: Optional[str] = None
