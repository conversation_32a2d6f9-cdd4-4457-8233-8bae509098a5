"""
文件信息
"""

from typing import Optional

from infra_database.model.versioned.versioned_model import VersionedModel


class FileInfoModel(VersionedModel):
    """文件信息"""

    object_storage_raw_id: str
    original_name: str
    summary: Optional[str] = None


class FileExtInfoModel(FileInfoModel):
    """带下载链接的文件信息"""

    account_id: Optional[str] = None
    bucket_name: Optional[str] = None
    object_name: Optional[str] = None
    checksum: Optional[str] = None
    size: Optional[int] = None
    url: Optional[str] = None
    relationship: Optional[str] = None
