# -*- coding: utf-8 -*-
import uuid
from typing import Optional

import pytest
from dependency_injector.providers import Object
from infra_basic.resource.basic_resource import BasicResource
from infra_database.model.transaction_model import SystemTransactionModel
from infra_database.uow.session_proxy import SessionProxy
from infra_database.uow.sync_sql_alchemy_unit_of_work import SyncSqlAlchemyUnitOfWork

from infra_object_storage.error import ObjectStorageError
from infra_object_storage.model.file_info_model import FileExtInfoModel
from infra_object_storage.service.object_storage_service import ObjectStorageService


@pytest.fixture
def object_storage_service(test_container):
    """从测试容器获取对象存储服务实例"""
    return test_container.object_storage_container().object_storage_service()


@pytest.fixture
def prepare_uow(test_container):
    """从测试容器获取UOW实例"""
    return test_container.uow()


@pytest.fixture
def session_proxy(test_container):
    """从测试容器获取会话代理实例"""
    return test_container.session_proxy


@pytest.mark.usefixtures("create_tables")
class TestObjectStorageService:
    """测试 ObjectStorageService"""

    def _prepare_file(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
        account_id: str = "minio_test",
        file_name: str = "test.txt",
        file_content: bytes = b"test content",
        resource: Optional[BasicResource] = None,
        relationship: Optional[str] = None,
    ) -> FileExtInfoModel:
        """准备一个文件并返回其信息"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_prepare"
        )
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            if resource and relationship:
                file_info = object_storage_service.upload_file_with_resource(
                    account_id=account_id,
                    file_name=file_name,
                    file_blob=file_content,
                    resource=resource,
                    relationship=relationship,
                    transaction=transaction,
                )
            else:
                file_info = object_storage_service.upload_file(
                    account_id=account_id,
                    file_name=file_name,
                    file_blob=file_content,
                    transaction=transaction,
                )
        return file_info

    def test_upload_and_get_file(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试上传文件和获取文件URL"""
        file_name = f"{uuid.uuid4()}.txt"
        file_content = b"This is a test file for upload and get."
        
        # Upload
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_info = self._prepare_file(
                object_storage_service,
                prepare_uow,
                session_proxy,
                file_name=file_name,
                file_content=file_content,
            )

        assert file_info is not None
        assert file_info.id is not None
        assert file_info.original_name == file_name
        assert file_info.size == len(file_content)
        assert file_info.url is not None

        # Get URL
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_url = object_storage_service.get_file_url(file_info.id)
        
        assert file_url is not None
        assert isinstance(file_url, str)
        assert file_info.object_name is not None
        assert file_info.object_name in file_url

    def test_upload_file_with_resource_and_get_list(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
        prepare_handler: BasicResource,
    ):
        """测试上传文件并关联资源，然后获取列表"""
        file_name = f"{uuid.uuid4()}.txt"
        file_content = b"Test file linked to a resource."
        relationship = "attachment"

        # Upload with resource
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_info = self._prepare_file(
                object_storage_service,
                prepare_uow,
                session_proxy,
                file_name=file_name,
                file_content=file_content,
                resource=prepare_handler,
                relationship=relationship,
            )
        
        assert file_info is not None
        assert file_info.id is not None

        # Get list
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_list = object_storage_service.get_resource_related_file_list(
                resource=prepare_handler, relationship=relationship
            )

        assert len(file_list) == 1
        assert file_list[0].id == file_info.id
        assert file_list[0].original_name == file_name
        assert file_list[0].url is not None

    def test_link_and_unlink_file_and_resource(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
        prepare_handler: BasicResource,
    ):
        """测试关联和取消关联文件与资源"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_link_unlink"
        )
        relationship = "profile_image"

        # 1. Prepare file
        file_info = self._prepare_file(object_storage_service, prepare_uow, session_proxy)
        assert file_info.id is not None

        # 2. Link
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_service.link_file_and_resource(
                file_id=file_info.id,
                resource=prepare_handler,
                relationship=relationship,
                transaction=transaction,
            )

        # 3. Verify link
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            linked_files = object_storage_service.get_resource_related_file_list(
                resource=prepare_handler, relationship=relationship
            )
        assert len(linked_files) == 1
        assert linked_files[0].id == file_info.id

        # 4. Unlink
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_service.unlink_file_and_resource(
                file_id=file_info.id,
                resource=prepare_handler,
                relationship=relationship,
                transaction=transaction,
            )

        # 5. Verify unlink
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            unlinked_files = object_storage_service.get_resource_related_file_list(
                resource=prepare_handler, relationship=relationship
            )
        assert len(unlinked_files) == 0

    def test_delete_file(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试删除文件"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_delete"
        )
        
        # 1. Prepare file
        file_info = self._prepare_file(object_storage_service, prepare_uow, session_proxy)
        assert file_info.id is not None

        # 2. Delete file
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_service.delete_file(file_id=file_info.id, transaction=transaction)

    def test_download_file(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试下载文件内容"""
        file_name = f"{uuid.uuid4()}.txt"
        file_content = b"This is a test file for download."

        # 1. Upload file
        file_info = self._prepare_file(
            object_storage_service,
            prepare_uow,
            session_proxy,
            file_name=file_name,
            file_content=file_content,
        )
        assert file_info.id is not None

        # 2. Download file
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            downloaded_file_info, downloaded_content = object_storage_service.download_file(
                file_info.id
            )

        # 3. Verify downloaded content and info
        assert downloaded_file_info.id == file_info.id
        assert downloaded_content == file_content
        assert downloaded_file_info.original_name == file_name
        assert downloaded_file_info.size == len(file_content)

    def test_get_file_url_error_handling(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试获取文件URL时错误处理"""
        # Test with non-existent file ID
        with pytest.raises(ObjectStorageError, match="storage for file_id\\[non_existent_id\\] not found or account_id is missing"):
            with prepare_uow:
                session_proxy.provided.set_session(prepare_uow.session)
                object_storage_service.get_file_url("non_existent_id")

    def test_download_file_error_handling(
        self,
        object_storage_service: ObjectStorageService,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试下载文件时错误处理"""
        # Test with non-existent file ID
        with pytest.raises(ObjectStorageError, match="storage for file_id\\[non_existent_id\\] not found or account_id is missing"):
            with prepare_uow:
                session_proxy.provided.set_session(prepare_uow.session)
                object_storage_service.download_file("non_existent_id")

        # Test with file info missing (e.g., storage exists but file_info deleted)
        # This scenario is harder to mock directly without deeper repository control,
        # but the current implementation raises ObjectStorageError if file_info is None.
        # We can simulate this by creating a storage_raw but not a file_info.
        # For now, rely on the first test case covering the primary error path.
        # If needed, a more complex setup could involve mocking repository methods.