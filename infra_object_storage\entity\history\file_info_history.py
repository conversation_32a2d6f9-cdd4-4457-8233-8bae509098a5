"""
资源与文件之间的关系历史实体类
"""

from typing import Optional

from infra_database.entity.versioned.history_entity import HistoryEntity
from sqlalchemy import Index, String, Text
from sqlalchemy.orm import Mapped, mapped_column


class FileInfoHistoryEntity(HistoryEntity):
    """
    资源与文件之间的关系（历史实体类）
    """

    __tablename__ = "st_file_info_history"
    __table_args__ = {"comment": "文件信息（历史）"}

    object_storage_raw_id: Mapped[str] = mapped_column(
        String(40), nullable=False, comment="存储id", index=True
    )
    original_name: Mapped[str] = mapped_column(
        String(255), comment="文件原始名称", nullable=False
    )
    summary: Mapped[Optional[str]] = mapped_column(Text, comment="描述")


# 时间检索索引
Index(
    "idx_file_info_history_time_range",
    FileInfoHistoryEntity.id,
    FileInfoHistoryEntity.commenced_on,
    FileInfoHistoryEntity.ceased_on.desc(),
    unique=True,
)
