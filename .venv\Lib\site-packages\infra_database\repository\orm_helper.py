"""
ORM 辅助函数模块

本模块提供了与SQLAlchemy ORM实体相关的、类型安全的辅助函数，用于：
1. 实体类型检查和验证
2. 实体属性操作
3. 基于ORM的SQL查询构建
4. 实体数据转换
5. 实体扫描和发现

这些工具函数主要用于支持仓储层的ORM操作，简化实体相关的常见任务。
"""

import importlib
import inspect
import pkgutil
from types import ModuleType
from typing import Any, Dict, List, Optional, Type

from loguru import logger
from sqlalchemy import inspect as sqlalchemy_inspect
from sqlalchemy.exc import NoInspectionAvailable, SQLAlchemyError
from sqlalchemy.orm import Query, Session

from infra_database.entity.base_entity import GenericSqlalchemyEntity, SqlalchemyEntity
from infra_database.error import (
    InvalidEntityClassColumnError,
    InvalidEntityClassError,
)
from infra_database.model.database_query_model import FilterCondition


def is_entity_type(input_cls: Type) -> bool:
    """
    判断一个类是否是SQLAlchemy的实体类型。

    此函数通过多种方式进行检查，以提高在不同场景（尤其是测试中）的健壮性。

    参数:
        input_cls: 要检查的类。

    返回:
        bool: 如果是SQLAlchemy实体类型则返回True，否则返回False。
    """
    if not inspect.isclass(input_cls):
        return False
    try:
        sqlalchemy_inspect(input_cls)
        return hasattr(input_cls, "__tablename__")
    except (NoInspectionAvailable, SQLAlchemyError):
        return False


def is_entity_type_with_id_field(input_cls: Type[Any]) -> bool:
    """
    判断一个类是否是SQLAlchemy的实体类型，并且该实体包含'id'字段

    此函数首先检查类是否为SQLAlchemy实体类型，然后验证其是否包含'id'字段。

    参数:
        input_cls: 要检查的类

    返回:
        bool: 如果是带有'id'字段的SQLAlchemy实体类型则返回True，否则返回False

    异常:
        如果检查过程中发生错误，会记录错误并返回False
    """
    try:
        if not is_entity_type(input_cls):
            return False
        column_name_list = get_entity_column_name_list(input_cls)
        return "id" in column_name_list
    except Exception as e:
        logger.error(
            f"判断类 {getattr(input_cls, '__name__', str(input_cls))} 是否为带有'id'字段的entity类型时发生错误: {str(e)}"
        )
        return False


def get_entity_column_name_list(entity_cls: Type[Any]) -> List[str]:
    """
    获取实体类对应的所有列名列表

    此函数通过SQLAlchemy的inspect功能获取实体类映射的所有列名。

    参数:
        entity_cls: 实体类

    返回:
        List[str]: 列名列表

    异常:
        如果传入的不是有效的SQLAlchemy实体类或发生其他错误，会记录错误并返回空列表
    """
    try:
        if not is_entity_type(entity_cls):
            logger.error(f"传入的类 {entity_cls.__name__} 不是有效的SQLAlchemy实体类")
            return []

        mapper = sqlalchemy_inspect(entity_cls)
        columns = [column.name for column in mapper.columns]

        return columns
    except NoInspectionAvailable as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的列名列表时发生检查错误: {str(e)}")
        return []
    except SQLAlchemyError as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的列名列表时发生SQLAlchemy错误: {str(e)}")
        return []
    except TypeError as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的列名列表时发生类型错误: {str(e)}")
        return []


def is_entity_row(entity_row: Any) -> bool:
    """
    判断一个对象是否是有效的SQLAlchemy实体实例

    此函数检查对象是否为None以及其类型是否为SQLAlchemy实体类型。

    参数:
        entity_row: 要检查的对象

    返回:
        bool: 如果是有效的SQLAlchemy实体实例则返回True，否则返回False

    异常:
        如果检查过程中发生错误，会记录错误并返回False
    """
    try:
        if entity_row is None:
            return False

        return is_entity_type(type(entity_row))
    except (TypeError, NoInspectionAvailable) as e:
        logger.error(f"判断对象是否为实体记录时发生错误: {str(e)}")
        return False


def get_entity_cls_attr(entity_cls: Type[GenericSqlalchemyEntity], attr_name: str) -> Any:
    """
    获取实体类中指定属性的类型

    此函数通过SQLAlchemy的inspect功能获取实体类中指定属性的Python类型。

    参数:
        entity_cls: 实体类
        attr_name: 属性名称

    返回:
        Any: 属性的Python类型

    异常:
        如果传入的不是有效的SQLAlchemy实体类或属性不存在，会记录错误并返回Any类型
    """
    try:
        if not is_entity_type(entity_cls):
            logger.error(f"传入的类 {entity_cls.__name__} 不是有效的SQLAlchemy实体类")
            return Any

        mapper = sqlalchemy_inspect(entity_cls)

        if attr_name not in mapper.columns:
            logger.error(f"属性 {attr_name} 在实体类 {entity_cls.__name__} 中不存在")
            return Any

        column = mapper.columns[attr_name]
        return column.type.python_type

    except NoInspectionAvailable as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的属性 {attr_name} 类型时发生检查错误: {str(e)}")
        return Any
    except SQLAlchemyError as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的属性 {attr_name} 类型时发生SQLAlchemy错误: {str(e)}")
        return Any
    except TypeError as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的属性 {attr_name} 类型时发生类型错误: {str(e)}")
        return Any
    except AttributeError as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的属性 {attr_name} 类型时发生属性错误: {str(e)}")
        return Any


def get_entity_table_name(entity_cls: Type[GenericSqlalchemyEntity]) -> str:
    """
    获取实体类对应的数据库表名

    此函数从实体类中获取其映射的数据库表名。

    参数:
        entity_cls: 实体类

    返回:
        str: 表名

    异常:
        如果传入的不是有效的SQLAlchemy实体类或发生其他错误，会记录错误并返回空字符串
    """
    try:
        if not is_entity_type(entity_cls):
            logger.error(f"传入的类 {entity_cls.__name__} 不是有效的SQLAlchemy实体类")
            return ""

        table_name = entity_cls.__tablename__
        return str(table_name)
    except NoInspectionAvailable as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的表名时发生检查错误: {str(e)}")
        return ""
    except SQLAlchemyError as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的表名时发生SQLAlchemy错误: {str(e)}")
        return ""
    except AttributeError as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的表名时发生属性错误: {str(e)}")
        return ""


def entity_row_to_dict(entity_row: SqlalchemyEntity) -> Dict[str, Any]:  # type:ignore
    """
    将 SQLAlchemy 实体行转换为字典

    参数:
        entity_row: SQLAlchemy 实体实例

    返回:
        Dict[str, Any]: 包含实体属性的字典

    异常:
        如果传入的不是有效的 SQLAlchemy 实体实例，会记录错误并返回空字典
    """
    try:
        if not is_entity_row(entity_row):
            logger.error(f"传入的对象不是有效的 SQLAlchemy 实体实例: {type(entity_row)}")
            return {}

        # 统一使用我们自己的转换逻辑，不依赖实体可能存在的to_dict方法
        result: Dict[str, Any] = {}
        mapper = sqlalchemy_inspect(type(entity_row))

        for column in mapper.columns:
            column_name = column.name
            column_value = getattr(entity_row, column_name, None)
            result[column_name] = column_value

        return result
    except (NoInspectionAvailable, SQLAlchemyError, TypeError, AttributeError) as e:
        logger.error(f"将实体行转换为字典时发生错误: {str(e)}")
        return {}


def prepare_new_entity(entity_cls: Type[GenericSqlalchemyEntity]) -> GenericSqlalchemyEntity:
    """
    根据类来生成一个空的实体对象

    参数:
        entity_cls: Type[GenericSqlalchemyEntity]

    返回:
        SqlalchemyEntity: 创建的空实体对象

    异常:
        InvalidEntityClassError: 如果传入的不是有效的SQLAlchemy实体类
        SQLAlchemyError: 如果创建实体对象时发生SQLAlchemy错误
    """
    try:
        if entity_cls is None or not is_entity_type(entity_cls):
            logger.error(f"传入的类 {getattr(entity_cls, '__name__', str(entity_cls))} 不是有效的SQLAlchemy实体类")
            raise InvalidEntityClassError(cls=entity_cls)

        if not is_entity_type(entity_cls):
            logger.error(f"传入的类 {entity_cls.__name__} 不是有效的SQLAlchemy实体类")
            raise InvalidEntityClassError(cls=entity_cls)

        return entity_cls()
    except SQLAlchemyError as e:
        logger.error(f"创建实体类 {entity_cls.__name__} 的实例时发生错误: {str(e)}")
        raise


def is_versioned_entity_type(input_cls: type) -> bool:
    """
    判断一个类是否是VersionedEntity类型（包括其子类）

    参数:
        input_cls (type): 需要判断的类

    返回:
        bool: 如果是VersionedEntity类型或其子类，返回True，否则返回False

    说明:
        仅支持类对象作为参数，传入其他类型会返回False。
    """
    try:
        # 延迟导入，避免循环依赖
        from infra_database.entity.versioned.versioned_entity import VersionedEntity

        if not isinstance(input_cls, type):
            return False
        return issubclass(input_cls, VersionedEntity)
    except Exception as e:
        logger.error(f"判断类 {repr(input_cls)} 是否为VersionedEntity类型时发生错误: {str(e)}")
        return False


def find_entities_in_package(package: ModuleType) -> list[type]:
    """
    递归扫描指定包下所有模块，收集所有SQLAlchemy实体类

    参数：
        package: 需要扫描的包对象

    返回：
        list[type]: 所有发现的SQLAlchemy实体类列表
    """
    entity_class_list = []
    visited_modules = set()

    def _scan_module(mod: ModuleType):
        if mod.__name__ in visited_modules:
            return
        visited_modules.add(mod.__name__)
        for name, obj in inspect.getmembers(mod, inspect.isclass):
            if is_entity_type(obj):
                entity_class_list.append(obj)

    def _walk_package(pkg: ModuleType):
        _scan_module(pkg)
        if hasattr(pkg, "__path__"):
            for finder, name, ispkg in pkgutil.walk_packages(pkg.__path__, pkg.__name__ + "."):
                try:
                    submod = importlib.import_module(name)
                    _scan_module(submod)
                    if ispkg:
                        _walk_package(submod)
                except Exception as e:
                    print(f"扫描模块{name}时发生错误: {e}")

    _walk_package(package)
    return entity_class_list


def prepare_update_data(
    entity_cls: Type[GenericSqlalchemyEntity],
    update_data: Dict[str, Any],
    limited_col_list: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    根据类和限制字段列过滤出需要更新的数据
    :param entity_cls:
    :param update_data:
    :param limited_col_list:
    :return:
    """
    entity_column_list = get_entity_column_name_list(entity_cls=entity_cls)
    update_col_list = entity_column_list
    # 当有限制字段设置的话，则需要进行检查，不满足的则抛出
    if limited_col_list:
        invalid_col_list = [x for x in limited_col_list if x not in entity_column_list]
        if invalid_col_list:
            raise InvalidEntityClassColumnError(cls=entity_cls, column_name=";".join(invalid_col_list))
        update_col_list = limited_col_list

    # 过滤出数据在更新字段中的
    filtered_data = {key: values for key, values in update_data.items() if key in update_col_list}
    return filtered_data


def prepare_entity_by_dict(
    entity_cls: Type[GenericSqlalchemyEntity], dict_data: Dict[str, Any]
) -> GenericSqlalchemyEntity:
    """
    根据传入的字典和实体类，组装成一个实体对象

    参数：
        entity_cls: Type[GenericSqlalchemyEntity] - 实体类
        dict_data: Dict[str, Any] - 字段数据字典

    返回：
        GenericSqlalchemyEntity: 组装好的实体对象

    异常：
        InvalidEntityClassError: 如果传入的不是有效的SQLAlchemy实体类
        SQLAlchemyError: 如果创建实体对象时发生SQLAlchemy错误
    """
    try:
        if entity_cls is None or not is_entity_type(entity_cls):
            logger.error(f"传入的类 {getattr(entity_cls, '__name__', str(entity_cls))} 不是有效的SQLAlchemy实体类")
            raise InvalidEntityClassError(cls=entity_cls)
        # 创建空实体对象
        entity = prepare_new_entity(entity_cls)
        # 获取实体所有字段名
        column_name_list = get_entity_column_name_list(entity_cls)
        # 只设置实体类中存在的字段
        for key, value in dict_data.items():
            if key in column_name_list:
                setattr(entity, key, value)
        return entity
    except SQLAlchemyError as e:
        logger.error(f"根据字典组装实体类 {entity_cls.__name__} 的实例时发生错误: {str(e)}")
        raise


# SQL操作符常量
class SQLOperator:
    """SQL操作符常量类"""

    EQUAL = "="
    NOT_EQUAL = "!="
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="
    IN = "in"
    LIKE = "like"


# SQL操作符映射
OPERATOR_MAP = {
    SQLOperator.EQUAL: lambda column, value: column == value,
    SQLOperator.NOT_EQUAL: lambda column, value: column != value,
    SQLOperator.GREATER_THAN: lambda column, value: column > value,
    SQLOperator.LESS_THAN: lambda column, value: column < value,
    SQLOperator.GREATER_EQUAL: lambda column, value: column >= value,
    SQLOperator.LESS_EQUAL: lambda column, value: column <= value,
    SQLOperator.IN: lambda column, value: column.in_(value),
    SQLOperator.LIKE: lambda column, value: column.like(value),
}


def build_sqlalchemy_query(
    session: Session,
    entity_cls: Type[GenericSqlalchemyEntity],
    params: Optional[List[FilterCondition]] = None,
) -> Query[GenericSqlalchemyEntity]:
    """
    根据输入的数据库类及查询条件构建查询
    :param session: SQLAlchemy会话
    :param entity_cls: 实体类
    :param params: 过滤条件列表
    :return: SQLAlchemy查询对象
    """
    query = session.query(entity_cls)
    if not params:
        return query

    for fc in params:
        column_name = fc.column_name
        operator = fc.operator
        value = fc.value

        if not hasattr(entity_cls, column_name):
            logger.warning(f"实体类{entity_cls.__name__}无字段{column_name}, 跳过该条件")
            continue

        column = getattr(entity_cls, column_name)

        if operator not in OPERATOR_MAP:
            logger.warning(f"不支持的操作符: {operator}，跳过该条件")
            continue

        query = query.filter(OPERATOR_MAP[operator](column, value))

    return query
