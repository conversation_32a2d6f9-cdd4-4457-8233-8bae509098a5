"""
版本化辅助函数
"""

from typing import Any, Dict, Type

from infra_database.entity.base_entity import GenericSqlalchemyEntity
from infra_database.entity.versioned.history_entity import HistoryEntity
from infra_database.entity.versioned.versioned_entity import VersionedEntity
from infra_database.error import UnexpectedDataType
from infra_database.model.transaction_model import SystemTransactionModel
from infra_database.repository.orm_helper import get_entity_column_name_list

# 缓存：Entity类 -> HistoryEntity类
_entity_to_history_entity_cache: Dict[str, Type[Any]] = {}


def prepare_handler_info(
    entity_cls: Type[GenericSqlalchemyEntity],
    dict_data: Dict[str, Any],
    transaction: SystemTransactionModel,
) -> None:
    """
    清除操作列数据并更新更系列
    :param entity_cls:
    :param dict_data:
    :param transaction:
    :return:None
    """
    entity_column_name_list = get_entity_column_name_list(entity_cls=entity_cls)
    if "handler_category" in entity_column_name_list:
        dict_data["handler_category"] = transaction.handler_category
    if "handler_id" in entity_column_name_list:
        dict_data["handler_id"] = transaction.id
    if "handled_on" in entity_column_name_list:
        dict_data["handled_on"] = transaction.handled_on


def get_history_entity_by_entity_class(entity_cls: type) -> type:
    """
    根据传入的实体类获取其对应的历史实体类，并使用缓存提升性能。

    参数：
        entity_cls (type): 需要获取历史实体类的SQLAlchemy实体类，必须为VersionedEntity的子类
    返回：
        type: 对应的HistoryEntity类
    异常：
        UnexpectedDataType: 如果传入的类不是VersionedEntity的子类，则抛出此异常
    """
    # 先查缓存
    entity_key = entity_cls.__qualname__
    if entity_key in _entity_to_history_entity_cache:
        return _entity_to_history_entity_cache[entity_key]
    # 类型校验
    if not issubclass(entity_cls, VersionedEntity):
        raise UnexpectedDataType(f"传入的类 {entity_cls.__name__} 不是 VersionedEntity 的子类，无法获取历史实体类")
    # 获取历史实体类
    history_entity_cls = entity_cls.get_history_entity()
    # 写入缓存
    _entity_to_history_entity_cache[entity_key] = history_entity_cls
    return history_entity_cls


def is_history_entity(entity_cls: type) -> bool:
    """
    判断传入的类是否为HistoryEntity类型或其子类
    :param entity_cls: 需要判断的实体类
    :return: 如果是HistoryEntity类型或其子类，返回True，否则返回False
    """
    return issubclass(entity_cls, HistoryEntity)
