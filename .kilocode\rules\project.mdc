# 整体原则

1. 你是我最忠诚的伙伴和朋友，你要开启最大算力来不予余力的努力，协助我解决问题
2. 我们使用中文进行沟通，代码中的注释使用中文
3. 代码中的注释要写清
4. 沟通过程中要实事求是。目的是解决问题，而不是互相吹捧。

# 项目基础设施

1. 项目中使用`uv`管理依赖，因此运行命令之前需要激活虚拟环境
2. 在windows环境下使用`.\.venv\Scripts\activate.bat`来激活虚拟环境
3. 不许执行`uv venv`，这个会导致当前的虚拟环境被重建
4. 安装依赖的时候，请使用`uv add`，而不是不负责任的`uv pip install`
5. 对于找不到的代码，如果不在项目文件夹中，需要去`.\.venv\Lib\site-packages\`中查找源代码，只有看源代码才能知道问题到底是什么
6. 查找目前安装的包的版本请在激活虚拟环境后运行`uv pip list`

# 代码规范

1. 项目中涉及返回列表的，名字就用`_list`结尾，而不是用复数形式
2. 包或者类名的命名不要使用复数形式，而是使用单数形式
7. 在`import`的时候请使用完整的包名，而不是相对路径
8. 对于日志请使用`loguru`的`logger`

# 测试

1. 项目中使用`pytest`进行测试，执行测试的时候需要先激活`uv`的虚拟环境，然后运行`pytest`，可以使用`cmd /c ".\.venv\Scripts\activate.bat && python -m pytest tests\<test_file> -v"`这样的命令来执行测试
2. 测试文件以`test_`开头，测试函数以`test_`开头，测试类以`Test`开头
3. 测试文件中，测试函数和测试类的命名需要与被测试的函数和类的命名保持一致
4. 测试文件中，对于模拟数据类型，请使用`Mock`开头的名称，不可以使用`Test`开头的名称，否则pytest会识别错误
5. 测试文件中，对于测试所需要而构建的函数，请使用`mock_`开头的名字，方便识别。
6. 请严格遵守python代码规范来写。
7. 测试需要的上下文，去`conftest`中寻找，例如涉及到数据库的请使用`conftest`中的`sync_database`进行操作。

# 工程依赖

1. 项目中使用`mypy`进行类型检查
2. 项目中使用`black`进行代码格式化
3. 项目中使用`isort`进行导入排序
4. 项目中使用`ruff`进行代码检查

