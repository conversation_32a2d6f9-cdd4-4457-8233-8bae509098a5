[database]
driver = "postgresql+psycopg2"
host = "************"
port = 5432
user = "infra_object_storage"
password = "infra_object_storage"
database = "infra_object_storage"
echo = true
# 连接池配置
pool_size = 5
max_overflow = 10
pool_timeout = 30
pool_recycle = 1800
pool_pre_ping = true
# PostgreSQL 特有的 JIT 配置
enable_jit = true

[storage_client]
helper_type = 'cos'

[cos]
#default_bucket = 'bjsxxyrs-1305036724'
default_bucket = 'dev-cos-1305036724'
secret_id = 'AKIDCOXVFoBew42qUu7FCyb2KPGusfGgu2yf'
secret_key = 'av4zkIhspLf8VineJk196uZ2HNNDuRo3'
region = 'ap-nanjing'
endpoint_url = 'https://cos.ap-nanjing.myqcloud.com'

[oss]
default_bucket = 'minio-py-1305036724'
access_key_id = 'LTAI4GFp1gCtrZETfLBbdXUw'
access_key_secret = '7jXI0TGtN8YekJ1h1mM8fmPPaRtcKE'
endpoint = 'oss-cn-shanghai.aliyuncs.com'

[s3]
aws_access_key_id = '********************'
aws_secret_access_key = 'mXE3SKxsAO09Y/Nwf/WshUIr//re5F0B7G3EtSqU'
default_bucket = 'qindingtech-bucket-dev'
region_name = 'ap-northeast-1'
endpoint_url = 'https://s3.ap-northeast-1.amazonaws.com'

[minio]
default_bucket = 'minio-test'
host = 'dev-minio.qindingtech.com'
port = 443
secure = true
access_key = 'admin'
secret_key = 'Pa55word'



