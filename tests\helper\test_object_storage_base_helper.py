# -*- coding: utf-8 -*-
# @Time    : 2024/6/19
# <AUTHOR> lxt
# @File    : test_object_storage_base_helper.py
import re

import pytest

from infra_object_storage.error import BucketNotExistedError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
)


class TestObjectStorageBaseHelper:
    """
    测试对象存储基类
    """

    def test_generate_storage_name(self):
        """
        测试生成文件名
        """
        # 测试带扩展名
        file_name_with_extension = "test.txt"
        generated_name_with_extension = ObjectStorageBaseHelper.generate_storage_name(
            file_name_with_extension
        )
        assert generated_name_with_extension.endswith(".txt")
        # 验证格式
        pattern_with_extension = r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-[a-zA-Z0-9]+\.txt$"
        assert re.match(pattern_with_extension, generated_name_with_extension)

        # 测试不带扩展名
        file_name_without_extension = "test"
        generated_name_without_extension = (
            ObjectStorageBaseHelper.generate_storage_name(file_name_without_extension)
        )
        assert "." not in generated_name_without_extension.split("-")[-1]
        # 验证格式
        pattern_without_extension = (
            r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-[a-zA-Z0-9]+$"
        )
        assert re.match(pattern_without_extension, generated_name_without_extension)

    def test_prepare_bucket_name(self):
        """
        测试准备篮子名称
        """
        # 测试提供了 bucket_name
        helper_with_default = ObjectStorageBaseHelper(default_bucket="default-bucket")
        assert (
            helper_with_default._prepare_bucket_name(bucket_name="test-bucket")
            == "test-bucket"
        )

        # 测试 bucket_name 为 None，使用默认值
        assert (
            helper_with_default._prepare_bucket_name(bucket_name=None)
            == "default-bucket"
        )

        # 测试 bucket_name 和 default_bucket 都为 None
        helper_without_default = ObjectStorageBaseHelper(default_bucket=None)
        with pytest.raises(BucketNotExistedError):
            helper_without_default._prepare_bucket_name(bucket_name=None)

    def test_get_default_bucket(self):
        """
        测试获取默认篮子
        """
        helper = ObjectStorageBaseHelper(default_bucket="my-default-bucket")
        assert helper.get_default_bucket() == "my-default-bucket"

    def test_remove_url_parameters(self):
        """
        测试移除url参数
        """
        # 测试移除查询参数
        url_with_query = "http://example.com/path?param1=value1&param2=value2"
        assert (
            ObjectStorageBaseHelper.remove_url_parameters(url_with_query)
            == "http://example.com/path"
        )

        # 测试移除片段标识符
        url_with_fragment = "http://example.com/path#section"
        assert (
            ObjectStorageBaseHelper.remove_url_parameters(url_with_fragment)
            == "http://example.com/path"
        )

        # 测试移除查询参数和片段标识符
        url_with_both = "http://example.com/path?param=value#section"
        assert (
            ObjectStorageBaseHelper.remove_url_parameters(url_with_both)
            == "http://example.com/path"
        )

        # 测试干净的url
        clean_url = "http://example.com/path"
        assert ObjectStorageBaseHelper.remove_url_parameters(clean_url) == clean_url