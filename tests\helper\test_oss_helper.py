import uuid
from time import sleep

import pytest
from oss2.exceptions import NoSuch<PERSON><PERSON>

from infra_object_storage.helper.oss_helper import OssHelper


@pytest.fixture
def temp_bucket_name(oss_helper: OssHelper):
    """
    创建一个临时的存储桶，用于测试
    """
    bucket_name = f"test-bucket-oss-{uuid.uuid4()}"
    oss_helper.make_bucket(bucket_name)
    yield bucket_name
    # 清理：删除存储桶内的所有对象后删除存储桶
    try:
        # oss需要先删除所有对象才能删除bucket
        object_keys = [obj for obj in oss_helper.list_objects(bucket_name)]
        if object_keys:
            oss_helper.delete_objects(object_keys, bucket_name)
        oss_helper.remove_bucket(bucket_name)
    except Exception as e:
        print(f"Error cleaning up bucket {bucket_name}: {e}")


def test_bucket_operations(oss_helper: OssHelper):
    """
    测试存储桶的创建、存在性检查和删除
    """
    bucket_name = f"test-bucket-ops-oss-{uuid.uuid4()}"
    
    # 初始状态检查
    assert not oss_helper.bucket_exists(bucket_name), "Bucket should not exist initially"
    
    # 创建存储桶
    oss_helper.make_bucket(bucket_name)
    # OSS 操作通常很快，但以防万一，可以稍作等待
    sleep(2)
    assert oss_helper.bucket_exists(bucket_name), "Bucket should exist after creation"
    
    # 删除存储桶
    oss_helper.remove_bucket(bucket_name)
    sleep(2)
    assert not oss_helper.bucket_exists(bucket_name), "Bucket should not exist after removal"


def test_object_operations(oss_helper: OssHelper, temp_bucket_name: str):
    """
    测试对象的上传、下载、存在性检查和删除
    """
    object_name = f"test-object-{uuid.uuid4()}.txt"
    object_data = b"This is a test file for OSS."
    
    # 初始状态检查
    assert not oss_helper.object_exists(object_name, temp_bucket_name), "Object should not exist initially"
    
    # 上传文件
    oss_helper.upload_object(object_name, object_data, temp_bucket_name)
    assert oss_helper.object_exists(object_name, temp_bucket_name), "Object should exist after upload"
    
    # 下载文件
    downloaded_data = oss_helper.download_object(object_name, temp_bucket_name)
    assert downloaded_data == object_data, "Downloaded data should match uploaded data"
    
    # 删除文件
    oss_helper.delete_object(object_name, temp_bucket_name)
    assert not oss_helper.object_exists(object_name, temp_bucket_name), "Object should not exist after deletion"


def test_get_file_url(oss_helper: OssHelper, temp_bucket_name: str):
    """
    测试生成预签名URL的功能
    """
    object_name = f"test-url-object-{uuid.uuid4()}.txt"
    object_data = b"Test file for URL generation."
    oss_helper.upload_object(object_name, object_data, temp_bucket_name)
    
    # 获取预签名URL
    url = oss_helper.get_object_url(object_name, temp_bucket_name, expires_seconds=60)
    assert isinstance(url, str)
    assert temp_bucket_name in url
    assert object_name in url
    assert "OSSAccessKeyId" in url
    
    oss_helper.delete_object(object_name, temp_bucket_name)


def test_list_and_delete_objects(oss_helper: OssHelper, temp_bucket_name: str):
    """
    测试列出和批量删除对象的功能
    """
    object_names = [f"test-list-obj-{i}-{uuid.uuid4()}.txt" for i in range(3)]
    for name in object_names:
        oss_helper.upload_object(name, b"list test", temp_bucket_name)
        
    # 列出对象
    listed_objects = list(oss_helper.list_objects(temp_bucket_name))
    assert all(name in listed_objects for name in object_names)
    
    # 批量删除对象
    oss_helper.delete_objects(object_names, temp_bucket_name)
    
    # 验证对象已被删除
    for name in object_names:
        assert not oss_helper.object_exists(name, temp_bucket_name)


def test_public_read_operations(oss_helper: OssHelper, temp_bucket_name: str):
    """
    测试设置对象为公开读取和获取ACL的功能
    """
    object_name = f"test-public-object-{uuid.uuid4()}.txt"
    oss_helper.upload_object(object_name, b"public read test", temp_bucket_name)
    
    # 设置为公开读取
    oss_helper.set_object_public_read(object_name, temp_bucket_name)
    
    # 获取ACL进行验证
    acl = oss_helper.get_object_acl(object_name, temp_bucket_name)
    assert acl.get("acl") == "public-read", "Object should be public-read after setting ACL"
    
    oss_helper.delete_object(object_name, temp_bucket_name)
def test_bucket_policy(oss_helper: OssHelper, temp_bucket_name: str):
    """Test bucket policy operations"""
    policy = {
        "Version": "1",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": ["oss:GetObject"],
                "Principal": ["*"],
                "Resource": [f"acs:oss:*:*:{temp_bucket_name}/*"]
            }
        ]
    }
    oss_helper.set_bucket_policy(policy, temp_bucket_name)
    
    retrieved_policy = oss_helper.get_bucket_policy(temp_bucket_name)
    assert retrieved_policy["Version"] == "1"
    assert len(retrieved_policy["Statement"]) == 1