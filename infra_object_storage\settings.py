"""
定义对象存储相关的配置类，使用 Pydantic 进行数据验证和设置管理。
"""

from typing import Annotated, List, Literal, Optional, Union

from pydantic import BaseModel, Field


class StorageAccount(BaseModel):
    """
    存储账户的基类。
    定义了所有存储账户通用的基本属性。
    """

    id: str = Field(..., description="账户的唯一标识符")
    provider: Literal["s3", "cos", "minio", "oss"] = Field(
        ..., description="存储服务提供商类型"
    )
    default_bucket: str = Field(default="", description="此账户下的默认存储桶名称")


class OssSetting(BaseModel):
    """
    阿里云对象存储 (OSS) 的特定配置。
    """

    access_key_id: str = Field(default="", description="OSS AccessKey ID")
    access_key_secret: str = Field(default="", description="OSS AccessKey Secret")
    endpoint: str = Field(default="", description="OSS 服务端点")


class OssAccount(StorageAccount, OssSetting):
    """
    OSS 存储账户的完整配置。
    继承自 `StorageAccount` 和 `OssSetting`。
    """

    provider: Literal["oss"] = "oss"  # type: ignore # 明确指定提供商为 "oss"


class CosSetting(BaseModel):
    """
    腾讯云对象存储 (COS) 的特定配置。
    """

    secret_id: str = Field(default="", description="COS Secret ID")
    secret_key: str = Field(default="", description="COS Secret Key")
    region: str = Field(default="", description="COS 区域")
    # 使用临时密钥需要传入Token，默认为空,可不填
    token: Optional[str] = Field(default=None, description="COS 临时密钥 Token (可选)")
    endpoint_url: Optional[str] = Field(
        default=None, description="COS 的自定义服务端点 URL (可选)"
    )


class CosAccount(StorageAccount, CosSetting):
    """
    COS 存储账户的完整配置。
    继承自 `StorageAccount` 和 `CosSetting`。
    """

    provider: Literal["cos"] = "cos"  # type: ignore # 明确指定提供商为 "cos"


class S3Setting(BaseModel):
    """
    AWS S3 兼容对象存储的特定配置。
    """

    aws_access_key_id: str = Field(default="", description="S3 Access Key ID")
    aws_secret_access_key: str = Field(default="", description="S3 Secret Access Key")
    region_name: str = Field(default="", description="S3 区域名称")
    endpoint_url: str = Field(default="", description="S3 服务端点 URL")
    verify: bool = Field(default=True, description="是否验证 SSL 证书")
    connect_timeout: int = Field(default=65535, description="连接超时时间 (秒)")
    read_timeout: int = Field(default=65535, description="读取超时时间 (秒)")


class S3Account(StorageAccount, S3Setting):
    """
    S3 存储账户的完整配置。
    继承自 `StorageAccount` 和 `S3Setting`。
    """

    provider: Literal["s3"] = "s3"  # type: ignore # 明确指定提供商为 "s3"


class MinioSetting(BaseModel):
    """
    Minio 对象存储的特定配置。
    """

    host: str = Field(default="127.0.0.1", description="Minio 服务器主机地址")
    port: int = Field(default=9000, description="Minio 服务器端口")
    secure: bool = Field(default=True, description="是否使用 HTTPS 连接")
    access_key: str = Field(default="", description="Minio Access Key")
    secret_key: str = Field(default="", description="Minio Secret Key")


class MinioAccount(StorageAccount, MinioSetting):
    """
    Minio 存储账户的完整配置。
    继承自 `StorageAccount` 和 `MinioSetting`。
    """

    provider: Literal["minio"] = "minio"  # type: ignore # 明确指定提供商为 "minio"


AnyStorageAccount = Annotated[
    Union[S3Account, CosAccount, MinioAccount, OssAccount],
    Field(discriminator="provider"),
]
"""
一个联合类型，表示任何支持的存储账户类型。
使用 `discriminator="provider"` 字段来区分不同的账户类型。
"""


class StorageSettings(BaseModel):
    """
    整个对象存储模块的全局设置。
    包含了默认账户ID和所有配置的存储账户列表。
    """

    default_account_id: str = Field(..., description="默认使用的存储账户ID")
    accounts: List[AnyStorageAccount] = Field(..., description="所有配置的存储账户列表")
