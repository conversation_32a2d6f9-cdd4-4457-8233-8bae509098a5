"""
配置类
"""

from typing import Optional

from pydantic import BaseModel, Field


class StorageClientSetting(BaseModel):
    """存储客户端配置"""

    helper_type: str = Field(default="s3")


class OssSetting(BaseModel):
    """阿里OSS配置"""

    default_bucket: str = Field(default="")
    access_key_id: str = Field(default="")
    access_key_secret: str = Field(default="")
    endpoint: str = Field(default="")


class CosSetting(BaseModel):
    """腾讯COS配置"""

    default_bucket: str = Field(default="")
    secret_id: str = Field(default="")
    secret_key: str = Field(default="")
    region: str = Field(default="")
    # 使用临时密钥需要传入Token，默认为空,可不填
    token: Optional[str] = Field(default=None)


class S3Setting(BaseModel):
    """S3配置"""

    default_bucket: str = Field(default="")
    aws_access_key_id: str = Field(default="")
    aws_secret_access_key: str = Field(default="")
    region_name: str = Field(default="")
    endpoint_url: str = Field(default="")
    verify: bool = Field(default=True)
    connect_timeout: int = Field(default=65535)
    read_timeout: int = Field(default=65535)


class MinioSetting(BaseModel):
    """Minio配置"""

    default_bucket: str = Field(default="")
    host: str = Field(default="127.0.0.1")
    port: int = Field(default=9000)
    secure: bool = Field(default=True)
    access_key: str = Field(default="")
    secret_key: str = Field(default="")
