"""
对象存储容器
"""

from dependency_injector import containers, providers
from infra_database.uow.session_proxy import SessionProxy

from infra_object_storage.helper.object_storage_client import ObjectStorageClient
from infra_object_storage.repository.object_storage_repository import (
    ObjectStorageRepository,
)
from infra_object_storage.service.object_storage_service import ObjectStorageService


class ObjectStorageContainer(containers.DeclarativeContainer):
    session_proxy = providers.Dependency(instance_of=SessionProxy)  # type: ignore

    # type: ignore
    object_storage_client = providers.Singleton(ObjectStorageClient)  # type: ignore

    object_storage_repository = providers.Factory(  # type: ignore
        ObjectStorageRepository, session=session_proxy
    )

    object_storage_service = providers.Factory(  # type: ignore
        ObjectStorageService,
        object_storage_repository=object_storage_repository,
        object_storage_client=object_storage_client,
    )
