"""
定义对象存储模块的依赖注入容器。
使用 `dependency_injector` 库来管理和提供对象存储相关的服务和客户端实例。
"""

from dependency_injector import containers, providers
from infra_database.uow.session_proxy import SessionProxy

from infra_object_storage.helper.object_storage_client import ObjectStorageClient
from infra_object_storage.repository.object_storage_repository import (
    ObjectStorageRepository,
)
from infra_object_storage.service.object_storage_service import ObjectStorageService
from infra_object_storage.settings import StorageSettings


class ObjectStorageContainer(containers.DeclarativeContainer):
    """
    对象存储模块的依赖注入容器。
    负责配置和提供对象存储客户端、仓库和服务等组件。
    """

    # 定义一个依赖提供者，用于注入数据库会话代理。
    # `SessionProxy` 是一个抽象的会话代理，具体的会话实现将在运行时提供。
    session_proxy = providers.Dependency(instance_of=SessionProxy)  # type: ignore

    # 定义一个依赖提供者，用于注入存储设置。
    # `StorageSettings` 包含了所有对象存储账户的配置信息。
    storage_settings = providers.Dependency(instance_of=StorageSettings)

    # 定义一个单例提供者，用于创建 `ObjectStorageClient` 实例。
    # `ObjectStorageClient` 负责管理不同存储提供商的客户端。
    # 它使用 `storage_settings` 中提供的账户信息和默认账户ID进行初始化。
    object_storage_client = providers.Singleton(
        ObjectStorageClient,
        accounts=storage_settings.provided.accounts,
        default_account_id=storage_settings.provided.default_account_id,
    )

    # 定义一个工厂提供者，用于创建 `ObjectStorageRepository` 实例。
    # `ObjectStorageRepository` 负责与数据库交互，管理文件信息。
    # 它依赖于 `session_proxy` 来进行数据库操作。
    object_storage_repository = providers.Factory(  # type: ignore
        ObjectStorageRepository, session=session_proxy
    )

    # 定义一个工厂提供者，用于创建 `ObjectStorageService` 实例。
    # `ObjectStorageService` 提供了高层次的对象存储操作接口。
    # 它依赖于 `object_storage_repository` 进行数据持久化，
    # 并依赖于 `object_storage_client` 进行实际的对象存储操作。
    object_storage_service = providers.Factory(  # type: ignore
        ObjectStorageService,
        object_storage_repository=object_storage_repository,
        object_storage_client=object_storage_client,
    )
