"""地理点类型"""

import binascii
from typing import Any

from infra_geo.geo_helper import str_is_wkb
from infra_geo.model.geo_point_model import GeoPointModel
from loguru import logger
from shapely import wkb, wkt
from shapely.geometry import Point
from shapely.geometry.base import BaseGeometry
from sqlalchemy.sql.type_api import UserDefinedType


class GeographyPointType(UserDefinedType):
    """
    地理点类型，用于处理 PostGIS 的 GEOMETRY(Point, 4326) 类型。

    这个自定义类型负责 Python 中的 GeoPointModel 对象与数据库中
    WKT (Well-Known Text) 或 WKB (Well-Known Binary) 格式之间的转换。

    - 写入数据库时 (`bind_processor`)：将 GeoPointModel 转换为 WKT 字符串。
    - 从数据库读取时 (`result_processor`)：将 WKB (通常是十六进制字符串) 或 WKT 格式的数据转换为 GeoPointModel 对象。
    """

    cache_ok = True  # 标记该类型可被 SQLAlchemy 缓存，因为它是不可变的。

    def get_col_spec(self, **kw: Any) -> str:
        """
        返回数据库中此类型的列定义。

        :param kw: SQLAlchemy 传递的额外关键字参数。
        :return: PostGIS 中地理点类型的 SQL 定义。
        """
        return "GEOMETRY(POINT, 4326)"

    def bind_processor(self, dialect: Any) -> Any:
        """
        定义如何将 Python 对象处理成数据库可接受的格式。

        :param dialect: 当前使用的数据库方言。
        :return: 一个处理函数，用于在 INSERT 或 UPDATE 语句中转换数据。
        """

        def process(value: Any) -> Any:
            """
            处理绑定值。

            :param value: 待写入数据库的 Python 对象。
            :return: 转换后的 WKT 字符串，或在值为 None 时返回 None。
            """
            if value is None:
                return None

            if isinstance(value, GeoPointModel):
                # 将 GeoPointModel 转换为 shapely.geometry.Point，再生成 WKT 字符串
                point = Point(value.longitude, value.latitude)
                return point.wkt

            # 如果是字符串，假定它已经是有效的 WKT/WKB，直接传递
            # 可以在这里添加验证逻辑，但通常为了性能会信任上层代码
            if isinstance(value, str):
                try:
                    # 尝试解析以验证格式，但不改变值
                    wkt.loads(value)
                    return value
                except Exception:
                    logger.warning(f"传入了无效的 WKT 字符串: {value}")
                    # 根据策略，可以选择返回 None、抛出异常或直接返回原值
                    return None

            logger.warning(f"未知的地理点类型: {type(value)}，值为: {value}")
            return None

        return process

    def result_processor(self, dialect: Any, coltype: Any) -> Any:
        """
        定义如何将从数据库读取的数据转换成 Python 对象。

        :param dialect: 当前使用的数据库方言。
        :param coltype: 列的类型信息。
        :return: 一个处理函数，用于在 SELECT 语句的结果中转换数据。
        """

        def process(value: Any) -> Any:
            """
            处理结果值。

            :param value: 从数据库返回的原始值 (通常是 WKB 的十六进制字符串)。
            :return: 转换后的 GeoPointModel 对象，或在值为 None 或无效时返回 None。
            """
            if not value:
                return None

            try:
                geometry: BaseGeometry
                if str_is_wkb(value):
                    # PostGIS 通常返回 EWKB (Extended WKB) 的十六进制表示
                    # 使用 binascii.unhexlify 将十六进制字符串转为二进制
                    geometry = wkb.loads(binascii.unhexlify(value))
                else:
                    # 如果不是 WKB，则尝试按 WKT 解析
                    geometry = wkt.loads(value)

                # 确保解析出的几何对象是 Point 类型
                if isinstance(geometry, Point):
                    return GeoPointModel(longitude=geometry.x, latitude=geometry.y)

                logger.warning(f"期望的几何类型是 Point，但实际为 {type(geometry).__name__}")
                return None

            except (ValueError, TypeError, binascii.Error) as e:
                # 捕获并记录解析过程中可能发生的各种错误
                logger.error(f"解析地理空间数据失败: {e}", value=value, exc_info=True)
                return None

        return process

    @property
    def python_type(self) -> Any:
        return GeoPointModel
