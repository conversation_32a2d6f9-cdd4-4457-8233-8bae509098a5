"""
XML字段类型
"""

from typing import Any, Optional, Union

from loguru import logger
from lxml import etree
from sqlalchemy.types import UserDefinedType


class XmlType(UserDefinedType):
    """
    为 SQLAlchemy 提供的自定义 XML 类型，用于 PostgreSQL 的 XML 数据类型。

    这个类型负责在 Python 的 lxml.etree._Element 对象（或 XML 字符串）
    与数据库存储的 XML 格式之间进行双向转换。

    - 写入数据库时 (`bind_processor`): 接受 lxml 元素或字符串，并将其转换为
      数据库可接受的字符串或字节串格式。
    - 从数据库读取时 (`result_processor`): 将数据库返回的 XML 字符串解析为
      一个 lxml.etree._Element 对象，方便在 Python 中进行操作。
    """

    cache_ok = True  # 标记该类型可被 SQLAlchemy 缓存

    def get_col_spec(self, **kw: Any) -> str:
        """返回数据库中此类型的列定义，即 "XML"."""
        return "XML"

    def bind_processor(self, dialect: Any) -> Any:
        """
        定义如何将 Python 对象处理成数据库可接受的格式。

        :param dialect: 当前使用的数据库方言。
        :return: 一个处理函数，用于在 INSERT 或 UPDATE 语句中转换数据。
        """

        def process(value: Optional[Union[str, etree._Element]]) -> Optional[Union[str, bytes]]:
            """
            处理绑定值。

            :param value: 待写入数据库的 Python 对象 (lxml 元素、字符串或 None)。
            :return: 转换后的字符串、字节串或 None。
            """
            if value is None:
                return None
            if isinstance(value, str):
                # 如果是字符串，直接返回，信任其为有效的 XML
                return value
            if isinstance(value, etree._Element):
                # 如果是 lxml 元素，序列化为字符串, 不需要XML声明
                return etree.tostring(value, encoding="unicode")
            # 对于其他意外的类型，记录警告并返回 None
            logger.warning(f"不兼容的 XML 类型: {type(value)}，值为: {value}")
            return None

        return process

    def result_processor(self, dialect: Any, coltype: Any) -> Any:
        """
        定义如何将从数据库读取的数据转换成 Python 对象。

        :param dialect: 当前使用的数据库方言。
        :param coltype: 列的类型信息。
        :return: 一个处理函数，用于在 SELECT 语句的结果中转换数据。
        """

        def process(value: Optional[str]) -> Optional[etree._Element]:
            """
            处理结果值。

            :param value: 从数据库返回的原始 XML 字符串。
            :return: 转换后的 lxml 元素对象，或在值为 None 或无效时返回 None。
            """
            if value is None:
                return None
            try:
                # 使用 XMLParser 去除空白文本节点，使解析出的树更干净
                # 不要使用 recover=True，因为我们需要在 XML 无效时严格失败
                parser = etree.XMLParser(remove_blank_text=True)
                # 将数据库返回的字符串（通常是 utf-8 编码）解析为 lxml 元素
                return etree.fromstring(value.encode("utf-8"), parser=parser)
            except etree.XMLSyntaxError:
                logger.exception("解析数据库中的 XML 数据失败", value=value)
                return None

        return process

    @property
    def python_type(self) -> Any:
        """声明此 SQLAlchemy 类型在 Python 中对应的主要类型。"""
        return etree._Element
