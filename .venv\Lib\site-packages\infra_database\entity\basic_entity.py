"""
带ID的基础实体模块

该模块定义了一个抽象的实体基类 `BasicEntity`，它在 `SqlalchemyEntity` 的基础上
增加了一个自动生成的 `id` 字段作为主键。
"""

from typing import TypeVar

from sqlalchemy import String, text
from sqlalchemy.orm import Mapped, mapped_column

from infra_database.entity.base_entity import SqlalchemyEntity


class BasicEntity(SqlalchemyEntity):
    """
    带ID的基础实体抽象类

    这是一个抽象基类 (`__abstract__ = True`)，为需要 UUID 主键的实体提供了一个标准实现。
    它继承自 `SqlalchemyEntity`，因此也具备了 `to_dict` 和 `from_dict` 的能力。

    所有需要自增 UUID 主键的业务实体都应继承此类。

    Attributes:
        id (str): 实体的主键。使用 PostgreSQL 的 `uuid_generate_v7()` 函数
                  在数据库层面自动生成，确保了ID的唯一性和有序性。
    """

    __abstract__ = True

    id: Mapped[str] = mapped_column(
        String(40),
        comment="实体的主键ID",
        server_default=text("uuid_generate_v7()"),
        primary_key=True,
    )


GenericBasicEntity = TypeVar("GenericBasicEntity", bound=BasicEntity)
