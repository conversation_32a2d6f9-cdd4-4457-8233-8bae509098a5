"""
对象存储服务
"""

from typing import List, Optional

from infra_basic.resource.resource_interface import Resource
from infra_database.model.transaction_model import SystemTransactionModel
from infra_utility.file_helper import checksum_by_bytes

from infra_object_storage.error import ObjectStorageError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
)
from infra_object_storage.helper.object_storage_client import ObjectStorageClient
from infra_object_storage.helper.object_storage_interface import ObjectStorageInterface
from infra_object_storage.model.file_info_model import FileExtInfoModel
from infra_object_storage.model.object_storage_raw_model import ObjectStorageRawModel
from infra_object_storage.repository.object_storage_repository import (
    ObjectStorageRepository,
)


class ObjectStorageService:
    """对象存储服务"""

    def __init__(
        self,
        object_storage_repository: ObjectStorageRepository,
        object_storage_client: ObjectStorageClient,
    ):
        self.__object_storage_repository = object_storage_repository
        self.__object_storage_client = object_storage_client

    def get_resource_related_file_list(
        self,
        resource: Resource,
        relationship: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> List[FileExtInfoModel]:
        """
        获取资源相关的文件
        """
        file_ext_list = self.__object_storage_repository.get_resource_related_file_list(
            resource=resource, relationship=relationship
        )
        for file_ext_info in file_ext_list:
            if not file_ext_info.account_id or not file_ext_info.object_name:
                continue
            helper = self.__object_storage_client.get_helper(file_ext_info.account_id)
            # 确保 object_name 不为 None，因为 get_file_url 需要 str 类型
            if file_ext_info.object_name:
                file_ext_info.url = helper.get_object_url(
                    object_name=file_ext_info.object_name,
                    bucket_name=file_ext_info.bucket_name,
                    expires_seconds=expires_seconds,
                )
        return file_ext_list

    def delete_file(self, file_id: str, transaction: SystemTransactionModel):
        """删除文件"""
        self.__object_storage_repository.delete_file(
            file_info_id=file_id, transaction=transaction
        )

    def unlink_file_and_resource(
        self,
        file_id: str,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
    ):
        """断开文件和资源的关联"""
        return self.__object_storage_repository.unlink_file_and_resource(
            file_info_id=file_id,
            resource=resource,
            relationship=relationship,
            transaction=transaction,
        )

    def get_file_url(self, file_id: str, expires_seconds: int = 60 * 60 * 24) -> str:
        """
        根据文件id获取文件下载链接
        """
        found_storage = self.__object_storage_repository.get_storage_by_file_info_id(
            file_info_id=file_id
        )
        if not found_storage or not found_storage.account_id:
            raise ObjectStorageError(
                f"storage for file_id[{file_id}] not found or account_id is missing"
            )

        helper = self.__object_storage_client.get_helper(found_storage.account_id)
        return helper.get_object_url(
            object_name=found_storage.object_name,
            bucket_name=found_storage.bucket_name,
            expires_seconds=expires_seconds,
        )

    def download_file(self, file_id: str) -> tuple[FileExtInfoModel, bytes]:
        """
        下载文件内容
        """
        found_storage = self.__object_storage_repository.get_storage_by_file_info_id(
            file_info_id=file_id
        )
        if not found_storage or not found_storage.account_id:
            raise ObjectStorageError(
                f"storage for file_id[{file_id}] not found or account_id is missing"
            )

        helper = self.__object_storage_client.get_helper(found_storage.account_id)
        file_blob = helper.download_object(
            object_name=found_storage.object_name, bucket_name=found_storage.bucket_name
        )

        file_info = self.__object_storage_repository.get_file_info_by_id(
            file_info_id=file_id
        )

        if not file_info:
            raise ObjectStorageError(f"file info for file_id[{file_id}] not found")

        # 确保 found_storage.id 不为 None
        if found_storage.id is None:
            raise ObjectStorageError(
                f"object storage raw id for file_id[{file_id}] is missing"
            )

        return (
            FileExtInfoModel(
                id=file_id,
                object_storage_raw_id=found_storage.id,
                account_id=found_storage.account_id,
                bucket_name=found_storage.bucket_name,
                object_name=found_storage.object_name,
                checksum=found_storage.checksum,
                size=found_storage.size,
                original_name=file_info.original_name,
                summary=file_info.summary,
            ),
            file_blob,
        )

    # pylint: disable=too-many-arguments
    def upload_file_with_resource(
        self,
        account_id: str,
        file_name: str,
        file_blob: bytes,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
        summary: Optional[str] = None,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ) -> FileExtInfoModel:
        """
        上传文件并与资源建立关系
        """
        file_info = self.upload_file(
            account_id=account_id,
            file_name=file_name,
            file_blob=file_blob,
            transaction=transaction,
            summary=summary,
            bucket_name=bucket_name,
            is_public=is_public,
        )
        if not file_info.id:
            raise ObjectStorageError("Failed to create file info record.")

        _ = self.link_file_and_resource(
            file_id=file_info.id,
            resource=resource,
            relationship=relationship,
            transaction=transaction,
        )
        return file_info

    # pylint: disable=too-many-arguments
    def upload_file(
        self,
        account_id: str,
        file_name: str,
        file_blob: bytes,
        transaction: SystemTransactionModel,
        summary: Optional[str] = None,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ) -> FileExtInfoModel:
        """
        插入文件并返回文件id
        """
        if not file_blob:
            raise ObjectStorageError(f"file {file_name} is empty")

        helper = self.__object_storage_client.get_helper(account_id)
        target_bucket_name = helper.get_target_bucket_name(bucket_name=bucket_name)

        upload_storage_raw = self._upload_storage_raw(
            helper=helper,
            account_id=account_id,
            file_name=file_name,
            file_blob=file_blob,
            bucket_name=target_bucket_name,
            is_public=is_public,
        )
        if not upload_storage_raw.id:
            raise ObjectStorageError("Failed to create object storage raw record.")

        file_id = self.__object_storage_repository.insert_file_info(
            object_storage_raw_id=upload_storage_raw.id,
            file_name=file_name,
            transaction=transaction,
            summary=summary,
        )

        result_url = helper.get_object_url(
            object_name=upload_storage_raw.object_name,
            bucket_name=target_bucket_name,
        )

        result = FileExtInfoModel(
            id=file_id,
            account_id=account_id,
            bucket_name=target_bucket_name,
            object_name=upload_storage_raw.object_name,
            checksum=upload_storage_raw.checksum,
            size=upload_storage_raw.size,
            url=result_url,
            object_storage_raw_id=upload_storage_raw.id,
            original_name=file_name,
            summary=summary,
        )
        return result

    def _upload_storage_raw(
        self,
        helper: ObjectStorageInterface,
        account_id: str,
        file_name: str,
        file_blob: bytes,
        bucket_name: str,
        is_public: bool = False,
    ) -> ObjectStorageRawModel:
        """
        上传物理文件
        """
        storage_name = ObjectStorageBaseHelper.generate_storage_name(file_name)
        check_sum = checksum_by_bytes(file_blob)
        file_size = len(file_blob)

        upload_storage_raw = ObjectStorageRawModel(
            account_id=account_id,
            bucket_name=bucket_name,
            object_name=storage_name,
            checksum=check_sum,
            size=file_size,
        )

        found_same_file = self.__object_storage_repository.found_same_storage_raw(
            check_storage_raw=upload_storage_raw
        )

        if not found_same_file:
            helper.upload_object(
                object_name=storage_name,
                object_data=file_blob,
                bucket_name=bucket_name,
                is_public=is_public,
            )
            upload_storage_id = (
                self.__object_storage_repository.insert_object_storage_raw(
                    storage_info=upload_storage_raw
                )
            )
            upload_storage_raw.id = upload_storage_id
        else:
            upload_storage_raw.id = found_same_file.id
            upload_storage_raw.object_name = found_same_file.object_name

        if is_public:
            helper.set_object_public_read(
                object_name=upload_storage_raw.object_name,
                bucket_name=bucket_name,
            )
        return upload_storage_raw

    def link_file_and_resource(
        self,
        file_id: str,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
    ) -> str:
        """关联文件与资源"""
        # 先检查文件是否存在
        file_info = self.__object_storage_repository.get_file_info_by_id(
            file_info_id=file_id
        )
        if not file_info:
            raise ObjectStorageError(f"file info for file_id[{file_id}] not found")

        if relationship is None:
            raise ValueError("relationship must be a non-empty string")

        return self.__object_storage_repository.link_file_and_resource(
            file_info_id=file_id,
            resource=resource,
            relationship=relationship,
            transaction=transaction,
        )
