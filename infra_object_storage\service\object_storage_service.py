"""
对象存储服务
"""

from typing import List, Optional

from infra_basic.resource.resource_interface import Resource
from infra_database.model.transaction_model import SystemTransactionModel
from infra_utility.file_helper import checksum_by_bytes

from infra_object_storage.error import ObjectStorageError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
)
from infra_object_storage.helper.object_storage_interface import ObjectStorageInterface
from infra_object_storage.model.file_info_model import FileExtInfoModel
from infra_object_storage.model.object_storage_raw_model import ObjectStorageRawModel
from infra_object_storage.repository.object_storage_repository import (
    ObjectStorageRepository,
)


class ObjectStorageService:
    """对象存储服务"""

    def __init__(
        self,
        object_storage_repository: ObjectStorageRepository,
        object_storage_client: ObjectStorageInterface,
    ):
        self.__object_storage_repository = object_storage_repository
        self.__object_storage_client = object_storage_client
        # 初始化当前账号get_current_object_storage_account_id
        self.__current_object_storage_account_id = (
            self.__init_object_storage_account_id()
        )

    def __init_object_storage_account_id(self):
        found_account = self.__object_storage_repository.found_account(
            check_info=self.__object_storage_client.get_account_info()
        )
        if not found_account:
            # 当account不存在的时候，则新建
            new_account_id = (
                self.__object_storage_repository.insert_object_storage_account(
                    account_info=self.__object_storage_client.get_account_info()
                )
            )
            return new_account_id
        return found_account.id

    def get_resource_related_file_list(
        self,
        resource: Resource,
        relationship: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> List[FileExtInfoModel]:
        """
        获取资源相关的文件
        """
        file_ext_list = self.__object_storage_repository.get_resource_related_file_list(
            resource=resource, relationship=relationship
        )
        for file_ext_info in file_ext_list:
            file_ext_info.url = self.__object_storage_client.get_file_url(
                object_name=file_ext_info.object_name,
                bucket_name=file_ext_info.bucket_name,
                expires_seconds=expires_seconds,
            )
        return file_ext_list

    def delete_file(self, file_id: str, transaction: SystemTransactionModel):
        """删除文件"""
        # 断开文件与资源的联系并文件删除
        self.__object_storage_repository.delete_file(
            file_id=file_id, transaction=transaction
        )

    def unlink_file_and_resource(
        self,
        file_id: str,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
    ):
        """断开文件和资源的关联"""
        return self.__object_storage_repository.unlink_file_and_resource(
            file_id=file_id,
            resource=resource,
            relationship=relationship,
            transaction=transaction,
        )

    def build_public_url(self, bucket_name: str, object_name: str) -> str:
        """
        生成公开链接url
        """
        return self.__object_storage_client.get_file_public_url(
            object_name=object_name, bucket_name=bucket_name
        )

    def build_url(
        self, bucket_name: str, object_name: str, expires_seconds: int = 60 * 60 * 24
    ) -> str:
        """
        生成url
        """
        return self.__object_storage_client.get_file_url(
            object_name=object_name,
            bucket_name=bucket_name,
            expires_seconds=expires_seconds,
        )

    def get_file_public_url(self, file_id: str) -> str:
        """
        根据文件id获取文件公开下载链接
        :param file_id:
        :return:
        """
        found_storage = self.__object_storage_repository.get_storage_by_file_id(
            file_id=file_id
        )
        if not found_storage:
            raise ObjectStorageError(f"storage id[{file_id}] not found")
        return self.build_public_url(
            object_name=found_storage.object_name, bucket_name=found_storage.bucket_name
        )

    def get_file_url(self, file_id: str, expires_seconds: int = 60 * 60 * 24) -> str:
        """
        根据文件id获取文件下载链接
        :param file_id:
        :param expires_seconds:
        :return:
        """
        found_storage = self.__object_storage_repository.get_storage_by_file_id(
            file_id=file_id
        )
        if not found_storage:
            raise ObjectStorageError(f"storage id[{file_id}] not found")
        return self.build_url(
            object_name=found_storage.object_name,
            bucket_name=found_storage.bucket_name,
            expires_seconds=expires_seconds,
        )

    # pylint: disable=too-many-arguments
    def upload_file_with_resource(
        self,
        file_name: str,
        file_blob: bytes,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
        summary: Optional[str] = None,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ) -> FileExtInfoModel:
        """
        上传文件并与资源建立关系
        :param file_name:
        :param file_blob:
        :param resource:
        :param relationship:
        :param transaction:
        :param summary:
        :param bucket_name:
        :param is_public:
        :return:
        """
        file_info = self.upload_file(
            file_name=file_name,
            file_blob=file_blob,
            transaction=transaction,
            summary=summary,
            bucket_name=bucket_name,
            is_public=is_public,
        )
        _ = self.link_file_and_resource(
            file_id=file_info.id,
            resource=resource,
            relationship=relationship,
            transaction=transaction,
        )
        return file_info

    # pylint: disable=too-many-arguments
    def upload_file(
        self,
        file_name: str,
        file_blob: bytes,
        transaction: SystemTransactionModel,
        summary: Optional[str] = None,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ) -> FileExtInfoModel:
        """
        插入文件并返回文件id
        :param file_name:
        :param file_blob:
        :param transaction:
        :param summary:
        :param bucket_name:
        :param is_public:
        :return: file info id
        """
        if not file_blob:
            raise ObjectStorageError(f"file {file_name} is empty")

        target_bucket_name = self.__object_storage_client.get_target_bucket_name(
            bucket_name=bucket_name
        )

        upload_storage_raw = self._upload_storage_raw(
            file_name=file_name,
            file_blob=file_blob,
            bucket_name=target_bucket_name,
            is_public=is_public,
        )
        file_id = self.__object_storage_repository.insert_file_info(
            storage_info_id=upload_storage_raw.id,
            file_name=file_name,
            transaction=transaction,
            summary=summary,
        )
        result_url = (
            self.build_public_url(
                bucket_name=target_bucket_name,
                object_name=upload_storage_raw.object_name,
            )
            if is_public
            else self.build_url(
                bucket_name=target_bucket_name,
                object_name=upload_storage_raw.object_name,
            )
        )

        result = FileExtInfoModel(
            id=file_id,
            bucket_name=target_bucket_name,
            object_name=upload_storage_raw.object_name,
            checksum=upload_storage_raw.checksum,
            size=upload_storage_raw.size,
            url=result_url,
            storage_info_id=upload_storage_raw.id,
            original_name=file_name,
            summary=summary,
        )
        return result

    def _upload_storage_raw(
        self,
        file_name: str,
        file_blob: bytes,
        bucket_name: str,
        is_public: bool = False,
    ) -> ObjectStorageRawModel:
        """
        上传物理文件
        :param file_name:
        :param file_blob:
        :param bucket_name:
        :return:
        """
        storage_name = ObjectStorageBaseHelper.generate_storage_name(file_name)
        object_storage_account_id = self.__current_object_storage_account_id
        check_sum = checksum_by_bytes(file_blob)
        file_size = len(file_blob)
        upload_storage_raw = ObjectStorageRawModel(
            object_storage_account_id=object_storage_account_id,
            bucket_name=bucket_name,
            object_name=storage_name,
            checksum=check_sum,
            size=file_size,
        )
        if is_public:
            # 对于公开文件，不需要检查是否已经存在
            self.__object_storage_client.upload_file(
                object_name=storage_name,
                object_data=file_blob,
                bucket_name=bucket_name,
                is_public=is_public,
            )
            upload_storage_id = (
                self.__object_storage_repository.insert_object_storage_raw(
                    storage_info=upload_storage_raw
                )
            )
            upload_storage_raw.id = upload_storage_id
        else:
            found_same_file = self.__object_storage_repository.found_same_storage_raw(
                check_storage_raw=upload_storage_raw
            )
            # 检查文件是否已经存在，不存在的话则保存
            if not found_same_file:
                # 没有找到对应的文件
                self.__object_storage_client.upload_file(
                    object_name=storage_name,
                    object_data=file_blob,
                    bucket_name=bucket_name,
                    is_public=is_public,
                )
                upload_storage_id = (
                    self.__object_storage_repository.insert_object_storage_raw(
                        storage_info=upload_storage_raw
                    )
                )
                upload_storage_raw.id = upload_storage_id
            else:
                upload_storage_raw.id = found_same_file.id
                upload_storage_raw.object_name = found_same_file.object_name
        return upload_storage_raw

    def link_file_and_resource(
        self,
        file_id: str,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
    ) -> str:
        """关联文件与资源"""
        return self.__object_storage_repository.link_file_and_resource(
            file_id=file_id,
            resource=resource,
            relationship=relationship,
            transaction=transaction,
        )
