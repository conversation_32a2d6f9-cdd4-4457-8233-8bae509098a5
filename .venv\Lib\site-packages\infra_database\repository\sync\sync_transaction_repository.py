"""
同步事务仓库

"""

from typing import List

from loguru import logger
from sqlalchemy.exc import SQLAlchemyError

from infra_database.entity.transaction_entity import SystemTransactionEntity
from infra_database.error import EntityError
from infra_database.model.transaction_model import SystemTransactionModel
from infra_database.repository.sync.sync_base_repository import SyncBaseRepository


class SyncTransactionRepository(SyncBaseRepository):
    """
    同步事务仓库，用于持久化事务日志。
    """

    def save_transaction_logs(self, logs: List[SystemTransactionModel]) -> None:
        """
        批量保存事务日志。

        这个方法会将提供的所有日志模型转换为实体，并添加到当前 Session 中，
        以便在父级工作单元（UoW）提交时一并持久化。

        参数:
            logs: 一个包含 SystemTransactionModel 实例的列表。

        异常:
            EntityError: 如果模型转换或数据库会话操作失败。
        """
        if not logs:
            logger.debug("事务日志列表为空，无需保存。")
            return

        try:
            entities = [SystemTransactionEntity.from_model(log) for log in logs]
            self.session.add_all(entities)
            logger.info(f"已将 {len(entities)} 条事务日志添加到会话中。")
        except SQLAlchemyError as e:
            logger.error(f"添加事务日志到会话时发生数据库错误: {e}")
            raise EntityError(f"添加事务日志到会话时发生数据库错误: {e}") from e
        except Exception as e:
            logger.error(f"从模型转换事务日志实体时发生未知错误: {e}")
            raise EntityError(f"从模型转换事务日志实体时发生未知错误: {e}") from e
