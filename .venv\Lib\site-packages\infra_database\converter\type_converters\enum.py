"""
枚举类型转换器

这个模块提供了枚举类型和字符串之间的转换功能。
"""

import types
from enum import Enum
from typing import Any, Optional, Type, TypeVar, Union, get_args, get_origin

E = TypeVar('E', bound=Enum)


def _get_union_members(type_hint: Any) -> list[Any]:
    """
    从类型提示中提取成员。如果类型是 Union（或 | 语法），则返回其所有成员的列表。
    否则，返回包含该类型本身的列表。
    """
    # In Python 3.10+, `str | int` creates a `types.UnionType`
    is_pipe_union = hasattr(types, 'UnionType') and isinstance(type_hint, types.UnionType)

    if get_origin(type_hint) is Union or is_pipe_union:
        return list(get_args(type_hint))

    return [type_hint]


def str_to_enum(value: Optional[str], enum_type: Type[E]) -> Optional[E]:
    """将字符串转换为枚举值，使用value进行匹配

    Args:
        value: 要转换的字符串值
        enum_type: 目标枚举类型

    Returns:
        Optional[E]: 转换后的枚举值，如果输入为None或无法匹配则返回None

    Raises:
        TypeError: 如果 enum_type 不是一个有效的枚举类型。
    """
    if value is None:
        return None

    if not (isinstance(enum_type, type) and issubclass(enum_type, Enum)):
        raise TypeError(f"'{enum_type.__name__}' is not an Enum type.")

    # 遍历所有枚举值，查找匹配的value
    for enum_member in enum_type:
        if str(enum_member.value) == value:
            return enum_member
    return None


def enum_to_str(value: Optional[Enum]) -> Optional[str]:
    """将枚举值转换为字符串

    Args:
        value: 要转换的枚举值

    Returns:
        Optional[str]: 转换后的字符串（枚举的value），如果输入为None则返回None
    """
    if value is None:
        return None
    return str(value.value)


def is_enum_field(field_type: Any) -> bool:
    """判断字段类型是否是（或包含）枚举类型

    Args:
        field_type: 字段类型

    Returns:
        bool: 如果是枚举类型则返回True，否则返回False
    """
    for t in _get_union_members(field_type):
        # issubclass 的第一个参数必须是 type
        if isinstance(t, type) and issubclass(t, Enum):
            return True
    return False


def get_enum_type_from_field(field_type: Any) -> Optional[Type[Enum]]:
    """从字段类型中获取第一个找到的枚举类型

    Args:
        field_type: 字段的类型提示

    Returns:
        Optional[Type[Enum]]: 如果字段是枚举类型，则返回枚举类型；否则返回None
    """
    for t in _get_union_members(field_type):
        if isinstance(t, type) and issubclass(t, Enum):
            return t
    return None
