import uuid
from infra_basic.resource.basic_resource import BasicResource
from infra_database.uow.sync_sql_alchemy_unit_of_work import SyncSqlAlchemyUnitOfWork
from infra_object_storage.helper.object_storage_interface import AccountInfo
from infra_object_storage.model.object_storage_account_model import ObjectStorageAccountModel
from infra_object_storage.object_storage_container import ObjectStorageContainer
from infra_object_storage.repository.object_storage_repository import ObjectStorageRepository


def test_init_repo(prepare_storage_container: ObjectStorageContainer):
    repo: ObjectStorageRepository = prepare_storage_container.object_storage_repository()
    assert repo is not None


def test_account_action(prepare_storage_container: ObjectStorageContainer):
    uow: SyncSqlAlchemyUnitOfWork = prepare_storage_container.uow()
    with uow:
        repo: ObjectStorageRepository = prepare_storage_container.object_storage_repository()

        # 准备测试数据
        account_info = AccountInfo(
            type="test_type",
            site="test_site",
            unified_id=f"test_id_{uuid.uuid4()}",
        )

        # 测试插入
        account_model = ObjectStorageAccountModel(**account_info.model_dump())
        account_id = repo.insert_object_storage_account(account_model)
        assert account_id is not None

        # 测试查找
        found_account = repo.found_account(account_info)
        assert found_account is not None
        assert found_account.id == account_id
        assert found_account.type == account_info.type
        assert found_account.site == account_info.site
        assert found_account.unified_id == account_info.unified_id

        # 测试查找不存在的账户
        not_exist_account_info = AccountInfo(
            type="not_exist_type",
            site="not_exist_site",
            unified_id="not_exist_id",
        )
        found_not_exist_account = repo.found_account(not_exist_account_info)
        assert found_not_exist_account is None