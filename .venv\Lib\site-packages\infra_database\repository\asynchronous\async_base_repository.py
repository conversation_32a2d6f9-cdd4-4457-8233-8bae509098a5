"""
基本异步仓库类
提供基本的数据库操作功能
"""

from typing import Any, Dict, List, Optional, Type

from infra_basic.model.base_plus_model import GenericBaseModel
from loguru import logger
from pydantic import BaseModel
from sqlalchemy import delete, select, text, update
from sqlalchemy.ext.asyncio import AsyncSession

from infra_database.converter.convert_helper import (
    model_to_flat_dict,
    prepare_dict_for_entity,
    prepare_dict_for_model,
)
from infra_database.entity.base_entity import GenericSqlalchemyEntity
from infra_database.error import (
    EntityError,
    EntityNotFoundError,
    SQLExecutionError,
    SQLInjectionError,
)
from infra_database.model.database_query_model import (
    FilterCondition,
    OrderCondition,
    PageFilterParams,
    PageInitParams,
)
from infra_database.model.pagination_carrier import PaginationCarrier
from infra_database.repository.orm_helper import (
    OPERATOR_MAP,
    get_entity_table_name,
    prepare_entity_by_dict,
    prepare_update_data,
)
from infra_database.repository.raw_sql_helper import (
    build_bulk_insert_sql,
    build_extra_query_clause,
    build_full_text_search_clause,
    build_order_by_clause,
    build_pagination_clause,
    build_where_clause,
    cursor_result_to_list,
)


class AsyncBaseRepository:
    """基本异步仓库类"""

    def __init__(self, session: AsyncSession):
        """初始化"""
        self.session = session

    def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        if self.session is None:
            raise ValueError("Session has not been set in the repository.")
        return self.session

    async def _execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行sql语句，并返回字典列表
        :param sql: SQL语句
        :param params: SQL参数
        :return: 查询结果字典列表
        :raises SQLInjectionError: 如果检测到SQL注入风险
        :raises SQLExecutionError: 如果SQL执行失败
        """
        try:
            stmt = text(sql)
            logger.debug(f"execute_sql: {sql} <= {params}")
            params = {} if params is None else params
            result = await self.session.execute(statement=stmt, params=params)
            return cursor_result_to_list(result)
        except SQLInjectionError:
            raise
        except Exception as e:
            logger.error(f"执行SQL失败: {sql}, 参数: {params}, 错误: {e}")
            raise SQLExecutionError(f"执行SQL失败: {e}") from e

    async def _fetch_count(self, sql: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        获取对应sql和参数的记录总数
        :param sql: SQL语句
        :param params: SQL参数
        :return: 记录总数，若无结果则返回0
        :raises SQLInjectionError: 如果检测到SQL注入风险
        :raises SQLExecutionError: 如果SQL执行失败
        """
        try:
            stmt = text(f"select count(0) from ({sql}) _count_subquery")
            logger.debug(f"fetch_count: {sql} <= {params}")
            count = await self.session.scalar(stmt, params=params)
            # 若无结果则返回0，保证类型安全
            result = 0 if count is None else int(count)
            logger.debug(f"fetch_count result: {result}")
            return result
        except SQLInjectionError:
            raise
        except Exception as e:
            logger.error(f"获取记录总数失败: {sql}, 参数: {params}, 错误: {e}")
            raise SQLExecutionError(f"获取记录总数失败: {e}") from e

    def _build_sqlalchemy_select(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        params: Optional[List[FilterCondition]] = None,
    ):
        """
        根据输入的数据库类及查询条件构建查询
        :param entity_cls: 实体类
        :param params: 过滤条件列表
        :return: SQLAlchemy查询对象
        """
        stmt = select(entity_cls)
        if not params:
            return stmt

        for fc in params:
            column_name = fc.column_name
            operator = fc.operator
            value = fc.value

            if not hasattr(entity_cls, column_name):
                logger.warning(f"实体类{entity_cls.__name__}无字段{column_name}, 跳过该条件")
                continue

            column = getattr(entity_cls, column_name)

            if operator not in OPERATOR_MAP:
                logger.warning(f"不支持的操作符: {operator}，跳过该条件")
                continue

            stmt = stmt.where(OPERATOR_MAP[operator](column, value))

        return stmt

    async def _delete_entity_by_params(
        self, entity_cls: Type[GenericSqlalchemyEntity], params: List[FilterCondition]
    ) -> int:
        """
        【高危操作】根据参数删除实体，禁止无条件删除！
        :param entity_cls: 实体类
        :param params: 过滤条件列表，不能为空，否则抛出异常
        :return: 实际删除的记录数
        :raises ValueError: 若params为空，禁止全表删除
        :raises EntityError: 删除过程中发生异常
        """
        if not params:
            logger.error(f"禁止无条件删除实体: {entity_cls.__name__}，params为空")
            raise ValueError(f"禁止无条件删除实体: {entity_cls.__name__}，params不能为空")
        try:
            logger.info(f"准备删除实体: {entity_cls.__name__}，条件: {params}")
            stmt = delete(entity_cls)
            for fc in params:
                column = getattr(entity_cls, fc.column_name)
                stmt = stmt.where(OPERATOR_MAP[fc.operator](column, fc.value))

            result = await self.session.execute(stmt)
            deleted_count = result.rowcount
            logger.info(f"实体删除完成: {entity_cls.__name__}，条件: {params}，删除数量: {deleted_count}")
            return deleted_count
        except Exception as e:
            logger.error(f"删除实体失败: {entity_cls.__name__}，条件: {params}，错误: {e}")
            raise EntityError(f"删除实体失败: {entity_cls.__name__}，错误: {e}") from e

    async def __insert_entity(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],  # type: ignore
        dict_data: Dict[str, Any],
    ) -> GenericSqlalchemyEntity:
        """
        插入实体到数据库
        :param entity_cls: 要插入的实体类
        :param dict_data: 要插入的数据
        :return: 插入后的实体对象
        :raises EntityError: 插入过程中发生异常
        """

        try:
            entity = prepare_entity_by_dict(entity_cls=entity_cls, dict_data=dict_data)
            self.session.add(entity)
            logger.debug(f"实体已添加到会话: {entity_cls.__name__}, 数据: {dict_data}")
            return entity
        except Exception as e:
            logger.error(f"插入实体失败: {entity_cls.__name__}, 数据: {dict_data}, 错误: {e}")
            raise EntityError(f"插入实体失败: {entity_cls.__name__}, 错误: {e}") from e

    async def _insert_entity_by_dict_get_entity(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        dict_data: Dict[str, Any],
    ) -> GenericSqlalchemyEntity:
        """
        根据字典信息来插入数据库数据（内部方法）
        :param entity_cls: 实体类
        :param dict_data: 字段数据字典
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        logger.info(f"准备插入实体: {entity_cls.__name__}, 数据: {dict_data}")
        try:
            # 通过字典准备实体对象
            entity_dict = prepare_dict_for_entity(entity_cls=entity_cls, dict_data=dict_data)
            return await self.__insert_entity(entity_cls=entity_cls, dict_data=entity_dict)
        except Exception as e:
            logger.error(f"插入实体失败: {entity_cls.__name__}, 数据: {dict_data}, 错误: {e}")
            raise EntityError(f"插入实体失败: {entity_cls.__name__}, 错误: {e}") from e

    async def _insert_entity_by_model_get_entity(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        model_data: BaseModel,
    ) -> GenericSqlalchemyEntity:
        """
        根据model信息来插入数据库数据（内部方法）
        :param entity_cls: 实体类
        :param model_data: Pydantic模型数据
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        logger.info(f"准备插入实体: {entity_cls.__name__}, 模型数据: {model_data}")
        try:
            # 将model转换为字典
            dict_data = model_to_flat_dict(model_data)
            # 调用字典版本的插入方法
            return await self._insert_entity_by_dict_get_entity(entity_cls=entity_cls, dict_data=dict_data)
        except Exception as e:
            logger.error(f"插入实体失败: {entity_cls.__name__}, 模型数据: {model_data}, 错误: {e}")
            raise EntityError(f"插入实体失败: {entity_cls.__name__}, 错误: {e}") from e

    async def _insert_entity_by_dict(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        dict_data: Dict[str, Any],
    ) -> GenericSqlalchemyEntity:
        """
        根据字典信息来插入数据库数据，并返回实体对象。
        注意：此方法不会返回数据库生成的主键，因为flush操作由UOW管理。
        实体的主键将在UOW提交后被填充。
        :param entity_cls: 实体类
        :param dict_data: 字段数据字典
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        entity = await self._insert_entity_by_dict_get_entity(entity_cls=entity_cls, dict_data=dict_data)
        return entity

    async def _insert_entity_by_model(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        model_data: BaseModel,
    ) -> GenericSqlalchemyEntity:
        """
        根据model信息来插入数据库数据，并返回实体对象。
        注意：此方法不会返回数据库生成的主键，因为flush操作由UOW管理。
        实体的主键将在UOW提交后被填充。
        :param entity_cls: 实体类
        :param model_data: Pydantic模型数据
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        # 直接将model_data转为字典，调用_insert_entity_by_dict实现插入
        dict_data = model_to_flat_dict(model_data)
        return await self._insert_entity_by_dict(entity_cls=entity_cls, dict_data=dict_data)

    async def _update_entity_by_dict(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        update_data: Dict[str, Any],
        filter_params: List[FilterCondition],
        limited_col_list: Optional[List[str]] = None,
    ) -> int:
        """
        根据参数更新数据库，返回影响行数
        :param entity_cls:
        :param update_data:
        :param filter_params:
        :param limited_col_list:当申明的时候只更新字段内的，否则全更新
        :return:
        """
        entity_dict = prepare_dict_for_entity(entity_cls=entity_cls, dict_data=update_data)
        update_data = prepare_update_data(
            entity_cls=entity_cls,
            update_data=entity_dict,
            limited_col_list=limited_col_list,
        )

        stmt = update(entity_cls)
        for fc in filter_params:
            column = getattr(entity_cls, fc.column_name)
            stmt = stmt.where(OPERATOR_MAP[fc.operator](column, fc.value))

        stmt = stmt.values(**update_data)
        result = await self.session.execute(stmt)
        effected_rows = result.rowcount
        if effected_rows == 0:
            # 将 List[FilterCondition] 转为 Dict[str, Any]
            filter_dict = {fc.column_name: fc.value for fc in filter_params}
            raise EntityNotFoundError(cls=entity_cls, params=filter_dict)
        return effected_rows

    async def _update_entity_by_model(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        update_model: BaseModel,
        filter_params: List[FilterCondition],
        limited_col_list: Optional[List[str]] = None,
    ) -> int:
        """
        根据model更新数据库，返回影响行数
        :param entity_cls:
        :param update_model:
        :param filter_params:
        :param limited_col_list:当申明的时候只更新字段内的，否则全更新
        :return:
        """
        return await self._update_entity_by_dict(
            entity_cls=entity_cls,
            update_data=model_to_flat_dict(update_model),
            filter_params=filter_params,
            limited_col_list=limited_col_list,
        )

    async def _fetch_list(
        self,
        source_sql: str,
        filter_condition_list: Optional[List[FilterCondition]] = None,
        order_condition_list: Optional[List[OrderCondition]] = None,
        offset: int = 0,
        limit: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        根据过滤条件和排序条件查询任意子查询/表/视图数据，返回字典列表，支持分页
        :param source_sql: 子查询SQL或表名（如 'SELECT ... FROM ...' 或 'table_name'）
        :param filter_condition_list: 过滤条件列表
        :param order_condition_list: 排序条件列表
        :param offset: 偏移量
        :param limit: 返回数量
        :return: 查询结果的字典列表
        :raises ValueError: source_sql为空或非法
        """
        if not source_sql or not isinstance(source_sql, str):
            logger.error("source_sql不能为空，且必须为字符串")
            raise ValueError("source_sql不能为空，且必须为字符串")
        # 判断是否为简单表名（不含空格和括号），否则视为select语句
        if (" " in source_sql) or ("(" in source_sql) or ("SELECT" in source_sql.upper()):
            from_sql = f"({source_sql}) AS t"
        else:
            from_sql = source_sql
        # 构建where子句
        where_clause, params = build_where_clause(filter_condition_list or [])
        where_sql = f"WHERE {where_clause}" if where_clause != "1=1" else ""

        # 构建order by子句
        order_sql = build_order_by_clause(order_condition_list)

        # 构建分页
        limit_offset_sql = ""
        if limit is not None:
            limit_offset_sql = build_pagination_clause(limit, offset)
        # 拼接完整SQL
        sql = f"SELECT * FROM {from_sql} {where_sql} {order_sql} {limit_offset_sql}"
        logger.debug(f"_fetch_list SQL: {sql} <= {params}")
        stmt = text(sql)
        result = await self.session.execute(stmt, params=params)
        return cursor_result_to_list(result)

    async def bulk_insert(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        dict_list: List[Dict[str, Any]],
    ) -> List[Any]:
        """
        批量插入数据，充分利用PostgreSQL的SQL语法，返回插入后的id列表
        :param entity_cls: 实体类，必须有__table__属性
        :param dict_list: 待插入的数据字典列表
        :return: 插入后主键id的列表
        :raises TypeError: entity_cls无__table__属性
        :raises SQLExecutionError: 插入过程中发生异常
        """
        try:
            # 若数据为空，直接返回空列表
            if not dict_list:
                return []

            # 构建SQL和参数
            table_name = get_entity_table_name(entity_cls)
            stmt, params = build_bulk_insert_sql(table_name=table_name, dict_list=dict_list)
            if not stmt.text:
                return []

            # 执行SQL
            # 使用 session.scalars 更符合 SQLAlchemy 2.0 的风格，并且能更好地处理 RETURNING
            result = await self.session.execute(stmt, params)
            id_list = [row[0] for row in result.fetchall()]
            # flush() is managed by UoW
            return id_list
        except Exception as e:
            logger.error(f"批量插入数据失败: {entity_cls.__name__}, 数据: {dict_list}, 错误: {e}")
            raise SQLExecutionError(f"批量插入数据失败: {e}") from e

    async def _fetch_first(
        self,
        sql: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
         获取对应sql和参数的对应第一条数据
        :param sql:
        :param params:
        :return:
        """
        sql_stmt = f"select * from ({sql}) first_only limit 1"
        params = {} if params is None else params
        result = await self._execute_sql(sql=sql_stmt, params=params)

        if result and len(result) > 0:
            return result[0]
        return None

    async def _fetch_first_to_model(
        self,
        model_cls: Type[GenericBaseModel],
        sql: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> Optional[GenericBaseModel]:
        """
         获取对应sql和参数的所有记录
        :param model_cls:
        :param sql:
        :param params:
        :return:
        """
        first_result = await self._fetch_first(sql=sql, params=params)
        if first_result:
            model_dict = prepare_dict_for_model(model_cls=model_cls, dict_data=first_result)
            # 使用model_validate保证Pydantic v2的默认值和类型校验
            return model_cls.model_validate(model_dict)
        return None

    async def _fetch_all_to_model(
        self,
        model_cls: Type[GenericBaseModel],
        sql: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[GenericBaseModel]:
        """
        获取对应sql和参数的所有记录
        :param model_cls: 模型类
        :param sql: SQL语句
        :param params: SQL参数
        :return: 模型对象列表
        """
        result_list = await self._execute_sql(sql=sql, params=params)
        # 使用model_validate保证Pydantic v2的默认值和类型校验
        return [
            model_cls.model_validate(prepare_dict_for_model(model_cls=model_cls, dict_data=result_dict))
            for result_dict in result_list
        ]

    async def _paginate(
        self,
        result_type: Type[GenericBaseModel],
        total_params: PageInitParams,
        page_params: PageFilterParams,
    ) -> PaginationCarrier[GenericBaseModel]:
        """获取分页结果

        Args:
            result_type: 结果模型类型
            total_params: 分页初始化参数
            page_params: 分页过滤参数

        Returns:
            PaginationCarrier: 分页结果载体
        """
        # 准备文本过滤列表
        search_segment_list = [x.strip() for x in page_params.search_text.split(" ") if x != ""]

        # 准备过滤sql及参数
        filter_sql, filter_para_dict = build_full_text_search_clause(
            search_segment_list=search_segment_list,
            filter_columns=total_params.filter_columns,
        )
        extra_sql, extra_para_dict = build_extra_query_clause(page_params.extra_params)

        sql_segment = f" AND {filter_sql}" if filter_sql else ""
        sql_segment += f" AND {extra_sql}" if extra_sql else ""
        execute_para_dict = filter_para_dict | extra_para_dict
        sql_filter_data = f"""select * from ({total_params.sql}) pqb
         where 1=1{sql_segment}
         {build_order_by_clause(total_params.order_columns)}
         {build_pagination_clause(page_params.page_size, page_params.page_index)}"""

        used_params = {} if total_params.params is None else total_params.params
        full_para_dict = used_params | execute_para_dict

        total_count = await self._fetch_count(sql=total_params.sql, params=total_params.params)
        filter_count = await self._fetch_count(
            sql=f"select * from ({total_params.sql}) pqb where 1=1 {sql_segment} ",
            params=full_para_dict,
        )
        data = await self._fetch_all_to_model(model_cls=result_type, sql=sql_filter_data, params=full_para_dict)

        return PaginationCarrier(
            search_text=page_params.search_text,
            total_count=total_count,
            filter_count=filter_count,
            page_index=page_params.page_index,
            page_size=page_params.page_size,
            draw=page_params.draw,
            data=data,
        )
