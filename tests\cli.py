import os
from typing import Optional

import click
from infra_basic.resource.basic_resource import BasicResource

from infra_object_storage.service.object_storage_service import ObjectStorageService
from tests.conftest import TestStubContainer, get_mock_app_settings


@click.group()
def cli():
    """CLI tool for object storage operations."""
    pass


@cli.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--account-id', '-a', help='Storage account ID to use')
@click.option('--resource-category', '-c', help='Resource category for association')
@click.option('--resource-id', '-i', help='Resource ID for association')
@click.option('--relationship', '-r', default='attachment', help='Relationship type')
@click.option('--summary', '-s', help='Summary description for the file')
@click.option('--public', '-p', is_flag=True, help='Make the file publicly accessible')
def upload(file_path: str, account_id: Optional[str], resource_category: Optional[str], 
           resource_id: Optional[str], relationship: str, summary: Optional[str], public: bool = False):
    """Upload a file to object storage."""
    # Initialize container
    container = TestStubContainer()
    container.init_resources()
    
    # Initialize services
    storage_service = ObjectStorageService(
        object_storage_repository=container.object_storage_container.object_storage_repository(),
        object_storage_client=container.object_storage_container.object_storage_client()
    )
    
    # Read file
    with open(file_path, 'rb') as f:
        file_blob = f.read()
    
    file_name = os.path.basename(file_path)

    handler=BasicResource(category='cli', id='cli')
    # Create transaction
    with container.uow() as uow:
        # Log the transaction with handler and action
        transaction = uow.log_transaction(
            handler=handler,
            action='file_upload'
        )
        
        # Associate with resource if provided
        if resource_category and resource_id:
            resource = BasicResource(category=resource_category, id=resource_id)
            file_info = storage_service.upload_file_with_resource(
                account_id=account_id,
                file_name=file_name,
                file_blob=file_blob,
                resource=resource,
                relationship=relationship,
                transaction=transaction,
                summary=summary or f"Uploaded via CLI: {file_name}",
                is_public=public
            )
        else:
            file_info = storage_service.upload_file(
                file_name=file_name,
                file_blob=file_blob,
                transaction=transaction,
                summary=summary or f"Uploaded via CLI: {file_name}",
                account_id=account_id,
                is_public=public
            )


@cli.command()
@click.argument('file_id')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
def download(file_id: str, output: Optional[str]):
    """Download a file from object storage."""
    # Initialize container
    container = TestStubContainer()
    container.init_resources()
    
    # Get services
    storage_service = container.object_storage_container.object_storage_service()
    
    # Create transaction
    with container.uow() as uow:
        # Log the transaction
        transaction = uow.log_transaction(
            handler=BasicResource(category='cli', id='cli'),
            action='file_download'
        )
        
        # Download file
        file_info, file_blob = storage_service.download_object(file_id=file_id)
    
    # Determine output path
    if not output:
        output = file_info.original_name
    
    # Write to file
    with open(output, 'wb') as f:
        f.write(file_blob)
    
    click.echo(f"File downloaded successfully to: {output}")


@cli.command()
@click.argument('file_id')
@click.option('--resource-category', '-c', required=True, help='Resource category for association')
@click.option('--resource-id', '-i', required=True, help='Resource ID for association')
@click.option('--relationship', '-r', default='attachment', help='Relationship type')
def link(file_id: str, resource_category: str, resource_id: str, relationship: str):
    """link a file with a resource."""
    # Initialize container
    container = TestStubContainer()
    container.init_resources()
    
    # Get services
    storage_service = container.object_storage_container.object_storage_service()

    # Create resource
    resource = BasicResource(category=resource_category, id=resource_id)
    
    # Associate file with resource
    with container.uow() as uow:
        transaction = uow.log_transaction(
            handler=BasicResource(category='cli', id='cli'),
            action='link_file_and_resource_with_transaction'
        )

        storage_service.link_file_and_resource(
            file_id=file_id,
            resource=resource,
            relationship=relationship,
            transaction=transaction
        )
    
    click.echo(f"File {file_id} associated with {resource_category}/{resource_id}")


@cli.command()
@click.option('--account-id', '-a', help='Storage account ID to list')
def list_accounts(account_id: Optional[str]):
    """List available storage accounts."""
    settings = get_mock_app_settings()
    accounts = settings.storage.accounts
    
    if account_id:
        account = next((acc for acc in accounts if acc.id == account_id), None)
        if account:
            click.echo(f"Account ID: {account.id}")
            click.echo(f"Provider: {account.provider}")
            click.echo(f"Default bucket: {account.default_bucket}")
        else:
            click.echo(f"Account {account_id} not found")
    else:
        click.echo("Available storage accounts:")
        for account in accounts:
            click.echo(f"- {account.id} ({account.provider})")


@cli.command()
@click.argument('file_id')
@click.option('--expires', '-e', type=int, default=86400, help='URL expiration time in seconds (default: 24 hours)')
@click.option('--open', '-o', is_flag=True, help='Open the URL in default browser')
def view_url(file_id: str, expires: int, open: bool):
    """Get a pre-signed URL for a file."""
    # Initialize container
    container = TestStubContainer()
    container.init_resources()
    
    # Get services
    storage_service = container.object_storage_container.object_storage_service()
    
    # Create transaction
    with container.uow() as uow:
        # Log the transaction
        transaction = uow.log_transaction(
            handler=BasicResource(category='cli', id='cli'),
            action='file_view_url'
        )
        
        # Get file URL
        url = storage_service.get_object_url(file_id=file_id, expires_seconds=expires)
    
    click.echo(f"File URL (expires in {expires} seconds):")
    click.echo(url)
    
    # Open in browser if requested
    if open:
        click.launch(url)


@cli.command()
@click.argument('file_id')
def delete(file_id: str):
    """Delete a file from object storage."""
    # Initialize container
    container = TestStubContainer()
    container.init_resources()
    
    # Get services
    storage_service = container.object_storage_container.object_storage_service()
    
    # Create transaction
    with container.uow() as uow:
        # Log the transaction
        transaction = uow.log_transaction(
            handler=BasicResource(category='cli', id='cli'),
            action='file_delete'
        )
        
        # Delete file
        storage_service.delete_file(file_id=file_id, transaction=transaction)
    
    click.echo(f"File {file_id} deleted successfully")


@cli.command()
@click.argument('file_id')
@click.option('--resource-category', '-c', required=True, help='Resource category')
@click.option('--resource-id', '-i', required=True, help='Resource ID')
@click.option('--relationship', '-r', default='attachment', help='Relationship type')
def unlink(file_id: str, resource_category: str, resource_id: str, relationship: str):
    """Unlink a file from a resource."""
    # Initialize container
    container = TestStubContainer()
    container.init_resources()
    
    # Get services
    storage_service = container.object_storage_container.object_storage_service()
    
    # Create resource
    resource = BasicResource(category=resource_category, id=resource_id)
    
    # Create transaction
    with container.uow() as uow:
        # Log the transaction
        transaction = uow.log_transaction(
            handler=BasicResource(category='cli', id='cli'),
            action='unlink_file_and_resource'
        )
        
        # Unlink file from resource
        storage_service.unlink_file_and_resource(
            file_id=file_id,
            resource=resource,
            relationship=relationship,
            transaction=transaction
        )
    
    click.echo(f"File {file_id} unlinked from {resource_category}/{resource_id}")


@cli.command()
@click.option('--resource-category', '-c', required=True, help='Resource category')
@click.option('--resource-id', '-i', required=True, help='Resource ID')
@click.option('--relationship', '-r', help='Filter by relationship type')
@click.option('--expires', '-e', type=int, default=86400, help='URL expiration time in seconds (default: 24 hours)')
def list_files(resource_category: str, resource_id: str, relationship: Optional[str], expires: int):
    """List files related to a resource."""
    # Initialize container
    container = TestStubContainer()
    container.init_resources()
    
    # Get services
    storage_service = container.object_storage_container.object_storage_service()
    
    # Create resource
    resource = BasicResource(category=resource_category, id=resource_id)
    
    # Get related files
    with container.uow() as uow:
        files = storage_service.get_resource_related_file_list(
            resource=resource,
            relationship=relationship,
            expires_seconds=expires
        )
    
    if not files:
        click.echo(f"No files found for {resource_category}/{resource_id}")
        return
    
    click.echo(f"Files related to {resource_category}/{resource_id}:")
    for file in files:
        click.echo(f"- ID: {file.id}")
        click.echo(f"  Name: {file.original_name}")
        click.echo(f"  Summary: {file.summary}")
        if file.url:
            click.echo(f"  URL: {file.url}")
        click.echo("")


if __name__ == '__main__':
    cli()