"""
这个文件演示了如何使用 `create_tables` fixture。
"""

from infra_database.database import SyncDatabase


def test_tables_are_created(create_tables: SyncDatabase):
    """
    这是一个演示测试。

    通过将 `create_tables` 作为参数，pytest 会在运行此测试之前
    自动执行 `create_tables` fixture，从而确保所有数据库表都已创建。
    """
    # `create_tables` fixture 在执行完毕后，会返回它所依赖的 `prepare_sync_database` 的结果，
    # 也就是一个 SyncDatabase 实例。我们可以用它来做一些简单的断言。
    assert create_tables is not None

    # 我们可以尝试获取一个会话，如果数据库和表都设置正确，这里应该不会出错。
    session_factory = create_tables.get_session_factory()
    session = session_factory()
    assert session is not None
    print("\n数据库会话创建成功，表明数据库连接和表结构已准备就绪。")
    session.close()