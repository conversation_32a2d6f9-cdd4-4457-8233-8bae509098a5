# 对象存储 CLI 工具使用指南

这个 CLI 工具提供了与对象存储系统交互的命令行接口，支持文件上传、下载、资源关联等操作。

## 安装依赖

确保已安装所需依赖：

```bash
# 激活虚拟环境
.\.venv\Scripts\activate.bat

# 安装依赖
uv sync --extra full --group dev
```

## 基本用法

CLI 工具位于 `tests/cli.py`，可以通过 Python 模块方式运行：

```bash
python -m tests.cli [命令] [参数]
```

## 可用命令

### 1. 上传文件

将文件上传到对象存储系统。

```bash
python -m tests.cli upload [文件路径] [选项]
```

**参数：**
- `文件路径`：要上传的文件的本地路径

**选项：**
- `--account-id`, `-a`：指定存储账户 ID
- `--resource-category`, `-c`：关联资源的类别
- `--resource-id`, `-i`：关联资源的 ID
- `--relationship`, `-r`：关系类型（默认：attachment）
- `--summary`, `-s`：文件描述摘要

**示例：**

```bash
# 简单上传
python -m tests.cli upload D:\images\photo.jpg

# 指定存储账户上传
python -m tests.cli upload D:\images\photo.jpg --account-id minio_test

# 上传并关联到资源
python -m tests.cli upload D:\images\photo.jpg --resource-category user --resource-id user123 --relationship avatar
```

### 2. 下载文件

从对象存储系统下载文件。

```bash
python -m tests.cli download [文件ID] [选项]
```

**参数：**
- `文件ID`：要下载的文件 ID

**选项：**
- `--output`, `-o`：输出文件路径（如果不指定，将使用原始文件名）

**示例：**

```bash
# 下载到原始文件名
python -m tests.cli download file_id_123

# 下载到指定路径
python -m tests.cli download file_id_123 --output D:\downloads\my_photo.jpg
```

### 3. 关联文件与资源

将已上传的文件关联到资源。

```bash
python -m tests.cli associate [文件ID] [选项]
```

**参数：**
- `文件ID`：要关联的文件 ID

**选项：**
- `--resource-category`, `-c`：资源类别（必需）
- `--resource-id`, `-i`：资源 ID（必需）
- `--relationship`, `-r`：关系类型（默认：attachment）

**示例：**

```bash
python -m tests.cli associate file_id_123 --resource-category product --resource-id product456 --relationship image
```

### 4. 列出存储账户

列出可用的存储账户信息。

```bash
python -m tests.cli list-accounts [选项]
```

**选项：**
- `--account-id`, `-a`：指定要查看详情的账户 ID

**示例：**

```bash
# 列出所有账户
python -m tests.cli list-accounts

# 查看特定账户详情
python -m tests.cli list-accounts --account-id minio_test
```

### 5. 获取文件URL

获取文件的预签名URL，可用于临时访问或分享。

```bash
python -m tests.cli view-url [文件ID] [选项]
```

**参数：**
- `文件ID`：要获取URL的文件ID

**选项：**
- `--expires`, `-e`：URL过期时间（秒），默认为24小时（86400秒）
- `--open`, `-o`：在默认浏览器中打开URL（标志选项）

**示例：**

```bash
# 获取默认24小时有效的URL
python -m tests.cli view-url file_id_123

# 获取自定义过期时间（1小时）的URL
python -m tests.cli view-url file_id_123 --expires 3600

# 获取URL并在浏览器中打开
python -m tests.cli view-url file_id_123 --open
```

### 6. 删除文件

从对象存储系统中删除文件。

```bash
python -m tests.cli delete [文件ID]
```

**参数：**
- `文件ID`：要删除的文件 ID

**示例：**

```bash
python -m tests.cli delete file_id_123
```

### 7. 解除文件与资源的关联

解除已上传文件与资源的关联关系。

```bash
python -m tests.cli unlink [文件ID] [选项]
```

**参数：**
- `文件ID`：要解除关联的文件 ID

**选项：**
- `--resource-category`, `-c`：资源类别（必需）
- `--resource-id`, `-i`：资源 ID（必需）
- `--relationship`, `-r`：关系类型（默认：attachment）

**示例：**

```bash
python -m tests.cli unlink file_id_123 --resource-category product --resource-id product456 --relationship image
```

### 8. 列出资源关联的文件

列出与特定资源关联的所有文件。

```bash
python -m tests.cli list-files [选项]
```

**选项：**
- `--resource-category`, `-c`：资源类别（必需）
- `--resource-id`, `-i`：资源 ID（必需）
- `--relationship`, `-r`：按关系类型筛选（可选）
- `--expires`, `-e`：URL过期时间（秒），默认为24小时（86400秒）

**示例：**

```bash
# 列出所有关联文件
python -m tests.cli list-files --resource-category product --resource-id product456

# 按关系类型筛选
python -m tests.cli list-files --resource-category product --resource-id product456 --relationship image

# 自定义URL过期时间
python -m tests.cli list-files --resource-category product --resource-id product456 --expires 3600
```

## 使用场景示例

### 场景一：上传产品图片并关联到产品

```bash
# 上传产品图片
python -m tests.cli upload D:\product_images\product1.jpg --summary "产品1主图" --account-id minio_test

# 记下返回的文件ID，然后关联到产品
python -m tests.cli link file_id_123 --resource-category product --resource-id product1 --relationship main_image
```

### 场景二：上传用户头像

```bash
# 直接上传并关联
python -m tests.cli upload D:\avatars\user1.jpg --resource-category user --resource-id user1 --relationship avatar --summary "用户头像"
```

### 场景三：下载并查看文件

```bash
# 下载文件
python -m tests.cli download file_id_123 --output D:\temp\view.jpg
```

### 场景四：获取并分享文件链接

```bash
# 获取文件的临时访问链接
python -m tests.cli view-url file_id_123 --expires 7200
```

### 场景五：管理产品图片关联

```bash
# 上传产品图片
python -m tests.cli upload D:\product_images\product1.jpg --resource-category product --resource-id product1 --relationship main_image

# 查看产品关联的所有图片
python -m tests.cli list-files --resource-category product --resource-id product1

# 解除特定图片与产品的关联
python -m tests.cli unlink file_id_123 --resource-category product --resource-id product1 --relationship main_image

# 删除不再需要的图片
python -m tests.cli delete file_id_123
```

## 注意事项

1. 确保在运行命令前已激活虚拟环境
2. 文件上传成功后会显示文件ID，请记录此ID用于后续操作
3. 默认使用测试环境配置，生产环境使用前请确认配置正确
4. 大文件上传可能需要较长时间，请耐心等待