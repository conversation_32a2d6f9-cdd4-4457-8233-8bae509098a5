import types
from enum import Enum
from typing import Any, Dict, Type, Union, cast, get_args, get_origin

from infra_basic.model.base_plus_model import GenericBaseModel
from infra_geo.model.geo_point_model import GeoPointModel
from loguru import logger
from lxml import etree
from pydantic import BaseModel
from sqlalchemy import inspect as sqlalchemy_inspect
from sqlalchemy.exc import InvalidRequestError, NoInspectionAvailable, SQLAlchemyError

from infra_database.converter.type_converters.enum import (
    enum_to_str,
    str_to_enum,
)
from infra_database.converter.type_converters.geo import wkt_or_wkb_to_geo_point_model
from infra_database.entity.base_entity import GenericSqlalchemyEntity, SqlalchemyEntity
from infra_database.error import InvalidEntityClassError
from infra_database.repository.orm_helper import is_entity_type


def model_to_flat_dict(model: BaseModel) -> dict[str, Any]:
    """
    将 Pydantic 模型的第一层字段浅转换为字典。

    此函数遍历模型中定义的所有字段，并创建一个新字典。
    如果字段值是 Pydantic 模型的另一个实例，该实例将被直接引用，而不会被递归转换。
    这对于需要保留嵌套模型对象的场景很有用。

    参数:
        model: BaseModel - 要转换的 Pydantic 模型实例。

    返回:
        dict[str, Any]: 包含模型第一层字段的字典。
    """
    return {field_name: getattr(model, field_name) for field_name in type(model).model_fields}


def entity_to_flat_dict(entity: SqlalchemyEntity) -> dict[str, Any]:
    """
    将 SQLAlchemy 实体对象浅转换为包含其所有属性的字典。

    此函数会检查实体的所有列和关系属性，并将它们的值复制到一个新的字典中。
    注意：访问属性可能会触发数据库的延迟加载（lazy loading）。
    对于关系属性，它会直接引用关联的实体或实体集合，而不会进行递归转换。

    参数:
        entity: SqlalchemyEntity - 要转换的 SQLAlchemy 实体实例。

    返回:
        dict[str, Any]: 包含实体所有列和关系属性的字典。
    """
    result: dict[str, Any] = {}
    try:
        mapper = sqlalchemy_inspect(type(entity))
        # 处理列属性
        for column in mapper.columns:
            result[column.name] = getattr(entity, column.name)
        # 处理关系属性
        for relationship_property in mapper.relationships:
            result[relationship_property.key] = getattr(entity, relationship_property.key)
    except (InvalidRequestError, NoInspectionAvailable):
        logger.warning(f"对象 {entity} 可能不是一个有效的SQLAlchemy实体实例，将返回空字典。")
    return result


def get_entity_field_type_dict(entity_cls: Type[GenericSqlalchemyEntity]) -> Dict[str, Type[Any]]:
    """
    获取 SQLAlchemy 实体类的字段名与 Python 类型的映射字典。

    此函数通过 SQLAlchemy 的 inspect 功能来分析实体类，提取所有列属性
    及其对应的 Python 类型。

    参数:
        entity_cls: Type[GenericSqlalchemyEntity] - 要检查的 SQLAlchemy 实体类。

    返回:
        Dict[str, Type[Any]]: 一个字典，键是字段名，值是该字段对应的 Python 类型。

    异常:
        InvalidEntityClassError: 如果传入的类不是一个有效的 SQLAlchemy 实体类。
    """
    try:
        if not is_entity_type(entity_cls):
            logger.error(f"传入的类 {entity_cls} 不是有效的SQLAlchemy实体类")
            raise InvalidEntityClassError(cls=entity_cls)

        mapper = sqlalchemy_inspect(entity_cls)
        return {column.name: column.type.python_type for column in mapper.columns}
    except (NoInspectionAvailable, InvalidRequestError, SQLAlchemyError) as e:
        logger.error(f"获取实体类 {entity_cls.__name__} 的字段类型字典时发生错误: {e}")
        raise InvalidEntityClassError(cls=entity_cls) from e


def get_model_field_type_dict(model_cls: Type[GenericBaseModel]) -> Dict[str, Type[Any]]:
    """
    获取 Pydantic 模型的字段名与类型注解的字典。

    此函数会检查传入的类是否为有效的 Pydantic 模型，并提取其所有字段的
    名称和类型注解。

    参数:
        model_cls: Type[GenericBaseModel] - Pydantic 模型类。

    返回:
        Dict[str, Type[Any]]: 字段名到其类型注解的映射字典。

    异常:
        ValueError: 如果传入的不是有效的 Pydantic 模型类。
    """
    model_type = cast(Type[BaseModel], model_cls)

    if not isinstance(model_type, type) or not issubclass(model_type, BaseModel):
        msg = f"传入的类 {model_type.__name__} 不是有效的Pydantic模型类"
        logger.error(msg)
        raise ValueError(msg)

    if not model_type.model_fields:
        msg = f"Pydantic模型类 {model_type.__name__} 没有定义任何字段"
        logger.error(msg)
        raise ValueError(msg)

    return {
        field_name: field.annotation
        for field_name, field in model_type.model_fields.items()
        if field.annotation is not None
    }


def model_to_entity_dict(model: BaseModel, entity_cls: Type[GenericSqlalchemyEntity]) -> dict[str, Any]:
    """
    将 Pydantic 模型实例转换为适用于创建 SQLAlchemy 实体的字典。

    这是一个便捷函数，它首先将模型转换为扁平字典，然后调用 `prepare_dict_for_entity`
    来处理特定于实体的类型转换（例如，将枚举转换为字符串）。

    参数:
        model: BaseModel - 要转换的 Pydantic 模型实例。
        entity_cls: Type[GenericSqlalchemyEntity] - 目标 SQLAlchemy 实体类。

    返回:
        dict[str, Any]: 转换后的字典，可用于实例化目标实体。
    """
    model_dict = model_to_flat_dict(model)
    return prepare_dict_for_entity(entity_cls=entity_cls, dict_data=model_dict)


def entity_to_model_dict(entity: SqlalchemyEntity, model_cls: Type[GenericBaseModel]) -> dict[str, Any]:
    """
    将 SQLAlchemy 实体对象转换为适用于创建 Pydantic 模型的字典。

    该函数首先将 SQLAlchemy 实体转换为一个扁平的字典，然后调用 `prepare_dict_for_model`
    来处理特定于模型的类型转换（例如，将字符串转换为枚举）。

    参数:
        entity: SqlalchemyEntity - 要转换的实体对象。
        model_cls: Type[GenericBaseModel] - 目标 Pydantic 模型类。

    返回:
        dict[str, Any]: 转换后的字典，可用于实例化目标模型。
    """
    entity_dict = entity_to_flat_dict(entity)
    return prepare_dict_for_model(model_cls=model_cls, dict_data=entity_dict)


def _convert_value_for_entity(value: Any, target_type: Type[Any]) -> Any:
    """根据目标实体字段类型转换单个值。"""
    if isinstance(value, Enum) and target_type is str:
        return enum_to_str(value)
    return value


def prepare_dict_for_entity(
    entity_cls: Type[GenericSqlalchemyEntity],
    dict_data: dict[str, Any],
) -> dict[str, Any]:
    """
    准备一个字典，使其字段和类型适用于创建指定的 SQLAlchemy 实体。

    此函数会过滤输入字典，只保留实体类中存在的字段。
    它还会处理特定的类型转换，最常见的是将枚举（Enum）成员转换为其值（通常是字符串），
    以便能存入数据库。

    参数:
        entity_cls: Type[GenericSqlalchemyEntity] - 目标 SQLAlchemy 实体类。
        dict_data: dict[str, Any] - 包含原始数据的输入字典。

    返回:
        dict[str, Any]: 经过清理和类型转换后的字典，可安全地用于创建实体实例。
    """
    entity_field_type_dict = get_entity_field_type_dict(entity_cls)
    result_dict: dict[str, Any] = {}
    for field_name, entity_field_type in entity_field_type_dict.items():
        if field_name in dict_data:
            value = dict_data.get(field_name)
            if value is not None:
                result_dict[field_name] = _convert_value_for_entity(value, entity_field_type)
            else:
                result_dict[field_name] = None
    return result_dict


def _get_main_type_from_optional(annotation: Any) -> Any:
    """
    从 Optional 类型注解中提取主类型。
    例如: get_main_type_from_optional(str | None) -> str
          get_main_type_from_optional(str) -> str
    """
    origin = get_origin(annotation)
    if origin is Union or (hasattr(types, "UnionType") and origin is types.UnionType):
        args = get_args(annotation)
        non_none_args = [arg for arg in args if arg is not type(None)]
        if len(non_none_args) == 1:
            return non_none_args[0]
    return annotation


def _convert_str_to_enum(value: str, target_type: Type[Enum]) -> Enum | None:
    """将字符串转换为枚举类型。"""
    if not value:
        return None
    try:
        return str_to_enum(value, target_type)
    except ValueError as e:
        logger.error(f"将字符串 '{value}' 转换为枚举 {target_type.__name__} 失败: {e}")
        raise


def _convert_str_to_xml(value: str, field_name: str) -> etree._Element | None:
    """将字符串转换为XML元素。"""
    try:
        parser = etree.XMLParser(remove_blank_text=True)
        return etree.fromstring(value.encode("utf-8"), parser=parser)
    except etree.XMLSyntaxError:
        logger.warning(f"字段 '{field_name}' 的值 '{value}' 不是有效的XML，将设置为None")
        return None


def _convert_value_for_model(value: Any, target_type: Type[Any], field_name: str) -> Any:
    """根据目标模型字段类型转换单个值。"""
    if value is None:
        return None

    main_target_type = _get_main_type_from_optional(target_type)

    if isinstance(value, str):
        if isinstance(main_target_type, type) and issubclass(main_target_type, Enum):
            return _convert_str_to_enum(value, main_target_type)
        if main_target_type is GeoPointModel:
            return wkt_or_wkb_to_geo_point_model(value)
        if main_target_type is etree._Element:
            return _convert_str_to_xml(value, field_name)

    return value


def prepare_dict_for_model(
    model_cls: Type[GenericBaseModel],
    dict_data: dict[str, Any],
) -> dict[str, Any]:
    """
    准备一个字典，使其字段和类型适用于创建指定的 Pydantic 模型。

    此函数会过滤输入字典，只保留模型类中存在的字段。
    它还会处理特定的类型转换，例如：
    - 将字符串转换为目标字段所期望的枚举（Enum）类型。
    - 将WKB/WKT字符串转换为GeoPointModel。
    - 将XML字符串转换为lxml Element对象。

    参数:
        model_cls: Type[GenericBaseModel] - 目标 Pydantic 模型类。
        dict_data: dict[str, Any] - 包含原始数据的输入字典。

    返回:
        dict[str, Any]: 经过清理和类型转换后的字典，可安全地用于创建模型实例。
    """
    model_field_type_dict = get_model_field_type_dict(model_cls)
    result_dict: dict[str, Any] = {}
    for field_name, model_field_type in model_field_type_dict.items():
        if field_name in dict_data:
            value = dict_data.get(field_name)
            result_dict[field_name] = _convert_value_for_model(value, model_field_type, field_name)
    return result_dict
