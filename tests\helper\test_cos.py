import json
import pytest
from infra_object_storage.error import ObjectNotPublicReadError


@pytest.mark.parametrize("config_path", ["cos.toml"], indirect=True)
def test_cos_bucket(object_storage_client, config_path):
    check_bucket_name = "dev-cos-1305036724"
    assert object_storage_client.bucket_exists(check_bucket_name) is True

    test_bucket_name = "minio-py-1305036724"
    object_storage_client.make_bucket(test_bucket_name)
    object_storage_client.remove_bucket(test_bucket_name)
    assert object_storage_client.bucket_exists(test_bucket_name) is False


@pytest.mark.parametrize("config_path", ["cos.toml"], indirect=True)
def test_cos_upload(object_storage_client, config_path):
    with open("tests/helper/cos.png", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(
            object_name="cos_upload.png", object_data=obj_data
        )


@pytest.mark.parametrize("config_path", ["cos.toml"], indirect=True)
def test_cos_download(object_storage_client, config_path):
    obj_blob = object_storage_client.download_file(object_name="cos_upload.jpg")
    with open("tests/helper/cos_download.jpg", "wb") as f:
        f.write(obj_blob)


@pytest.mark.parametrize("config_path", ["cos.toml"], indirect=True)
def test_cos_url(object_storage_client, config_path):
    # test public url
    url = object_storage_client.get_file_url("s3-public.jpg")
    print(url)
    assert url is not None
    url = object_storage_client.get_file_url("cos_upload.jpg")
    print(url)
    assert url is not None


@pytest.mark.parametrize("config_path", ["cos.toml"], indirect=True)
def test_cos_public_upload(object_storage_client, config_path):
    with open("tests/helper/s3-public.jpg", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(
            object_name="s3-public.jpg", object_data=obj_data,
            is_public=True
        )


@pytest.mark.parametrize("config_path", ["cos.toml"], indirect=True)
def test_cos_get_policy(object_storage_client, config_path):
    policy = object_storage_client.get_object_policy("cos_upload.jpg")
    print(json.dumps(policy))
    policy = object_storage_client.get_object_policy("s3-public.jpg")
    print(json.dumps(policy))
