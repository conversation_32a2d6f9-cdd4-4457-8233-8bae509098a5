import importlib
from types import TracebackType
from typing import TYPE_CHECKING, Any, Optional, Type

from loguru import logger

# 导入必要的 SQLAlchemy 组件
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

# 从你的项目导入 BaseUnitOfWork
from infra_database.uow.base_unit_of_work_interface import BaseUnitOfWork

# 从你的项目导入上下文存储
from infra_database.uow.unit_of_work_storage import UnitOfWorkContainer, get_uow_storage

# 从你的项目导入异步事务仓库，确保路径正确
if TYPE_CHECKING:
    from infra_database.repository.asynchronous.async_transaction_repository import (
        AsyncTransactionRepository,
    )
else:
    async_repo_module = importlib.import_module("infra_database.repository.asynchronous.async_transaction_repository")
    AsyncTransactionRepository = async_repo_module.AsyncTransactionRepository


class AsyncSqlAlchemyUnitOfWork(BaseUnitOfWork):
    """
    基于 SQLAlchemy 实现的异步工作单元。

    它继承了 `BaseUnitOfWork` 提供的事务日志功能，
    并负责管理异步 `AsyncSession` 的生命周期和事务（包括嵌套事务/保存点）。
    同时负责将事务日志持久化。
    """

    def __init__(self, async_session_factory: async_sessionmaker[AsyncSession], log_enabled: bool = True):
        """
        初始化异步工作单元。

        参数:
            async_session_factory: 一个 SQLAlchemy `async_sessionmaker` 实例，用于创建新的 AsyncSession。
            log_enabled: 是否开启此工作单元的详细日志。
        """
        super().__init__()
        self._async_session_factory = async_session_factory
        self._log_enabled = log_enabled
        self._transaction_repo: Optional["AsyncTransactionRepository"] = None
        self._storage = get_uow_storage()

    @property
    def session_stack_level(self) -> int:
        return len(self._storage.uow_stack)

    @property
    def session(self) -> AsyncSession:
        current_container = self._storage.current_container
        if not current_container or not isinstance(current_container.session, AsyncSession):
            raise RuntimeError("当前AsyncSqlAlchemyUnitOfWork中没有活跃的异步 SQLAlchemy Session。")
        return current_container.session

    @property
    def transaction_repo(self) -> "AsyncTransactionRepository":
        """懒加载事务仓库，确保它总是使用当前UOW的session"""
        if self._transaction_repo is None:
            if self._log_enabled:
                logger.debug("懒加载 AsyncTransactionRepository...")
            self._transaction_repo = AsyncTransactionRepository(session=self.session)
        return self._transaction_repo

    def __enter__(self) -> 'AsyncSqlAlchemyUnitOfWork':
        raise NotImplementedError(
            "AsyncSqlAlchemyUnitOfWork 不支持同步上下文管理。请使用 SyncSqlAlchemyUnitOfWork 替代。"
        )

    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[TracebackType]
    ) -> Optional[bool]:
        raise NotImplementedError(
            "AsyncSqlAlchemyUnitOfWork 不支持同步上下文管理。请使用 SyncSqlAlchemyUnitOfWork 替代。"
        )

    async def __aenter__(self) -> 'AsyncSqlAlchemyUnitOfWork':
        current_level = self.session_stack_level
        if self._log_enabled:
            logger.debug(f"AsyncUOW [level={current_level}]: 进入异步上下文。")

        if current_level == 0:
            session = self._async_session_factory()
            transaction = await session.begin()
            if self._log_enabled:
                logger.info(f"AsyncUOW [level={current_level}]: 开启顶层异步事务. Session ID: {id(session)}")
        else:
            parent_container = self._storage.current_container
            if not parent_container:
                raise RuntimeError("AsyncUOW: 无法为嵌套异步事务找到父级容器。")
            session = parent_container.session
            transaction = await session.begin_nested()
            if self._log_enabled:
                logger.info(
                    f"AsyncUOW [level={current_level}]: 开启嵌套异步事务 (SAVEPOINT). Session ID: {id(session)}"
                )

        self._storage.push(UnitOfWorkContainer(session, transaction))
        return self

    async def __aexit__(
        self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[TracebackType]
    ) -> Optional[bool]:
        level_to_complete_log = self.session_stack_level
        if self._log_enabled:
            logger.debug(f"AsyncUOW [level={level_to_complete_log}]: 退出异步上下文。")

        repo = self.transaction_repo if level_to_complete_log == 1 else None
        container = self._storage.pop()
        if not container:
            raise RuntimeError("AsyncUOW: 尝试退出，但工作单元堆栈为空。")

        session_to_close = container.session if level_to_complete_log == 1 else None

        original_exception: Optional[BaseException] = exc_val

        try:
            if original_exception:
                if self._log_enabled:
                    logger.error(
                        f"AsyncUOW [level={level_to_complete_log}]: 上下文异常，回滚异步事务。错误: {original_exception}"
                    )
                await container.transaction.rollback()
                self._complete_log_entry(level_to_complete_log, is_succeed=False, error_message=str(original_exception))
            else:
                if self._log_enabled:
                    logger.debug(f"AsyncUOW [level={level_to_complete_log}]: 准备提交异步事务。")
                if session_to_close and self.all_transaction_logs:
                    if self._log_enabled:
                        logger.debug(
                            f"AsyncUOW [level={level_to_complete_log}]: 异步持久化 {len(self.all_transaction_logs)} 条成功日志。"
                        )
                    if repo:
                        await repo.save_transaction_logs(self.all_transaction_logs)

                await container.transaction.commit()
                if self._log_enabled:
                    logger.info(f"AsyncUOW [level={level_to_complete_log}]: 异步事务已成功提交。")
                self._complete_log_entry(level_to_complete_log, is_succeed=True)

        except Exception as e:
            if self._log_enabled:
                logger.error(f"AsyncUOW [level={level_to_complete_log}]: 处理异步事务时发生内部错误: {e}，将回滚。")
            await container.transaction.rollback()
            self._complete_log_entry(level_to_complete_log, is_succeed=False, error_message=f"事务内部错误: {e}")
            original_exception = e

        finally:
            if session_to_close:
                if original_exception and self.all_transaction_logs:
                    if self._log_enabled:
                        logger.warning(
                            f"AsyncUOW [level={level_to_complete_log}]: 尝试在独立事务中异步持久化失败日志。"
                        )
                    try:
                        async with self._async_session_factory() as log_session:
                            log_repo = AsyncTransactionRepository(session=log_session)
                            await log_repo.save_transaction_logs(self.all_transaction_logs)
                            await log_session.commit()
                            if self._log_enabled:
                                logger.info("失败日志已在独立异步事务中成功持久化。")
                    except Exception as log_e:
                        if self._log_enabled:
                            logger.error(f"持久化失败日志时发生严重错误，日志可能已丢失: {log_e}")

                if self._log_enabled:
                    logger.info(
                        f"AsyncUOW [level={level_to_complete_log}]: 关闭 AsyncSession ID: {id(session_to_close)}"
                    )
                await session_to_close.close()
                self._storage.clear()
                self._cleanup_logs()

        if original_exception:
            raise original_exception

        return False

    # ------------------ 额外的便捷异步方法 (示例) ------------------
    # ------------------ 额外的便捷异步方法 (示例) ------------------
    def add(self, instance: Any) -> None:
        self.session.add(instance)

    async def delete(self, instance: Any) -> None:
        await self.session.delete(instance)
