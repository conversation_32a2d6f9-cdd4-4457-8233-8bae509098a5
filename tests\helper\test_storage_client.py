import pytest

from infra_object_storage.helper.cos_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from infra_object_storage.helper.minio_helper import <PERSON><PERSON>H<PERSON><PERSON>
from infra_object_storage.helper.object_storage_client import ObjectStorageClient
from infra_object_storage.helper.oss_helper import <PERSON>ss<PERSON>elper
from infra_object_storage.helper.s3_helper import S3Helper


class TestObjectStorageClientIntegration:
    """
    ObjectStorageClient 集成测试
    使用 conftest.py 中的 fixtures 来与真实（或模拟的）后端服务交互
    """

    @pytest.mark.parametrize(
        "account_id, expected_helper_type",
        [
            ("cos_test", CosHelper),
            ("minio_test", MinioHelper),
            ("s3_test", S3Helper),
            ("oss_test", OssHelper),
        ],
    )
    def test_get_helper_by_id(
        self, object_storage_client_from_file: ObjectStorageClient, account_id: str, expected_helper_type: type
    ):
        """
        测试能否根据账户ID正确获取对应的Helper实例
        """
        helper = object_storage_client_from_file.get_helper(account_id)
        assert isinstance(helper, expected_helper_type)
        # 验证返回的helper中的账户ID是否正确
        assert helper.get_account_info().id == account_id

    def test_get_default_helper(self, object_storage_client_from_file: ObjectStorageClient):
        """
        测试能否正确获取默认的Helper实例
        """
        default_helper = object_storage_client_from_file.get_default_helper()
        default_account_id = object_storage_client_from_file._default_account_id
        
        # 再次获取，确认返回的是同一个实例
        helper_by_id = object_storage_client_from_file.get_helper(default_account_id)
        
        assert default_helper is helper_by_id
        assert default_helper.get_account_info().id == default_account_id

    def test_helper_caching(self, object_storage_client_from_file: ObjectStorageClient):
        """
        测试Helper实例的缓存机制
        """
        # 获取默认helper两次，验证是否为同一实例
        helper1 = object_storage_client_from_file.get_default_helper()
        helper2 = object_storage_client_from_file.get_default_helper()
        assert helper1 is helper2, "默认Helper实例应该被缓存"

        # 获取一个非默认的helper两次，验证是否为同一实例
        s3_helper1 = object_storage_client_from_file.get_helper("s3_test")
        s3_helper2 = object_storage_client_from_file.get_helper("s3_test")
        assert s3_helper1 is s3_helper2, "S3 Helper实例应该被缓存"

    def test_get_helper_invalid_id(self, object_storage_client_from_file: ObjectStorageClient):
        """
        测试使用无效的账户ID获取Helper时是否会引发错误
        """
        invalid_id = "non-existent-id"
        with pytest.raises(ValueError, match=f"Storage account with id '{invalid_id}' not found in configuration."):
            object_storage_client_from_file.get_helper(invalid_id)
