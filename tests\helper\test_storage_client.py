import os
import pytest


@pytest.mark.parametrize("config_path", ["cos.toml", "minio.toml", "oss.toml", "s3.toml"], indirect=True)
def test_storage_client_download(object_storage_client, config_path):
    with open(os.path.join(os.path.dirname(__file__), "storage_client.jpg"), "rb") as f:
        file_data = f.read()
    object_storage_client.upload_file(
        object_name="storage_client_upload.jpg",
        object_data=file_data,
    )
    obj_blob = object_storage_client.download_file(
        object_name="storage_client_upload.jpg"
    )
    with open("tests/helper/storage_client_download.jpg", "wb") as f:
        f.write(obj_blob)


@pytest.mark.parametrize("config_path", ["cos.toml", "minio.toml", "oss.toml", "s3.toml"], indirect=True)
def test_storage_client_upload(object_storage_client, config_path):
    with open("tests/helper/storage_client.jpg", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(
            object_name="storage_client_upload.jpg", object_data=obj_data
        )


@pytest.mark.parametrize("config_path", ["cos.toml", "minio.toml", "oss.toml", "s3.toml"], indirect=True)
def test_storage_client_bucket(object_storage_client, config_path):
    if "cos" in config_path:
        # cos 的 bucket 名称需要以 appid 结尾
        appid = object_storage_client._ObjectStorageClient__client._CosHelper__settings.default_bucket.split("-")[-1]
        test_bucket_name = f"minio-py-{os.urandom(4).hex()}-{appid}"
    else:
        test_bucket_name = f"minio-py-{os.urandom(4).hex()}"
    object_storage_client.make_bucket(test_bucket_name)
    assert object_storage_client.bucket_exists(test_bucket_name) is True
    object_storage_client.remove_bucket(test_bucket_name)
    assert object_storage_client.bucket_exists(test_bucket_name) is False


@pytest.mark.parametrize("config_path", ["cos.toml", "minio.toml", "oss.toml", "s3.toml"], indirect=True)
def test_storage_client_url(object_storage_client, config_path):
    with open(os.path.join(os.path.dirname(__file__), "storage_client.jpg"), "rb") as f:
        file_data = f.read()
    object_storage_client.upload_file(
        object_name="storage_client_upload_url.jpg",
        object_data=file_data,
        is_public=False,
    )
    url = object_storage_client.get_file_url("storage_client_upload_url.jpg")
    assert url is not None
    assert "Expires" in url or "expires" in url or "sign" in url


@pytest.mark.parametrize("config_path", ["cos.toml", "minio.toml", "oss.toml", "s3.toml"], indirect=True)
def test_storage_client_account_info(object_storage_client, config_path):
    account_info = object_storage_client.get_account_info()
    assert account_info is not None
    assert account_info.type is not None
    assert account_info.site is not None
    assert account_info.unified_id is not None


@pytest.mark.parametrize("config_path", ["cos.toml", "minio.toml", "oss.toml", "s3.toml"], indirect=True)
def test_storage_client_acl(object_storage_client, config_path):
    with open(os.path.join(os.path.dirname(__file__), "storage_client.jpg"), "rb") as f:
        file_data = f.read()
    object_storage_client.upload_file(
        object_name="storage_client_upload_acl.jpg",
        object_data=file_data,
    )
    try:
        object_storage_client.set_object_public_read(object_name="storage_client_upload_acl.jpg")
        acl = object_storage_client.get_object_acl(object_name="storage_client_upload_acl.jpg")
        assert acl is not None
    except NotImplementedError:
        pass


@pytest.mark.parametrize("config_path", ["cos.toml", "minio.toml", "oss.toml", "s3.toml"], indirect=True)
def test_storage_client_policy(object_storage_client, config_path):
    from botocore.exceptions import ClientError
    try:
        policy = object_storage_client.get_bucket_policy()
        assert policy is not None
    except (NotImplementedError, ClientError):
        pass
    with open(os.path.join(os.path.dirname(__file__), "storage_client.jpg"), "rb") as f:
        file_data = f.read()
    object_storage_client.upload_file(
        object_name="storage_client_upload_policy.jpg",
        object_data=file_data,
    )
    try:
        policy = object_storage_client.get_object_policy(object_name="storage_client_upload_policy.jpg")
        assert policy is not None
    except (NotImplementedError, ClientError):
        pass
