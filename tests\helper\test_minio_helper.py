import io
import uuid

import pytest

from infra_object_storage.helper.minio_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from infra_object_storage.settings import MinioAccount


def test_minio_helper_init(minio_account, minio_helper: MinioHelper):
    """Test MinioHelper initialization"""
    # Verify account info
    account_info = minio_helper.get_account_info()
    assert account_info == minio_account
    assert minio_helper.bucket_exists(minio_account.default_bucket)


def test_minio_helper_target_bucket_name(minio_account, minio_helper: MinioHelper):
    """Test get_target_bucket_name method"""
    # Test with default bucket name
    default_bucket = minio_account.default_bucket
    assert minio_helper.get_target_bucket_name(default_bucket) == default_bucket

    # Test with another bucket name
    test_bucket = "test-bucket"
    assert minio_helper.get_target_bucket_name(test_bucket) == test_bucket


def test_minio_helper_object_operations(minio_helper: MinioHelper):
    """Test object operations (upload, download, exists, delete)"""
    object_name = f"test-object-{uuid.uuid4()}.txt"
    try:
        # Upload a test file
        obj_data = b"test content for minio"
        minio_helper.upload_object(object_name=object_name, object_data=obj_data)
        
        # Verify object exists
        assert minio_helper.object_exists(object_name)
        
        # Download and verify content
        downloaded_data = minio_helper.download_object(object_name)
        assert downloaded_data == obj_data
        
    finally:
        # Clean up
        if minio_helper.object_exists(object_name):
            minio_helper.delete_object(object_name)


def test_minio_helper_bucket_policy(minio_helper: MinioHelper):
    """Test bucket policy operations"""
    bucket_name = minio_helper.get_account_info().default_bucket
    policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {"AWS": ["*"]},
                "Action": ["s3:GetObject"],
                "Resource": [f"arn:aws:s3:::{bucket_name}/*"],
            },
        ],
    }
    minio_helper.set_bucket_policy(policy)
    
    retrieved_policy = minio_helper.get_bucket_policy()
    assert retrieved_policy["Statement"][0]["Resource"][0] == f"arn:aws:s3:::{bucket_name}/*"


def test_minio_helper_public_read(minio_helper: MinioHelper):
    """Test public read operations"""
    object_name = f"test-public-read-{uuid.uuid4()}.txt"
    try:
        # Upload a test file
        obj_data = b"test content for public read"
        minio_helper.upload_object(object_name=object_name, object_data=obj_data)
        
        # Make file public
        minio_helper.set_object_public_read(object_name)
        
        # Get bucket policy and verify it contains the object
        policy = minio_helper.get_bucket_policy()
        
        # Check if the policy has a statement allowing public read for this object
        has_public_read = False
        for statement in policy.get("Statement", []):
            if statement.get("Effect") == "Allow" and "s3:GetObject" in statement.get("Action", []):
                resources = statement.get("Resource", [])
                if isinstance(resources, list):
                    for resource in resources:
                        if object_name in resource:
                            has_public_read = True
                            break
        
        assert has_public_read, "Object should have public read permission in bucket policy"
        
    finally:
        # Clean up
        if minio_helper.object_exists(object_name):
            minio_helper.delete_object(object_name)


def test_list_and_delete_objects(minio_helper: MinioHelper):
    """Test listing and batch deleting objects."""
    prefix = f"test-list-{uuid.uuid4()}"
    object_names = [f"{prefix}/obj_{i}.txt" for i in range(3)]
    try:
        # Upload multiple files
        for name in object_names:
            minio_helper.upload_object(name, b"test data")

        # List all objects with prefix
        listed_objects = list(minio_helper.list_objects(prefix=prefix))
        assert sorted(listed_objects) == sorted(object_names)

        # Test listing with a more specific prefix
        listed_subset = list(minio_helper.list_objects(prefix=f"{prefix}/obj_1"))
        assert listed_subset == [object_names[1]]

        # Batch delete objects
        minio_helper.delete_objects(object_names)

        # Verify objects are deleted
        listed_after_delete = list(minio_helper.list_objects(prefix=prefix))
        assert len(listed_after_delete) == 0

    finally:
        # Clean up any remaining objects
        remaining = list(minio_helper.list_objects(prefix=prefix))
        if remaining:
            minio_helper.delete_objects(remaining)