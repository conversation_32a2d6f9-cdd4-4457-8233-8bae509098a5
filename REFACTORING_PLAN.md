# 对象存储接口 (ObjectStorageInterface) 重构计划

本文档旨在详细说明对 `infra_object_storage/helper/object_storage_interface.py` 的重构和优化计划，以提升其完整性、健壮性和易用性。

## 1. 初步评审意见

### 1.1. 优点

*   **抽象清晰**: 使用 `abc.ABC` 和 `@abstractmethod` 明确定义了接口契约。
*   **类型提示**: 广泛使用了类型提示，增强了代码的可读性和健壮性。
*   **功能覆盖**: 基本覆盖了对象存储的常用操作。

### 1.2. 可改进之处

*   **术语统一性**: 注释中的“篮子”应统一为更专业的“存储桶” (Bucket)。
*   **功能缺失**: 缺少列出对象 (`list_objects`) 和批量删除对象 (`delete_objects`) 的方法。
*   **大文件处理**: 当前方法一次性将文件读入内存，对大文件不友好。需要支持流式处理。
*   **错误处理**: 缺少统一的异常体系，不利于上层调用者捕获和处理错误。
*   **返回类型模糊**: 部分方法返回 `dict`，其内部结构不明确。

## 2. 详细重构计划

### 2.1. Mermaid 类图

```mermaid
classDiagram
    direction LR
    class ObjectStorageInterface {
        <<ABC>>
        +get_target_bucket_name(bucket_name: str | None) str
        +bucket_exists(bucket_name: str | None) bool
        +make_bucket(bucket_name: str | None) None
        +remove_bucket(bucket_name: str | None) None
        +download_file(object_name: str, bucket_name: str | None) bytes
        +upload_file(object_name: str, object_data: bytes, bucket_name: str | None, is_public: bool) None
        +get_file_url(object_name: str, bucket_name: str | None, expires_seconds: int) str
        +get_account_info() StorageAccountType
        +get_object_policy(object_name: str, bucket_name: str | None) dict
        +get_bucket_policy(bucket_name: str | None) dict
        +set_object_public_read(object_name: str, bucket_name: str | None) None
        +get_object_acl(object_name: str, bucket_name: str | None) dict
        +object_exists(object_name: str, bucket_name: str | None) bool
        +delete_object(object_name: str, bucket_name: str | None) None
        
        <<New Methods>>
        +list_objects(bucket_name: str | None, prefix: str | None) Iterable[str]
        +delete_objects(object_names: list[str], bucket_name: str | None) None
        +download_file_stream(object_name: str, bucket_name: str | None) Iterable[bytes]
        +upload_file_stream(object_name: str, stream: IO, length: int, bucket_name: str | None, is_public: bool) None
    }
```

### 2.2. 计划步骤

#### 步骤 1: 术语和文档标准化

*   **任务**: 将代码和文档中所有的“篮子”替换为“存储桶”。
*   **任务**: 统一并完善所有方法的 Docstring，使其描述更清晰，风格一致。

#### 步骤 2: 核心功能增强

*   **任务**: 添加 `list_objects` 方法。
    *   **签名**: `def list_objects(self, bucket_name: Optional[str] = None, prefix: Optional[str] = None) -> Iterable[str]:`
    *   **功能**: 列出指定存储桶下，符合特定前缀的所有对象的名称。返回一个迭代器以支持分页和节省内存。
*   **任务**: 添加 `delete_objects` 方法。
    *   **签名**: `def delete_objects(self, object_names: list[str], bucket_name: Optional[str] = None):`
    *   **功能**: 在一个请求中批量删除多个对象，提升效率。

#### 步骤 3: 支持大文件流式处理

*   **任务**: 添加 `download_file_stream` 方法。
    *   **签名**: `def download_file_stream(self, object_name: str, bucket_name: Optional[str] = None) -> Iterable[bytes]:`
    *   **功能**: 以数据块（chunk）迭代器的形式返回文件内容，避免将整个文件读入内存。
*   **任务**: 添加 `upload_file_stream` 方法。
    *   **签名**: `def upload_file_stream(self, object_name: str, stream: IO, length: int, bucket_name: Optional[str] = None, is_public: bool = False):`
    *   **功能**: 从一个文件流（`IO`对象）中读取数据并上传，适用于大文件。

#### 步骤 4: 强化类型和错误处理

*   **任务**: 在 `infra_object_storage/error.py` 中定义自定义异常。
    ```python
    class ObjectStorageError(Exception):
        """Base exception for object storage operations."""
    class BucketNotFound(ObjectStorageError):
        """Raised when a bucket does not exist."""
    class ObjectNotFound(ObjectStorageError):
        """Raised when an object does not exist."""
    ```
*   **任务**: 使用 `TypedDict` 精确化返回类型。
    *   为 `get_bucket_policy` 等返回 `dict` 的方法定义 `TypedDict` 结构，以明确其返回内容。
    ```python
    from typing import TypedDict, List
    
    class PolicyStatement(TypedDict):
        Effect: str
        Principal: dict
        Action: List[str]
        Resource: List[str]
    
    class BucketPolicy(TypedDict):
        Version: str
        Statement: List[PolicyStatement]