"""
事务处理模型定义

本模块提供了用于记录和处理系统事务的模型类。
事务模型用于跟踪系统中的各种操作，包括操作者、操作时间、操作内容等信息。
"""

from datetime import datetime
from typing import Any, Optional

from infra_basic.error.error_info import ErrorInfo
from infra_utility.credential_helper import generate_uuid_id
from infra_utility.datetime_helper import local_now
from pydantic import BaseModel, Field


class SystemTransactionModel(BaseModel):
    """
    系统事务模型

    用于记录系统中的各种操作事务，包括操作者、操作时间、操作内容等信息。
    此模型遵循Pydantic V2的最佳实践，将动态默认值（如时间）的设置移至 `model_post_init` 中处理。

    属性:
        id (str): 事务ID，使用UUID自动生成。
        parent_transaction_id (Optional[str]): 父事务ID，用于关联嵌套或分步事务。默认为 None。
        handler_category (str): 执行操作的主体类别，例如 'user', 'system'。
        handler_id (str): 执行操作的主体ID。
        handled_on (Optional[datetime]): 操作执行的时间。如果未提供，则在模型初始化时自动设置为当前时间。
        action (str): 操作的类型或名称，例如 'CREATE_USER', 'UPDATE_ORDER'。
        action_params (Optional[Any]): 操作相关的参数，可以是任意可序列化的数据结构。默认为 None。
        initiator_id (Optional[str]): 事务的最初发起者ID。如果未提供，则默认为 `handler_id`。
        initiator_category (Optional[str]): 事务的最初发起者类别。如果未提供，则默认为 `handler_category`。
        commenced_on (Optional[datetime]): 事务的开始时间。如果未提供，则默认为 `handled_on` 的值。
        ceased_on (Optional[datetime]): 事务的结束时间。默认为 None。
        is_succeed (Optional[bool]): 事务是否成功。默认为 True。
        error_detail (Optional[ErrorInfo]): 如果事务失败，记录错误详情。默认为 None。
        remark (Optional[str]): 备注信息。默认为 None。
    """

    id: str = Field(default_factory=generate_uuid_id)
    parent_transaction_id: Optional[str] = None
    # 谁做的
    handler_category: str
    handler_id: str
    # 什么时候做的
    handled_on: Optional[datetime] = None
    # 动作及参数
    action: str
    action_params: Optional[Any] = None
    # 谁发起的
    initiator_id: Optional[str] = None
    initiator_category: Optional[str] = None
    # 开始和结束
    commenced_on: Optional[datetime] = None
    ceased_on: Optional[datetime] = None
    # 是否成功
    is_succeed: Optional[bool] = True
    # 错误信息
    error_detail: Optional[ErrorInfo] = None
    # 备注信息
    remark: Optional[str] = None

    def model_post_init(self, __context: Any) -> None:
        """
        在模型实例化并完成验证后，设置动态的默认值。

        - 设置 `handled_on` 和 `commenced_on` 的默认时间。
        - 设置 `initiator_id` 和 `initiator_category` 的默认值。
        """
        # 如果 handled_on 未在初始化时提供，则设置为当前时间
        if self.handled_on is None:
            self.handled_on = local_now()

        # 如果 commenced_on 未在初始化时提供，则使其与 handled_on 保持一致
        if self.commenced_on is None:
            self.commenced_on = self.handled_on

        # 如果 initiator_id 未提供，则默认操作者就是发起者
        if self.initiator_id is None:
            self.initiator_id = self.handler_id

        # 如果 initiator_category 未提供，则默认操作者类别就是发起者类别
        if self.initiator_category is None:
            self.initiator_category = self.handler_category

        return super().model_post_init(__context)
