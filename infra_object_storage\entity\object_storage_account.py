"""
存储信息
"""

from infra_database.entity.basic_entity import BasicEntity
from sqlalchemy import Column, Index, String, UniqueConstraint


class ObjectStorageAccountEntity(BasicEntity):
    """
    存储信息
    """

    __tablename__ = "st_object_storage_account"
    __table_args__ = (
        UniqueConstraint(
            "type",
            "site",
            "unified_id",
            name="uc_object_storage_account_account_info",
        ),
        {"comment": "对象存储账号信息"},
    )

    type = Column(String(255), comment="类型", nullable=False)
    site = Column(String(255), comment="连接点，保存region或者endpoint", nullable=False)
    unified_id = Column(String(255), comment="唯一标识", nullable=False, index=True)


Index(
    "idx_object_storage_account_unified_info",
    ObjectStorageAccountEntity.type,
    ObjectStorageAccountEntity.site,
    ObjectStorageAccountEntity.unified_id,
)
