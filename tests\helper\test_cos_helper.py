import uuid

from infra_object_storage.helper.cos_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_cos_helper_init(cos_account, cos_helper: <PERSON>s<PERSON>el<PERSON>):
    """Test CosHelper initialization"""
    # Verify account info
    account_info = cos_helper.get_account_info()
    assert account_info == cos_account
    assert cos_helper.bucket_exists(cos_account.default_bucket)


def test_cos_helper_target_bucket_name(cos_account, cos_helper: <PERSON>s<PERSON>el<PERSON>):
    """Test get_target_bucket_name method"""
    # Test with bucket name that already has appid
    bucket_with_appid = cos_account.default_bucket
    assert cos_helper.get_target_bucket_name(bucket_with_appid) == bucket_with_appid

    # Test with bucket name without appid
    test_bucket = "test-bucket"
    appid = cos_account.default_bucket.split("-")[-1]
    assert cos_helper.get_target_bucket_name(test_bucket) == f"{test_bucket}-{appid}"

    # Test with another bucket name without appid
    test_bucket_2 = "another-test-bucket"
    assert cos_helper.get_target_bucket_name(test_bucket_2) == f"{test_bucket_2}-{appid}"


def test_cos_helper_public_read(cos_helper: <PERSON><PERSON><PERSON><PERSON><PERSON>):
    """Test public read operations"""
    object_name = f"test-public-read-{uuid.uuid4()}.png"
    try:
        # Upload a test file
        obj_data = b"test content for public read"
        cos_helper.upload_object(object_name=object_name, object_data=obj_data)

        # Verify it's private initially by checking ACL
        acl = cos_helper.get_object_acl(object_name)
        public_read_grant = next(
            (
                g
                for g in acl["AccessControlList"]["Grant"]
                if g.get("Grantee", {}).get("URI")
                == "http://cam.qcloud.com/groups/global/AllUsers"
            ),
            None,
        )
        assert public_read_grant is None

        # Make file public
        cos_helper.set_object_public_read(object_name)

        # Verify it's public now
        acl = cos_helper.get_object_acl(object_name)
        public_read_grant = next(
            (
                g
                for g in acl["AccessControlList"]["Grant"]
                if g.get("Grantee", {}).get("URI")
                == "http://cam.qcloud.com/groups/global/AllUsers"
                and g.get("Permission") == "READ"
            ),
            None,
        )
        assert public_read_grant is not None

    finally:
        # Clean up
        if cos_helper.object_exists(object_name):
            cos_helper.delete_object(object_name)


def test_list_and_delete_objects(cos_helper: CosHelper):
    """Test listing and batch deleting objects."""
    prefix = f"test-list-{uuid.uuid4()}"
    object_names = [f"{prefix}/obj_{i}.txt" for i in range(3)]
    try:
        # Upload multiple files
        for name in object_names:
            cos_helper.upload_object(name, b"test data")

        # List all objects with prefix
        listed_objects = list(cos_helper.list_objects(prefix=prefix))
        assert sorted(listed_objects) == sorted(object_names)

        # Test listing with a more specific prefix
        listed_subset = list(cos_helper.list_objects(prefix=f"{prefix}/obj_1"))
        assert listed_subset == [object_names[1]]

        # Batch delete objects
        cos_helper.delete_objects(object_names)

        # Verify objects are deleted
        listed_after_delete = list(cos_helper.list_objects(prefix=prefix))
        assert len(listed_after_delete) == 0

    finally:
        # Clean up any remaining objects
        remaining = list(cos_helper.list_objects(prefix=prefix))
        if remaining:
            cos_helper.delete_objects(remaining)


def test_bucket_policy(cos_helper: CosHelper):
    """Test bucket policy operations"""
    bucket_name = cos_helper.get_account_info().default_bucket
    
    # Clear any existing policies
    try:
        existing_policy = cos_helper.get_bucket_policy(bucket_name)
        if existing_policy:
            empty_policy = {"Statement": [], "version": "2.0"}
            cos_helper.set_bucket_policy(empty_policy, bucket_name)
    except Exception as e:
        # If there's no existing policy, we can proceed
        pass

    policy = {
        "Statement": [
            {
                "Principal": {"qcs": ["qcs::cam::anyone:anyone"]},
                "Action": [
                    "name/cos:GetObject"
                ],
                "Effect": "allow",
                "Resource": [
                    f"qcs::cos:{cos_helper.get_account_info().region}:uid/{bucket_name.split('-')[-1]}:{bucket_name}/*"
                ]
            }
        ],
        "version": "2.0"
    }
    cos_helper.set_bucket_policy(policy, bucket_name)
    
    retrieved_policy = cos_helper.get_bucket_policy(bucket_name)
    assert retrieved_policy["version"] == "2.0"
    assert len(retrieved_policy["Statement"]) == 1
