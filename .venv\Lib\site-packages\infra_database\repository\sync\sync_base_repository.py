"""
基本同步仓库类
提供基本的数据库操作功能
"""

from typing import Any, Dict, List, Optional, Type, TypeVar

from loguru import logger
from pydantic import BaseModel
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Query, Session

from infra_database.converter.convert_helper import (
    model_to_flat_dict,
    prepare_dict_for_entity,
    prepare_dict_for_model,
)
from infra_database.entity.base_entity import GenericSqlalchemyEntity
from infra_database.error import (
    EntityError,
    EntityNotFoundError,
    SQLExecutionError,
)
from infra_database.model.database_query_model import (
    FilterCondition,
    OrderCondition,
    PageFilterParams,
    PageInitParams,
)
from infra_database.model.pagination_carrier import PaginationCarrier
from infra_database.repository.orm_helper import (
    build_sqlalchemy_query,
    prepare_entity_by_dict,
    prepare_update_data,
)
from infra_database.repository.raw_sql_helper import (
    build_extra_query_clause,
    build_full_text_search_clause,
    build_order_by_clause,
    build_pagination_clause,
    build_where_clause,
    cursor_result_to_list,
)

GenericBaseModel = TypeVar("GenericBaseModel", bound=BaseModel)


class SyncBaseRepository:
    """基本同步仓库类"""

    def __init__(self, session: Session):
        """初始化"""
        self.session = session

    def get_session(self) -> Session:
        """获取数据库会话"""
        if self.session is None:
            raise ValueError("Session has not been set in the repository.")
        return self.session

    def _execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行sql语句，并返回字典列表
        :param sql: SQL语句
        :param params: SQL参数
        :return: 查询结果字典列表
        :raises SQLExecutionError: 如果SQL执行失败
        """
        try:
            stmt = text(sql)
            final_params = params or {}
            logger.debug(f"Executing SQL: {sql} with params: {final_params}")
            result = self.session.execute(statement=stmt, params=final_params)
            return cursor_result_to_list(result)
        except SQLAlchemyError as e:
            logger.error(f"SQL execution failed. SQL: {sql}, Params: {final_params}, Error: {e}")
            raise SQLExecutionError(f"SQL execution failed: {e}") from e

    def _fetch_count(self, sql: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        获取对应sql和参数的记录总数
        :param sql: SQL语句
        :param params: SQL参数
        :return: 记录总数，若无结果则返回0
        :raises SQLExecutionError: 如果SQL执行失败
        """
        try:
            # 使用 CTE (Common Table Expression) 提高可读性和潜在性能
            count_sql = f"WITH _count_subquery AS ({sql}) SELECT count(*) FROM _count_subquery"
            stmt = text(count_sql)
            final_params = params or {}
            logger.debug(f"Fetching count with SQL: {count_sql} and params: {final_params}")

            count = self.session.execute(stmt, params=final_params).scalar()

            result = int(count) if count is not None else 0
            logger.debug(f"Fetch count result: {result}")
            return result
        except SQLAlchemyError as e:
            logger.error(f"Failed to fetch count. SQL: {sql}, Params: {final_params}, Error: {e}")
            raise SQLExecutionError(f"Failed to fetch count: {e}") from e

    def _build_sqlalchemy_query(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        params: Optional[List[FilterCondition]] = None,
    ) -> Query:
        """
        根据输入的数据库类及查询条件构建查询
        :param entity_cls: 实体类
        :param params: 过滤条件列表
        :return: SQLAlchemy查询对象
        """
        return build_sqlalchemy_query(session=self.session, entity_cls=entity_cls, params=params)

    def _delete_entity_by_params(self, entity_cls: Type[GenericSqlalchemyEntity], params: List[FilterCondition]) -> int:
        """
        【高危操作】根据参数删除实体，禁止无条件删除！
        :param entity_cls: 实体类
        :param params: 过滤条件列表，不能为空，否则抛出异常
        :return: 实际删除的记录数
        :raises ValueError: 若params为空，禁止全表删除
        :raises EntityError: 删除过程中发生异常
        """
        if not params:
            logger.error(f"禁止无条件删除实体: {entity_cls.__name__}，params为空")
            raise ValueError(f"禁止无条件删除实体: {entity_cls.__name__}，params不能为空")
        try:
            logger.info(f"准备删除实体: {entity_cls.__name__}，条件: {params}")
            query = self._build_sqlalchemy_query(entity_cls=entity_cls, params=params)
            deleted_count = query.delete(synchronize_session=False)
            logger.info(f"实体删除完成: {entity_cls.__name__}，条件: {params}，删除数量: {deleted_count}")
            return deleted_count
        except SQLAlchemyError as e:
            logger.error(f"删除实体失败: {entity_cls.__name__}，条件: {params}，错误: {e}")
            raise EntityError(f"删除实体失败: {entity_cls.__name__}，错误: {e}") from e

    def __insert_entity(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        dict_data: Dict[str, Any],
    ) -> GenericSqlalchemyEntity:
        """
        准备并添加实体到会话，但不执行flush。
        这允许上层调用者在一个事务中批量处理。
        :param entity_cls: 要插入的实体类
        :param dict_data: 要插入的数据
        :return: 准备好的实体对象
        :raises EntityError: 准备实体过程中发生异常
        :raises SQLAlchemyError: 数据库会话操作发生异常
        """
        try:
            entity = prepare_entity_by_dict(entity_cls=entity_cls, dict_data=dict_data)
            self.session.add(entity)
            logger.debug(f"实体已添加到会话: {entity_cls.__name__}, 数据: {dict_data}")
            return entity
        except SQLAlchemyError as e:
            logger.error(f"添加实体到会话失败: {entity_cls.__name__}, 数据: {dict_data}, 错误: {e}")
            raise
        except Exception as e:
            logger.error(f"准备实体失败: {entity_cls.__name__}, 数据: {dict_data}, 错误: {e}")
            raise EntityError(f"准备实体失败: {entity_cls.__name__}, 错误: {e}") from e

    def _insert_entity_by_dict_get_entity(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        dict_data: Dict[str, Any],
    ) -> GenericSqlalchemyEntity:
        """
        根据字典信息来插入数据库数据（内部方法）
        :param entity_cls: 实体类
        :param dict_data: 字段数据字典
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        logger.info(f"准备插入实体: {entity_cls.__name__}, 数据: {dict_data}")
        try:
            # 通过字典准备实体对象
            entity_dict = prepare_dict_for_entity(entity_cls=entity_cls, dict_data=dict_data)
            return self.__insert_entity(entity_cls=entity_cls, dict_data=entity_dict)
        except (EntityError, SQLAlchemyError) as e:
            # 捕获已知异常并重新抛出，以保持错误类型
            logger.error(f"插入实体失败: {entity_cls.__name__}, 数据: {dict_data}, 错误: {e}")
            raise

    def _insert_entity_by_model_get_entity(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        model_data: BaseModel,
    ) -> GenericSqlalchemyEntity:
        """
        根据model信息来插入数据库数据（内部方法）
        :param entity_cls: 实体类
        :param model_data: Pydantic模型数据
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        logger.info(f"准备插入实体: {entity_cls.__name__}, 模型数据: {model_data}")
        try:
            # 将model转换为字典
            dict_data = model_to_flat_dict(model_data)
            # 调用字典版本的插入方法
            return self._insert_entity_by_dict_get_entity(entity_cls=entity_cls, dict_data=dict_data)
        except (EntityError, SQLAlchemyError) as e:
            logger.error(f"插入实体失败: {entity_cls.__name__}, 模型数据: {model_data}, 错误: {e}")
            raise

    def _insert_entity_by_dict(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        dict_data: Dict[str, Any],
    ) -> GenericSqlalchemyEntity:
        """
        根据字典信息来插入数据库数据，并返回实体对象。
        注意：此方法不会返回数据库生成的主键，因为flush操作由UOW管理。
        实体的主键将在UOW提交后被填充。
        :param entity_cls: 实体类
        :param dict_data: 字段数据字典
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        entity = self._insert_entity_by_dict_get_entity(entity_cls=entity_cls, dict_data=dict_data)
        return entity

    def _insert_entity_by_model(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        model_data: BaseModel,
    ) -> GenericSqlalchemyEntity:
        """
        根据model信息来插入数据库数据，并返回实体对象。
        注意：此方法不会返回数据库生成的主键，因为flush操作由UOW管理。
        实体的主键将在UOW提交后被填充。
        :param entity_cls: 实体类
        :param model_data: Pydantic模型数据
        :return: 新插入的实体对象
        :raises EntityError: 插入过程中发生异常
        """
        # 直接将model_data转为字典，调用_insert_entity_by_dict实现插入
        dict_data = model_to_flat_dict(model_data)
        return self._insert_entity_by_dict(entity_cls=entity_cls, dict_data=dict_data)

    def _update_entity_by_dict(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        update_data: Dict[str, Any],
        filter_params: List[FilterCondition],
        limited_col_list: Optional[List[str]] = None,
    ) -> int:
        """
        根据参数更新数据库，返回影响行数
        :param entity_cls:
        :param update_data:
        :param filter_params:
        :param limited_col_list:当申明的时候只更新字段内的，否则全更新
        :return:
        """
        try:
            query = self._build_sqlalchemy_query(entity_cls=entity_cls, params=filter_params)
            entity_dict = prepare_dict_for_entity(entity_cls=entity_cls, dict_data=update_data)
            final_update_data = prepare_update_data(
                entity_cls=entity_cls,
                update_data=entity_dict,
                limited_col_list=limited_col_list,
            )

            if not final_update_data:
                logger.warning("没有要更新的字段，操作被跳过")
                return 0

            # query.update() 期望一个以列对象为键的字典
            column_data = {getattr(entity_cls, k): v for k, v in final_update_data.items()}
            effected_rows = query.update(column_data, synchronize_session=False)

            if effected_rows == 0:
                # 将 List[FilterCondition] 转为 Dict[str, Any]
                filter_dict = {fc.column_name: fc.value for fc in filter_params}
                raise EntityNotFoundError(cls=entity_cls, params=filter_dict)
            return effected_rows
        except SQLAlchemyError as e:
            logger.error(f"更新实体失败: {entity_cls.__name__}, 错误: {e}")
            raise EntityError(f"更新实体失败: {e}") from e

    def _update_entity_by_model(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        update_model: BaseModel,
        filter_params: List[FilterCondition],
        limited_col_list: Optional[List[str]] = None,
    ) -> int:
        """
        根据model更新数据库，返回影响行数
        :param entity_cls:
        :param update_model:
        :param filter_params:
        :param limited_col_list:当申明的时候只更新字段内的，否则全更新
        :return:
        """
        try:
            return self._update_entity_by_dict(
                entity_cls=entity_cls,
                update_data=model_to_flat_dict(update_model),
                filter_params=filter_params,
                limited_col_list=limited_col_list,
            )
        except (EntityError, SQLAlchemyError) as e:
            logger.error(f"根据模型更新实体失败: {entity_cls.__name__}, 错误: {e}")
            raise

    def _fetch_list(
        self,
        source_sql: str,
        filter_condition_list: Optional[List[FilterCondition]] = None,
        order_condition_list: Optional[List[OrderCondition]] = None,
        offset: int = 0,
        limit: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        根据过滤条件和排序条件查询任意子查询/表/视图数据，返回字典列表，支持分页
        :param source_sql: 子查询SQL或表名。警告：此参数直接参与SQL拼接，若来自用户输入，必须经过严格的白名单验证！
        :param filter_condition_list: 过滤条件列表
        :param order_condition_list: 排序条件列表
        :param offset: 偏移量
        :param limit: 返回数量
        :return: 查询结果的字典列表
        :raises ValueError: source_sql为空或非法
        :raises SQLExecutionError: SQL执行失败
        """
        if not source_sql or not isinstance(source_sql, str):
            logger.error("source_sql不能为空，且必须为字符串")
            raise ValueError("source_sql不能为空，且必须为字符串")

        try:
            # 判断是否为简单表名（不含空格和括号），否则视为select语句
            if " " in source_sql or "(" in source_sql or "SELECT" in source_sql.upper():
                from_sql = f"({source_sql}) AS t"
            else:
                from_sql = source_sql

            # 构建where子句
            where_clause, params = build_where_clause(filter_condition_list or [])
            where_sql = f"WHERE {where_clause}" if where_clause else ""

            # 构建order by子句
            order_sql = build_order_by_clause(order_condition_list)

            # 构建分页
            limit_offset_sql = build_pagination_clause(limit, offset) if limit is not None else ""

            # 拼接完整SQL
            sql = f"SELECT * FROM {from_sql} {where_sql} {order_sql} {limit_offset_sql}"
            logger.debug(f"_fetch_list SQL: {sql} <= {params}")
            stmt = text(sql)
            result = self.session.execute(stmt, params=params)
            return cursor_result_to_list(result)
        except SQLAlchemyError as e:
            logger.error(f"执行列表查询失败: {sql}, 参数: {params}, 错误: {e}")
            raise SQLExecutionError(f"执行列表查询失败: {e}") from e

    def bulk_insert(
        self,
        entity_cls: Type[GenericSqlalchemyEntity],
        dict_list: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """
        批量插入数据，并返回传入的数据字典列表。
        注意：因为移除了flush，返回的字典中不会包含数据库生成的ID。
        UOW flush后，`return_defaults=True`会填充默认值到字典中。
        :param entity_cls: 实体类
        :param dict_list: 待插入的数据字典列表
        :return: 传入的数据字典列表
        :raises SQLExecutionError: 插入过程中发生异常
        """
        if not dict_list:
            return []

        try:
            # 使用 bulk_insert_mappings 提高性能
            # return_defaults=True 会在插入后填充默认值（如自增ID），这会在UOW flush时发生
            mapper = inspect(entity_cls)
            self.session.bulk_insert_mappings(mapper=mapper, mappings=dict_list, return_defaults=True)

            # 从 dict_list 中提取 id 的操作被移除，因为没有flush
            return dict_list
        except SQLAlchemyError as e:
            logger.error(f"批量插入数据失败: {entity_cls.__name__}, 错误: {e}")
            raise SQLExecutionError(f"批量插入数据失败: {e}") from e

    def _fetch_first(
        self,
        sql: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        获取对应sql和参数的对应第一条数据
        :param sql: 原始SQL查询
        :param params: SQL参数
        :return: 查询结果的第一条数据，或None
        """
        # 使用子查询包装原始SQL并添加 LIMIT 1，
        # 这是确保只从数据库获取一条记录的内存高效方法，
        # 避免了在应用层处理可能存在的巨大结果集。
        sql_stmt = f"SELECT * FROM ({sql}) AS first_only LIMIT 1"
        result = self._execute_sql(sql=sql_stmt, params=params)

        if result:
            return result[0]
        return None

    def _fetch_first_to_model(
        self,
        model_cls: Type[GenericBaseModel],
        sql: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> Optional[GenericBaseModel]:
        """
        获取对应sql和参数的第一条记录，并转换为模型对象
        :param model_cls: Pydantic模型类
        :param sql: 原始SQL查询
        :param params: SQL参数
        :return: 转换后的模型对象，或None
        """
        first_result = self._fetch_first(sql=sql, params=params)
        if first_result:
            model_dict = prepare_dict_for_model(model_cls=model_cls, dict_data=first_result)
            # 使用model_validate保证Pydantic v2的默认值和类型校验
            return model_cls.model_validate(model_dict)
        return None

    def _fetch_all_to_model(
        self,
        model_cls: Type[GenericBaseModel],
        sql: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[GenericBaseModel]:
        """
        获取对应sql和参数的所有记录
        :param model_cls: 模型类
        :param sql: SQL语句
        :param params: SQL参数
        :return: 模型对象列表
        """
        result_list = self._execute_sql(sql=sql, params=params)
        # 使用model_validate保证Pydantic v2的默认值和类型校验
        return [
            model_cls.model_validate(prepare_dict_for_model(model_cls=model_cls, dict_data=result_dict))
            for result_dict in result_list
        ]

    def _paginate(
        self,
        result_type: Type[GenericBaseModel],
        total_params: PageInitParams,
        page_params: PageFilterParams,
    ) -> PaginationCarrier[GenericBaseModel]:
        """获取分页结果

        Args:
            result_type: 结果模型类型
            total_params: 分页初始化参数. 警告: total_params.sql 直接参与SQL拼接，需确保其来源安全。
            page_params: 分页过滤参数

        Returns:
            PaginationCarrier: 分页结果载体
        """
        try:
            # 准备文本过滤列表
            search_segment_list = [x.strip() for x in page_params.search_text.split(" ") if x]

            # 准备过滤sql及参数
            filter_sql, filter_para_dict = build_full_text_search_clause(
                search_segment_list=search_segment_list,
                filter_columns=total_params.filter_columns,
            )
            extra_sql, extra_para_dict = build_extra_query_clause(page_params.extra_params)

            sql_segments = []
            if filter_sql:
                sql_segments.append(filter_sql)
            if extra_sql:
                sql_segments.append(extra_sql)
            sql_segment_clause = f" AND {' AND '.join(sql_segments)}" if sql_segments else ""

            execute_para_dict = filter_para_dict | extra_para_dict
            sql_for_data = f"""SELECT * FROM ({total_params.sql}) pqb
             WHERE 1=1{sql_segment_clause}
             {build_order_by_clause(total_params.order_columns)}
             {build_pagination_clause(page_params.page_size, page_params.page_index)}"""

            used_params = total_params.params or {}
            full_para_dict = used_params | execute_para_dict

            sql_for_count = f"SELECT * FROM ({total_params.sql}) pqb WHERE 1=1{sql_segment_clause}"

            return PaginationCarrier(
                search_text=page_params.search_text,
                total_count=self._fetch_count(sql=total_params.sql, params=total_params.params),
                filter_count=self._fetch_count(sql=sql_for_count, params=full_para_dict),
                page_index=page_params.page_index,
                page_size=page_params.page_size,
                draw=page_params.draw,
                data=self._fetch_all_to_model(model_cls=result_type, sql=sql_for_data, params=full_para_dict),
            )
        except SQLExecutionError as e:
            logger.error(f"分页查询失败: {e}")
            raise
