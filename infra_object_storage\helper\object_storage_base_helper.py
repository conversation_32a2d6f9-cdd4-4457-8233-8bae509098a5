"""
对象存储基类
"""

from enum import Enum
from typing import Optional
from urllib.parse import urlparse, urlunparse

import uuid_utils as uuid
from infra_utility.credential_helper import generate_random_string

from infra_object_storage.error import BucketNotExistedError


class ObjectStorageTypeEnum(str, Enum):
    """存储类型枚举"""

    COS = "cos"
    MINIO = "minio"
    OSS = "oss"
    S3 = "s3"


class ObjectStorageBaseHelper:
    """
    对象存储基类
    """

    @staticmethod
    def generate_storage_name(source_file_name: str) -> str:
        """
        根据输入名字生成文件名
        :param source_file_name:原文件名
        :return:
        """

        def __get_file_extension(file_name: str) -> str:
            if "." in file_name:
                return "." + file_name.split(".")[-1]
            else:
                return ""

        # filter_file_name = str(source_file_name).strip().replace(" ", "_").lower()
        # safe_file_name = sub(r"(?u)[^-\w.]", "", filter_file_name)
        # file_name, file_extension = splitext(safe_file_name)
        # timestamp_token = datetime.now().strftime("%Y%m%d%H%M%S")
        shorter_name = str(uuid.uuid7())
        random_token = generate_random_string()
        file_extension = __get_file_extension(file_name=source_file_name)
        return f"{shorter_name}-{random_token}{file_extension}"

    def __init__(self, default_bucket: str):
        """初始化默认篮子"""
        self._default_bucket = default_bucket

    def _prepare_bucket_name(self, bucket_name: Optional[str] = None) -> str:
        """
        获取存储对象篮子名称
        :param bucket_name:
        :return:
        """
        if not bucket_name and not self._default_bucket:
            raise BucketNotExistedError("please input bucket or setup default bucket")
        return bucket_name if bucket_name else self._default_bucket

    def get_default_bucket(self) -> str:
        """
        获取默认篮子
        :return: 默认篮子名
        """
        return self._default_bucket

    @staticmethod
    def remove_url_parameters(url: str) -> str:
        """去除url上无用的参数"""
        parsed_url = urlparse(url)
        return urlunparse(
            (parsed_url.scheme, parsed_url.netloc, parsed_url.path, "", "", "")
        )
