"""
数据库工厂类模块

本模块提供了数据库对象的工厂类，用于创建和管理数据库连接实例，包括：
1. 同步数据库实例的创建和管理
2. 数据库配置的验证
3. 实例的缓存和复用
4. 实例的清理

工厂类使用单例模式确保相同配置的数据库连接被复用。
"""

from typing import Dict

from infra_database.database.async_database import AsyncDatabase
from infra_database.database.sync_database import SyncDatabase
from infra_database.settings import DatabaseSettings


class DatabaseFactory:
    """
    数据库工厂类

    用于创建和管理数据库连接实例，提供以下功能：
    1. 创建同步数据库实例
    2. 验证数据库配置
    3. 缓存和复用数据库实例
    4. 清理数据库实例

    工厂类使用单例模式，确保相同配置的数据库连接被复用，避免重复创建连接。

    属性:
        _sync_instances: 同步数据库实例的缓存字典，键为连接字符串，值为数据库实例
    """

    # 单例实例
    _sync_instances: Dict[str, SyncDatabase] = {}
    _async_instances: Dict[str, AsyncDatabase] = {}

    @classmethod
    def _get_instance_key(cls, setting: DatabaseSettings) -> str:
        """
        获取数据库实例的唯一键

        根据数据库配置生成唯一的连接字符串，用于标识和缓存数据库实例。

        参数:
            setting: 数据库配置对象

        返回:
            str: 格式化的连接字符串，如 "postgresql://user@host:port/database"
        """
        return f"{setting.driver}://{setting.user}@{setting.host}:{setting.port}/{setting.database}"

    @classmethod
    def _validate_setting(cls, setting: DatabaseSettings) -> None:
        """
        验证数据库配置

        检查数据库配置的必填字段是否都已填写。

        参数:
            setting: 数据库配置对象

        异常:
            ValueError: 当必填字段未填写时抛出
        """
        if not setting.driver:
            raise ValueError("数据库驱动不能为空")
        if not setting.user:
            raise ValueError("数据库用户名不能为空")
        if not setting.password:
            raise ValueError("数据库密码不能为空")
        if not setting.host:
            raise ValueError("数据库主机不能为空")
        if not setting.port:
            raise ValueError("数据库端口不能为空")
        if not setting.database:
            raise ValueError("数据库名称不能为空")

    @classmethod
    def create_sync_database(cls, setting: DatabaseSettings) -> SyncDatabase:
        """
        创建同步数据库对象

        根据配置创建或获取缓存的同步数据库实例。如果相同配置的实例已存在，
        则直接返回缓存的实例，否则创建新实例。

        参数:
            setting: 数据库配置对象

        返回:
            SyncDatabase: 同步数据库实例

        异常:
            ValueError: 当配置验证失败时抛出
        """
        # 验证配置
        cls._validate_setting(setting)

        # 获取实例键
        instance_key = cls._get_instance_key(setting)

        # 如果实例不存在，创建新实例
        if instance_key not in cls._sync_instances:
            cls._sync_instances[instance_key] = SyncDatabase(setting)

        return cls._sync_instances[instance_key]

    @classmethod
    def create_async_database(cls, setting: DatabaseSettings) -> AsyncDatabase:
        """
        创建异步数据库对象

        参数：
            setting: 数据库配置

        返回：
            AsyncDatabase: 异步数据库对象

        异常：
            ValueError: 配置验证失败
        """
        # 验证配置
        cls._validate_setting(setting)

        # 获取实例键
        instance_key = cls._get_instance_key(setting)

        # 如果实例不存在，创建新实例
        if instance_key not in cls._async_instances:
            cls._async_instances[instance_key] = AsyncDatabase(setting)

        return cls._async_instances[instance_key]

    @classmethod
    async def clear_instances(cls) -> None:
        """
        清除所有数据库实例

        清理所有缓存的数据库实例，通常在应用程序关闭时调用。
        此方法会释放所有数据库连接资源。
        """
        for sync_instance in cls._sync_instances.values():
            sync_instance.dispose()
        cls._sync_instances.clear()

        for async_instance in cls._async_instances.values():
            await async_instance.dispose()
        cls._async_instances.clear()
