import uuid
from time import sleep

import pytest
from pytest import mark

from infra_object_storage.helper.s3_helper import S3Helper


@pytest.fixture
def temp_bucket_name(s3_helper: S3Helper):
    """
    创建一个临时的存储桶，用于测试
    """
    bucket_name = f"test-bucket-s3-{uuid.uuid4()}"
    s3_helper.make_bucket(bucket_name)
    yield bucket_name
    # 清理：删除存储桶内的所有对象后删除存储桶
    try:
        for obj in s3_helper.list_objects(bucket_name):
            s3_helper.delete_object(obj, bucket_name)
        s3_helper.remove_bucket(bucket_name)
    except Exception as e:
        print(f"Error cleaning up bucket {bucket_name}: {e}")


def test_bucket_operations(s3_helper: S3Helper):
    """
    测试存储桶的创建、存在性检查和删除
    """
    bucket_name = f"test-bucket-ops-s3-{uuid.uuid4()}"
    
    # 初始状态检查
    assert not s3_helper.bucket_exists(bucket_name), "Bucket should not exist initially"
    
    # 创建存储桶
    s3_helper.make_bucket(bucket_name)
    # S3 的最终一致性，需要等待
    sleep(5)
    assert s3_helper.bucket_exists(bucket_name), "Bucket should exist after creation"
    
    # 删除存储桶
    s3_helper.remove_bucket(bucket_name)
    sleep(5)
    assert not s3_helper.bucket_exists(bucket_name), "Bucket should not exist after removal"


def test_object_operations(s3_helper: S3Helper, temp_bucket_name: str):
    """
    测试对象的上传、下载、存在性检查和删除
    """
    object_name = f"test-object-{uuid.uuid4()}.txt"
    object_data = b"This is a test file for S3."
    
    # 初始状态检查
    assert not s3_helper.object_exists(object_name, temp_bucket_name), "Object should not exist initially"
    
    # 上传文件
    s3_helper.upload_object(object_name, object_data, temp_bucket_name)
    assert s3_helper.object_exists(object_name, temp_bucket_name), "Object should exist after upload"
    
    # 下载文件
    downloaded_data = s3_helper.download_object(object_name, temp_bucket_name)
    assert downloaded_data == object_data, "Downloaded data should match uploaded data"
    
    # 删除文件
    s3_helper.delete_object(object_name, temp_bucket_name)
    assert not s3_helper.object_exists(object_name, temp_bucket_name), "Object should not exist after deletion"


def test_get_file_url(s3_helper: S3Helper, temp_bucket_name: str):
    """
    测试生成预签名URL的功能
    """
    object_name = f"test-url-object-{uuid.uuid4()}.txt"
    object_data = b"Test file for URL generation."
    s3_helper.upload_object(object_name, object_data, temp_bucket_name)
    
    # 获取预签名URL
    url = s3_helper.get_object_url(object_name, temp_bucket_name, expires_seconds=60)
    assert isinstance(url, str)
    assert "http" in url
    assert object_name in url
    assert "AWSAccessKeyId" in url
    
    s3_helper.delete_object(object_name, temp_bucket_name)


def test_list_and_delete_objects(s3_helper: S3Helper, temp_bucket_name: str):
    """
    测试列出和批量删除对象的功能
    """
    object_names = [f"test-list-obj-{i}-{uuid.uuid4()}.txt" for i in range(3)]
    for name in object_names:
        s3_helper.upload_object(name, b"list test", temp_bucket_name)
        
    # 列出对象
    listed_objects = list(s3_helper.list_objects(temp_bucket_name))
    assert all(name in listed_objects for name in object_names)
    
    # 批量删除对象
    s3_helper.delete_objects(object_names, temp_bucket_name)
    
    listed_objects_after_delete = list(s3_helper.list_objects(temp_bucket_name))
    assert not any(name in listed_objects_after_delete for name in object_names)


def test_b_public_read_operations(s3_helper: S3Helper):
    """
    测试设置对象为公开读取和获取ACL的功能
    """
    object_name = f"test-public-object-{uuid.uuid4()}.txt"
    s3_helper.upload_object(object_name, b"public read test")
    
    # 设置为公开读取
    s3_helper.set_object_public_read(object_name)
    
    # 获取ACL进行验证
    # 注意：这需要 GetObjectAcl 权限
    acl = s3_helper.get_object_acl(object_name)
    is_public_read = any(
        grant.get('Permission') == 'READ' and 
        grant.get('Grantee', {}).get('URI') == 'http://acs.amazonaws.com/groups/global/AllUsers'
        for grant in acl.get('Grants', [])
    )
    assert is_public_read, "Object should be public-read after setting ACL"
    
    s3_helper.delete_object(object_name)


@mark.dependency()
def test_a_bucket_policy(s3_helper: S3Helper):
    """
    测试设置存储桶策略
    """
    # 获取默认存储桶的策略
    default_bucket = s3_helper.get_target_bucket_name(None)
    policy = s3_helper.get_bucket_policy()
    
    # 验证策略内容
    assert policy["Statement"][0]["Resource"] == f"arn:aws:s3:::{default_bucket}/*"
    assert policy["Statement"][0]["Effect"] == "Allow"
    assert policy["Statement"][0]["Action"] == "s3:GetObject"
    assert policy["Statement"][0]["Principal"] == "*"  # 验证是公共访问策略