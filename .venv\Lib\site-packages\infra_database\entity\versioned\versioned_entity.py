"""
版本化实体类，提供版本控制功能
"""

from datetime import datetime
from typing import Optional, Type, TypeVar

from sqlalchemy import DateTime, Integer, String, Text, func, text
from sqlalchemy.orm import Mapped, mapped_column

from infra_database.entity.basic_entity import BasicEntity
from infra_database.entity.versioned.history_entity import HistoryEntity
from infra_database.error import UnexpectedDataType

# 定义泛型类型
GenericVersionedEntity = TypeVar('GenericVersionedEntity', bound='VersionedEntity')


class VersionedEntity(BasicEntity):
    """
    版本化实体基类

    提供版本控制功能，包括：
    1. 版本号管理
    2. 操作者信息记录
    3. 操作时间记录
    4. 备注信息
    """

    __history_entity__: Type[HistoryEntity]
    __abstract__ = True

    handler_category: Mapped[str] = mapped_column(String(255), comment="操作者类型", nullable=False)
    handler_id: Mapped[str] = mapped_column(String(40), comment="操作者id", nullable=False)
    handled_on: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        comment="操作于",
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    remark: Mapped[Optional[str]] = mapped_column(Text, comment="备注", nullable=True)
    version: Mapped[int] = mapped_column(Integer, comment="版本", server_default=text("1"), nullable=False)

    __mapper_args__ = {
        "version_id_col": version,
        "version_id_generator": lambda x: (x or 0) + 1,
    }

    @classmethod
    def get_history_entity(cls) -> Type[HistoryEntity]:
        """
        获取类对应的历史实体类

        Returns:
            Type[HistoryEntity]: 历史实体类

        Raises:
            UnexpectedDataType: 当历史实体类不是HistoryEntity的子类时抛出
        """
        if issubclass(cls.__history_entity__, HistoryEntity):
            return cls.__history_entity__
        raise UnexpectedDataType(f"History entity {cls.__history_entity__.__name__} is not a subclass of HistoryEntity")
