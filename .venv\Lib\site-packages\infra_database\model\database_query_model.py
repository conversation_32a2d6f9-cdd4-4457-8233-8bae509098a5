"""
数据库查询相关的模型定义

本模块提供了用于构建数据库查询的各种模型类，包括过滤条件、排序条件、分页参数等。
这些模型类主要用于构建SQL查询语句和处理查询结果。
"""

import re
from typing import Any, Dict, List, Literal, Optional

from infra_basic.model.base_plus_model import BasePlusModel
from pydantic import field_validator


class FilterCondition(BasePlusModel):
    """
    过滤条件模型

    用于构建SQL查询的WHERE子句条件。

    属性:
        column_name (str): 需要过滤的列名。
        operator (str): 查询操作符，例如 "="、">"、"LIKE" 等。默认为 "="。
        value (Any): 用于过滤的值。
    """

    column_name: str
    operator: str = "="
    value: Any


class OrderCondition(BasePlusModel):
    """
    排序条件模型

    用于构建SQL查询的ORDER BY子句。

    属性:
        column_name (str): 需要排序的列名。
        order (Literal["asc", "desc"]): 排序方向，可选值为 "asc" (升序) 或 "desc" (降序)。默认为 "asc"。
    """

    column_name: str
    order: Literal["asc", "desc"] = "asc"

    @field_validator("column_name")
    def validate_column_name(cls, v: str) -> str:
        """
        验证列名以防止SQL注入。

        只允许字母、数字和下划线。

        参数:
            v (str): 待验证的列名。

        返回:
            str: 验证通过的列名。

        异常:
            ValueError: 如果列名包含非法字符。
        """
        if not re.match(r"^[a-zA-Z0-9_]+$", v):
            raise ValueError("Invalid column name for ordering")
        return v

    def build_order_sql(self) -> str:
        """
        构建排序SQL语句片段。

        返回:
            str: 格式化的排序SQL语句，例如 "column_name asc"。
        """
        return f"{self.column_name} {self.order}"


class PageInitParams(BasePlusModel):
    """
    分页初始化参数模型

    用于初始化分页查询的基本参数，通常在后端服务层定义。

    属性:
        sql (str): 基础SQL查询语句，不包含WHERE和ORDER BY子句。
        params (Optional[Dict[str, Any]]): SQL查询绑定的参数字典。默认为 None。
        filter_columns (Optional[List[str]]): 允许进行模糊搜索的列名列表。默认为 None。
        order_columns (Optional[List[OrderCondition]]): 允许进行排序的列及其默认排序方向。默认为 None。
    """

    sql: str
    params: Optional[Dict[str, Any]] = None
    filter_columns: Optional[List[str]] = None
    order_columns: Optional[List[OrderCondition]] = None


class QueryCondition(BasePlusModel):
    """
    精确查询条件模型

    用于构建精确的、非模糊搜索的查询条件。

    属性:
        column_name (str): 需要查询的列名。
        operator (str): 查询操作符。默认为 "="。
        value (Any): 查询值。默认为 None。
    """

    column_name: str
    operator: str = "="
    value: Any = None


class PageFilterParams(BasePlusModel):
    """
    分页过滤参数模型

    用于接收前端传递的分页、排序和过滤请求参数。

    属性:
        draw (int): DataTables插件用于异步请求计数的参数，原样返回即可。默认为 1。
        search_text (str): 全局模糊搜索的文本。默认为空字符串。
        page_size (int): 每页显示的记录数。默认为 20。
        page_index (int): 当前页码 (0-based)。默认为 0。
        extra_params (Optional[List[QueryCondition]]): 额外的精确查询条件列表。默认为 None。
    """

    draw: int = 1
    search_text: str = ""
    page_size: int = 20
    page_index: int = 0
    extra_params: Optional[List[QueryCondition]] = None
