"""
资源与文件之间的关系实体类
"""

from infra_database.entity.versioned.versioned_entity import VersionedEntity
from sqlalchemy import Column, Index, String, Text

from infra_object_storage.entity.history.file_relationship_history import (
    FileRelationshipHistoryEntity,
)


class FileRelationshipEntity(VersionedEntity):
    """
    资源与文件之间的关系
    """

    __tablename__ = "st_file_relationship"
    __history_entity__ = FileRelationshipHistoryEntity
    __table_args__ = {"comment": "资源与文件之间的关系"}
    file_id = Column(String(40), nullable=False, comment="文件id", index=True)
    res_category = Column(String(255), comment="资源类型", nullable=False)
    res_id = Column(String(40), comment="资源id", nullable=False)
    relationship = Column(
        String(255), comment="资源与文件之间的关系说明", index=True, nullable=False
    )
    summary = Column(Text, comment="摘要")


Index(
    "idx_file_relationship_resource_info",
    FileRelationshipEntity.res_id,
    FileRelationshipEntity.res_category,
)
