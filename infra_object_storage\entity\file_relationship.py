"""
资源与文件之间的关系实体类
"""

from typing import Optional

from infra_database.entity.versioned.versioned_entity import VersionedEntity
from sqlalchemy import Index, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from infra_object_storage.entity.history.file_relationship_history import (
    FileRelationshipHistoryEntity,
)


class FileRelationshipEntity(VersionedEntity):
    """
    资源与文件之间的关系
    """

    __tablename__ = "st_file_relationship"
    __history_entity__ = FileRelationshipHistoryEntity
    __table_args__ = {"comment": "资源与文件之间的关系"}

    file_info_id: Mapped[str] = mapped_column(
        String(40), nullable=False, comment="文件id", index=True
    )
    resource_category: Mapped[str] = mapped_column(
        String(255), comment="资源类型", nullable=False
    )
    resource_id: Mapped[str] = mapped_column(
        String(40), comment="资源id", nullable=False
    )
    relationship: Mapped[str] = mapped_column(
        String(255), comment="资源与文件之间的关系说明", index=True, nullable=False
    )
    summary: Mapped[Optional[str]] = mapped_column(Text, comment="摘要")


# 创建复合索引
Index(
    "idx_file_relationship_resource_info",
    FileRelationshipEntity.resource_id,
    FileRelationshipEntity.resource_category,
)
