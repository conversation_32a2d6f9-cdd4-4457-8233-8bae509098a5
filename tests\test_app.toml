# 测试环境配置文件
# 此文件用于单元测试，包含模拟的配置信息

[database]
driver = "postgresql+psycopg2"
host = "************"
port = 5432
user = "infra_object_storage"
password = "infra_object_storage"
database = "infra_object_storage"
echo = true
# 连接池配置
pool_size = 5
max_overflow = 10
pool_timeout = 30
pool_recycle = 1800
pool_pre_ping = true
# PostgreSQL 特有的 JIT 配置
enable_jit = true
uow_log_enabled = true

[storage]
default_account_id = "s3_test"  # 设置默认账户ID

# 存储账户配置
# 使用 TOML 的表数组（Array of Tables）格式
# 每个 [[storage.accounts]] 块代表一个独立的账户配置

[[storage.accounts]]
id = "s3_test"
provider = "s3"
default_bucket = "qindingtech-bucket-dev"
aws_access_key_id = "********************"
aws_secret_access_key = "mXE3SKxsAO09Y/Nwf/WshUIr//re5F0B7G3EtSqU"
region_name = "ap-northeast-1"
endpoint_url = "https://s3.ap-northeast-1.amazonaws.com" # 可选，例如用于 MinIO

[[storage.accounts]]
id = "cos_test"
provider = "cos"
default_bucket = "dev-cos-**********"
secret_id = "AKIDCOXVFoBew42qUu7FCyb2KPGusfGgu2yf"
secret_key = "av4zkIhspLf8VineJk196uZ2HNNDuRo3"
region = "ap-nanjing"
endpoint_url = 'https://cos.ap-nanjing.myqcloud.com'

[[storage.accounts]]
id = "minio_test"
provider = "minio"
default_bucket = "minio-test"
host = "dev-minio.qindingtech.com"
port = 443
access_key = "admin"
secret_key = "Pa55word"
secure = true # 如果 MinIO 使用 HTTPS，请设置为 true

[[storage.accounts]]
id = "oss_test"
provider = "oss"
default_bucket = "minio-py-**********"
access_key_id = "LTAI4GFp1gCtrZETfLBbdXUw"
access_key_secret = "7jXI0TGtN8YekJ1h1mM8fmPPaRtcKE"
endpoint = "oss-cn-shanghai.aliyuncs.com"

