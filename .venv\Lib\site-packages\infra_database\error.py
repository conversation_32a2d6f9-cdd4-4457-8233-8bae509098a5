"""
异常定义类，提供实体相关的异常类型
"""

from typing import Any, Dict, Optional, Type


class DatabaseError(Exception):
    """
    数据库错误
    """

    pass


class DatabaseConnectionError(DatabaseError):
    """
    数据库连接异常

    当数据库连接失败或验证失败时抛出
    """

    def __init__(self, message: str = "数据库连接验证失败"):
        """
        初始化数据库连接异常

        Args:
            message: 错误信息，默认为"数据库连接验证失败"
        """
        super().__init__(message)


class SessionClosedError(DatabaseError):
    """
    数据库会话已关闭异常

    当尝试在已关闭的数据库会话上执行操作时抛出此异常
    """

    pass


class EntityError(DatabaseError):
    """
    实体异常基类

    所有实体相关的异常都继承自此类
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "实体操作发生错误"
        super().__init__(message)


class VersionNotExistsError(EntityError):
    """
    版本号不存在异常

    当尝试访问不存在的版本时抛出
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "请求的版本号不存在"
        super().__init__(message)


class EntityLackIdError(EntityError):
    """
    实体缺乏ID异常

    当实体缺少必要的ID字段时抛出
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "实体缺少必要的ID字段"
        super().__init__(message)


class UnexpectedDataType(EntityError):
    """
    非期待的数据类型异常

    当数据类型不符合预期时抛出
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "数据类型不符合预期"
        super().__init__(message)


class InvalidEntityClassError(EntityError):
    """
    无效的SQLAlchemy类异常

    当类不是有效的SQLAlchemy实体类时抛出
    """

    def __init__(self, cls: Type):
        message = f"{cls.__name__} 不是有效的SQLAlchemy实体类"
        super().__init__(message)


class InvalidEntityClassWithIdError(EntityError):
    """
    无效的带ID的SQLAlchemy类异常

    当类不是有效的带ID的SQLAlchemy实体类时抛出
    """

    def __init__(self, cls: Type):
        message = f"{cls.__name__} 不是有效的带ID的SQLAlchemy实体类"
        super().__init__(message)


class NoneSqlUpdatingResult(EntityError):
    """
    SQL更新无结果异常

    当SQL更新操作没有返回结果时抛出
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "SQL更新操作没有返回结果"
        super().__init__(message)


class EntityNotFoundError(EntityError):
    """
    实体未找到异常

    当根据条件无法找到实体时抛出
    """

    def __init__(self, cls: Type, params: Dict[str, Any]):
        message = f"未找到类型为 [{cls.__name__}] 的实体，查询条件: [{params}]"
        super().__init__(message)


class InvalidEntityClassAttrError(EntityError):
    """
    实体类属性无效异常

    当实体类缺少必要的属性时抛出
    """

    def __init__(self, cls: Type, attr: str):
        message = f"{cls.__name__} 实体类缺少必要的属性: {attr}"
        super().__init__(message)


class InvalidEntityRowError(EntityError):
    """
    无效的实体行异常

    当实体行数据无效时抛出
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "实体行数据无效"
        super().__init__(message)


class InvalidHistoryDataError(EntityError):
    """
    无效的历史数据异常

    当历史数据无效时抛出
    """

    def __init__(self, message: Optional[str] = None):
        if not message:
            message = "历史数据无效"
        super().__init__(message)


class InvalidEntityClassColumnError(EntityError):
    """
    实体类列无效异常

    当实体类缺少必要的列时抛出
    """

    def __init__(self, cls: Type, column_name: str):
        message = f"{cls.__name__} 实体类缺少必要的列: {column_name}"
        super().__init__(message)


class ConversionError(DatabaseError):
    """类型转换错误

    当类型转换失败时抛出此异常。
    """

    def __init__(self, message: str) -> None:
        """初始化转换错误

        Args:
            message: 错误信息
        """
        super().__init__(message)


class SQLExecutionError(DatabaseError):
    """SQL执行错误"""

    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class SQLInjectionError(DatabaseError):
    """SQL注入错误"""

    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class TransactionLogSaveError(DatabaseError):
    """在保存事务日志时发生错误。"""

    pass
