import uuid

import pytest
from dependency_injector.providers import Object
from infra_basic.resource.basic_resource import BasicResource
from infra_database.model.transaction_model import SystemTransactionModel
from infra_database.uow.session_proxy import SessionProxy
from infra_database.uow.sync_sql_alchemy_unit_of_work import SyncSqlAlchemyUnitOfWork

from infra_object_storage.model.object_storage_raw_model import ObjectStorageRawModel
from infra_object_storage.repository.object_storage_repository import (
    ObjectStorageRepository,
)
from tests.conftest import TestStubContainer


@pytest.fixture
def object_storage_repository(test_container):
    """从测试容器获取对象存储仓库实例"""
    return test_container.object_storage_container().object_storage_repository()


@pytest.fixture
def prepare_uow(test_container):
    """从测试容器获取UOW实例"""
    return test_container.uow()


@pytest.fixture
def session_proxy(test_container):
    """从测试容器获取会话代理实例"""
    return test_container.session_proxy


@pytest.mark.usefixtures("create_tables")
class TestObjectStorageRepository:
    """测试 ObjectStorageRepository"""

    def _prepare_storage_raw(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ) -> ObjectStorageRawModel:
        """准备一个存储原文并返回模型"""
        storage_raw = ObjectStorageRawModel(
            account_id="test_account",
            bucket_name="test_bucket",
            object_name="test_object.txt",
            checksum=str(uuid.uuid4()),
            size=1024,
        )
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            storage_id = object_storage_repository.insert_object_storage_raw(
                storage_raw
            )
            storage_raw.id = storage_id
        return storage_raw

    def _prepare_file_info(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
        object_storage_raw_id: str,
        transaction: SystemTransactionModel,
    ) -> str:
        """准备一个文件信息并返回文件ID"""
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_info_id = object_storage_repository.insert_file_info(
                object_storage_raw_id=object_storage_raw_id,
                file_name="test_file.txt",
                transaction=transaction,
            )
        return file_info_id

    def test_insert_and_found_same_storage_raw(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试插入和查找存储原文"""
        storage_raw = ObjectStorageRawModel(
            account_id="test_account",
            bucket_name="test_bucket",
            object_name="test_object.txt",
            checksum=str(uuid.uuid4()),
            size=1024,
        )
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_repository.insert_object_storage_raw(
                storage_raw
            )

        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            found_storage_raw = object_storage_repository.found_same_storage_raw(
                storage_raw
            )

        assert found_storage_raw is not None
        assert found_storage_raw.checksum == storage_raw.checksum
        assert found_storage_raw.size == storage_raw.size
        assert found_storage_raw.account_id == storage_raw.account_id
        assert found_storage_raw.bucket_name == storage_raw.bucket_name
        assert found_storage_raw.object_name == storage_raw.object_name

    def test_get_file_info_by_id(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试根据文件ID获取文件信息"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_get_file_info"
        )
        storage_raw = self._prepare_storage_raw(
            object_storage_repository, prepare_uow, session_proxy
        )
        assert storage_raw.id is not None
        file_info_id = self._prepare_file_info(
            object_storage_repository,
            prepare_uow,
            session_proxy,
            object_storage_raw_id=storage_raw.id,
            transaction=transaction,
        )

        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            found_file_info = object_storage_repository.get_file_info_by_id(
                file_info_id
            )

        assert found_file_info is not None
        assert found_file_info.id == file_info_id
        assert found_file_info.object_storage_raw_id == storage_raw.id
        assert found_file_info.original_name == "test_file.txt"

    def test_insert_file_info_and_get_storage_by_file_info_id(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
    ):
        """测试插入文件信息和通过文件ID获取存储信息"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_create"
        )
        storage_raw = self._prepare_storage_raw(
            object_storage_repository, prepare_uow, session_proxy
        )
        assert storage_raw.id is not None
        file_info_id = self._prepare_file_info(
            object_storage_repository,
            prepare_uow,
            session_proxy,
            object_storage_raw_id=storage_raw.id,
            transaction=transaction,
        )

        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            found_storage = object_storage_repository.get_storage_by_file_info_id(
                file_info_id
            )

        assert found_storage is not None
        assert found_storage.id == storage_raw.id
        assert found_storage.checksum == storage_raw.checksum
        assert found_storage.object_name == storage_raw.object_name

    def test_link_and_unlink_file_and_resource(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
        prepare_handler: BasicResource,
    ):
        """测试关联和取消关联文件与资源"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_link"
        )
        storage_raw = self._prepare_storage_raw(
            object_storage_repository, prepare_uow, session_proxy
        )
        assert storage_raw.id is not None
        file_info_id = self._prepare_file_info(
            object_storage_repository,
            prepare_uow,
            session_proxy,
            object_storage_raw_id=storage_raw.id,
            transaction=transaction,
        )
        relationship = "attachment"

        # Link
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            relationship_id = object_storage_repository.link_file_and_resource(
                file_info_id=file_info_id,
                resource=prepare_handler,
                relationship=relationship,
                transaction=transaction,
            )

        assert relationship_id is not None

        # Verify link
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            relationships = (
                object_storage_repository.get_file_related_relationship_list(
                    file_info_id
                )
            )
        assert len(relationships) == 1
        assert relationships[0].id == relationship_id
        assert relationships[0].relationship == relationship
        assert relationships[0].resource_id == prepare_handler.resource_info.id

        # Unlink
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_repository.unlink_file_and_resource(
                file_info_id=file_info_id,
                resource=prepare_handler,
                relationship=relationship,
                transaction=transaction,
            )

        # Verify unlink
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            relationships = (
                object_storage_repository.get_file_related_relationship_list(
                    file_info_id
                )
            )
        assert len(relationships) == 0

    def test_get_resource_related_file_list(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
        prepare_handler: BasicResource,
    ):
        """测试获取资源相关的文件列表"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_get"
        )
        storage_raw = self._prepare_storage_raw(
            object_storage_repository, prepare_uow, session_proxy
        )
        assert storage_raw.id is not None
        file_info_id = self._prepare_file_info(
            object_storage_repository,
            prepare_uow,
            session_proxy,
            object_storage_raw_id=storage_raw.id,
            transaction=transaction,
        )
        relationship = "attachment"

        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_repository.link_file_and_resource(
                file_info_id=file_info_id,
                resource=prepare_handler,
                relationship=relationship,
                transaction=transaction,
            )

        # Get list without relationship filter
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_list = object_storage_repository.get_resource_related_file_list(
                resource=prepare_handler
            )
        assert len(file_list) == 1
        assert file_list[0].id == file_info_id
        assert file_list[0].checksum == storage_raw.checksum
        assert file_list[0].object_name == storage_raw.object_name

        # Get list with relationship filter
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_list_filtered = (
                object_storage_repository.get_resource_related_file_list(
                    resource=prepare_handler, relationship=relationship
                )
            )
        assert len(file_list_filtered) == 1
        assert file_list_filtered[0].id == file_info_id
        assert file_list_filtered[0].object_name == storage_raw.object_name

        # Get list with wrong relationship filter
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            file_list_wrong_filter = (
                object_storage_repository.get_resource_related_file_list(
                    resource=prepare_handler, relationship="wrong_relationship"
                )
            )
        assert len(file_list_wrong_filter) == 0

    def test_delete_file(
        self,
        object_storage_repository: ObjectStorageRepository,
        prepare_uow: SyncSqlAlchemyUnitOfWork,
        session_proxy: Object[SessionProxy],
        prepare_handler: BasicResource,
    ):
        """测试删除文件"""
        transaction = SystemTransactionModel(
            handler_category="test", handler_id="test", action="test_delete"
        )
        storage_raw = self._prepare_storage_raw(
            object_storage_repository, prepare_uow, session_proxy
        )
        assert storage_raw.id is not None
        file_info_id = self._prepare_file_info(
            object_storage_repository,
            prepare_uow,
            session_proxy,
            object_storage_raw_id=storage_raw.id,
            transaction=transaction,
        )
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_repository.link_file_and_resource(
                file_info_id=file_info_id,
                resource=prepare_handler,
                relationship="attachment",
                transaction=transaction,
            )

        # Delete file
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            object_storage_repository.delete_file(
                file_info_id=file_info_id, transaction=transaction
            )

        # Verify file is deleted (by trying to get it)
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            # This is a bit indirect. We check if its relationships are gone.
            relationships = (
                object_storage_repository.get_file_related_relationship_list(
                    file_info_id
                )
            )
            # Also check if we can get storage by file id
            storage = object_storage_repository.get_storage_by_file_info_id(
                file_info_id
            )

        assert len(relationships) == 0
        assert storage is None

        # Verify storage raw still exists
        with prepare_uow:
            session_proxy.provided.set_session(prepare_uow.session)
            found_storage_raw = object_storage_repository.found_same_storage_raw(
                storage_raw
            )
        assert found_storage_raw is not None
        assert found_storage_raw.object_name == storage_raw.object_name