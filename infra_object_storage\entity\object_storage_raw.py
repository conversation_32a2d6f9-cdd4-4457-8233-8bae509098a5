"""
存储信息
"""

from infra_database.entity.basic_entity import BasicEntity
from sqlalchemy import Column, Index, Integer, String


class ObjectStorageRawEntity(BasicEntity):
    """
    存储信息
    """

    __tablename__ = "st_object_storage_raw"
    __table_args__ = {"comment": "对象存储物理文件"}
    object_storage_account_id = Column(String(40), comment="存储账号", nullable=False)
    bucket_name = Column(String(255), comment="篮子名称", nullable=False)
    object_name = Column(
        String(255), comment="对象名称", nullable=False, unique=True, index=True
    )
    checksum = Column(String(255), comment="文件bytes的MD5值", nullable=False)
    size = Column(Integer, comment="文件大小，单位字节", nullable=False)


Index(
    "idx_object_storage_raw_bucket_checksum_size",
    ObjectStorageRawEntity.bucket_name,
    ObjectStorageRawEntity.checksum,
    ObjectStorageRawEntity.size,
)
