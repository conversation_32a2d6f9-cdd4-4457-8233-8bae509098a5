"""
存储信息
"""

from infra_database.entity.basic_entity import BasicEntity
from sqlalchemy import BigInteger, Index, String
from sqlalchemy.orm import Mapped, mapped_column


class ObjectStorageRawEntity(BasicEntity):
    """
    存储信息
    """

    __tablename__ = "st_object_storage_raw"
    __table_args__ = {"comment": "对象存储物理文件"}

    account_id: Mapped[str] = mapped_column(
        String(255), comment="存储账号ID (来自配置文件)", nullable=False, index=True
    )
    bucket_name: Mapped[str] = mapped_column(
        String(255), comment="篮子名称", nullable=False
    )
    object_name: Mapped[str] = mapped_column(
        String(255), comment="对象名", nullable=False
    )
    checksum: Mapped[str] = mapped_column(
        String(255), comment="文件bytes的MD5值", nullable=False, index=True
    )
    size: Mapped[int] = mapped_column(
        BigInteger, comment="文件大小，单位字节", nullable=False
    )


Index(
    "idx_object_storage_raw_account_bucket_checksum_size",
    ObjectStorageRawEntity.account_id,
    ObjectStorageRawEntity.bucket_name,
    ObjectStorageRawEntity.checksum,
    ObjectStorageRawEntity.size,
)

# 添加checksum和size的联合索引
Index(
    "idx_object_storage_raw_checksum_size",
    ObjectStorageRawEntity.checksum,
    ObjectStorageRawEntity.size,
)
