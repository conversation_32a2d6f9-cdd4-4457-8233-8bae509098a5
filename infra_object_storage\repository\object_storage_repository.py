"""
对象存储repo
"""

from typing import List, Optional

from infra_basic.resource.resource_interface import Resource
from infra_database.model.transaction_model import SystemTransactionModel
from infra_database.repository.sync.sync_versioned_repository import (
    SyncVersionedRepository,
)

from infra_object_storage.entity.file_info import FileInfoEntity
from infra_object_storage.entity.file_relationship import FileRelationshipEntity
from infra_object_storage.entity.object_storage_raw import ObjectStorageRawEntity
from infra_object_storage.model.file_info_model import FileExtInfoModel, FileInfoModel
from infra_object_storage.model.file_relationship_model import FileRelationshipModel
from infra_object_storage.model.object_storage_raw_model import ObjectStorageRawModel


class ObjectStorageRepository(SyncVersionedRepository):
    """对象存储repo"""

    def found_same_storage_raw(
        self, check_storage_raw: ObjectStorageRawModel
    ) -> Optional[ObjectStorageRawModel]:
        """
        查找已存在的存储
        """
        found_sql = (
            "select * from st_object_storage_raw where "
            "account_id=:account_id "
            "and  bucket_name=:bucket_name "
            "and checksum=:checksum and size=:size"
        )
        return self._fetch_first_to_model(
            model_cls=ObjectStorageRawModel,
            sql=found_sql,
            params={
                "account_id": check_storage_raw.account_id,
                "bucket_name": check_storage_raw.bucket_name,
                "object_name": check_storage_raw.object_name,
                "checksum": check_storage_raw.checksum,
                "size": check_storage_raw.size,
            },
        )

    def get_file_info_by_id(self, file_info_id: str) -> Optional[FileInfoModel]:
        """
        根据文件ID获取文件信息
        """
        return self._fetch_first_to_model(
            model_cls=FileInfoModel,
            sql="SELECT * FROM st_file_info WHERE id=:file_info_id",
            params={"file_info_id": file_info_id},
        )

    def get_resource_related_file_list(
        self, resource: Resource, relationship: Optional[str] = None
    ) -> List[FileExtInfoModel]:
        """
        获取资源相关的文件信息
        """
        params = {
            "resource_category": resource.resource_info.category,
            "resource_id": resource.resource_info.id,
        }
        sql = (
            "select f.*,si.account_id,si.bucket_name,si.object_name,si.checksum,si.size from st_file_info f "
            "inner join st_object_storage_raw si on f.object_storage_raw_id=si.id "
            "inner join st_file_relationship fr "
            "on f.id=fr.file_info_id and fr.resource_category=:resource_category "
            "and fr.resource_id=:resource_id "
        )
        if relationship:
            sql = sql + " and relationship=:relationship"
            params["relationship"] = relationship
        return self._fetch_all_to_model(
            model_cls=FileExtInfoModel, sql=sql, params=params
        )

    def unlink_file_and_resource(
        self,
        file_info_id: str,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
    ):
        """断开文件和资源的关联"""
        params = {
            "file_info_id": file_info_id,
            "relationship": relationship,
            "resource_category": resource.resource_info.category,
            "resource_id": resource.resource_info.id,
        }
        list_sql = (
            "select id from st_file_relationship where file_info_id=:file_info_id "
            "and relationship=:relationship and resource_category=:resource_category "
            "and resource_id=:resource_id "
        )
        existed_id_list = self._execute_sql(sql=list_sql, params=params)
        for existed_id in existed_id_list:
            self._delete_versioned_entity_by_id(
                entity_cls=FileRelationshipEntity,
                entity_id=existed_id["id"],
                transaction=transaction,
            )

    def link_file_and_resource(
        self,
        file_info_id: str,
        resource: Resource,
        relationship: str,
        transaction: SystemTransactionModel,
    ) -> str:
        """为文件和资源进行关联"""
        params = {
            "file_info_id": file_info_id,
            "relationship": relationship,
            "resource_category": resource.resource_info.category,
            "resource_id": resource.resource_info.id,
        }
        check_sql = (
            "select id from st_file_relationship where file_info_id=:file_info_id "
            "and relationship=:relationship and resource_category=:resource_category "
            "and resource_id=:resource_id "
        )
        existed_file_relationship = self._fetch_first(sql=check_sql, params=params)
        if existed_file_relationship is not None:
            return str(existed_file_relationship["id"])
        relationship = str(params["relationship"])
        if not relationship:
            raise ValueError("relationship cannot be None")
        resource_id = str(params["resource_id"])
        if not resource_id:
            raise ValueError("resource_id cannot be None")
        resource_category = str(params["resource_category"])
        if not resource_category:
            raise ValueError("resource_category cannot be None")
        file_info_id = str(params["file_info_id"])
        if not file_info_id:
            raise ValueError("file_info_id cannot be None")
        return self._insert_versioned_entity_by_model(
            entity_cls=FileRelationshipEntity,
            model_data=FileRelationshipModel(
                file_info_id=file_info_id,
                resource_category=resource_category,
                resource_id=resource_id,
                relationship=relationship,
            ),
            transaction=transaction,
        )

    def get_storage_by_file_info_id(
        self, file_info_id: str
    ) -> Optional[ObjectStorageRawModel]:
        """
        根据文件id获取存储信息
        """
        sql = (
            "select s.* from st_file_info f inner join st_object_storage_raw s "
            "on f.object_storage_raw_id=s.id and f.id=:file_info_id"
        )
        return self._fetch_first_to_model(
            model_cls=ObjectStorageRawModel,
            sql=sql,
            params={"file_info_id": file_info_id},
        )

    def insert_object_storage_raw(self, storage_info: ObjectStorageRawModel) -> str:
        """插入存储信息"""
        return self._insert_basic_entity_by_model(
            entity_cls=ObjectStorageRawEntity, model_data=storage_info
        )

    def get_file_related_relationship_list(
        self, file_info_id: str
    ) -> List[FileRelationshipModel]:
        """根据文件获得对应的资源"""
        sql = (
            "select id,resource_category,resource_id,file_info_id,relationship,summary, "
            "version,handler_category, handler_id,handled_on,remark "
            " from st_file_relationship where file_info_id=:file_info_id"
        )
        return self._fetch_all_to_model(
            model_cls=FileRelationshipModel,
            sql=sql,
            params={"file_info_id": file_info_id},
        )

    def delete_file(self, file_info_id: str, transaction: SystemTransactionModel) -> None:
        """删除文件"""
        # 断开文件与资源的联系
        file_relationship_list = self.get_file_related_relationship_list(
            file_info_id=file_info_id
        )
        for file_relationship in file_relationship_list:
            if file_relationship.id:
                self._delete_versioned_entity_by_id(
                    entity_cls=FileRelationshipEntity,
                    entity_id=file_relationship.id,
                    transaction=transaction,
                )
        # 删除文件
        self._delete_versioned_entity_by_id(
            entity_cls=FileInfoEntity, entity_id=file_info_id, transaction=transaction
        )

    def insert_file_info(
        self,
        object_storage_raw_id: str,
        file_name: str,
        transaction: SystemTransactionModel,
        summary: Optional[str] = None,
    ) -> str:
        """插入文件信息"""
        file_info = FileInfoModel(
            object_storage_raw_id=object_storage_raw_id,
            original_name=file_name,
            summary=summary,
        )
        return self._insert_versioned_entity_by_model(
            entity_cls=FileInfoEntity,
            model_data=file_info,
            transaction=transaction,
        )
