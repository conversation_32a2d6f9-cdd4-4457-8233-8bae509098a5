import os

from dependency_injector import containers, providers
from infra_basic.resource.basic_resource import BasicResource
from infra_basic.settings.settings_manager import SettingsManager
from infra_database.database import SyncDatabase
from infra_database.settings import DatabaseSettings
from infra_database.uow.session_proxy import current_session_proxy
from infra_database.uow.sync_sql_alchemy_unit_of_work import SyncSqlAlchemyUnitOfWork
from pydantic import BaseModel
from pytest import fixture

from infra_object_storage.entity.file_info import FileInfoEntity
from infra_object_storage.entity.file_relationship import FileRelationshipEntity
from infra_object_storage.entity.history.file_info_history import FileInfoHistoryEntity
from infra_object_storage.entity.history.file_relationship_history import (
    FileRelationshipHistoryEntity,
)
from infra_object_storage.entity.object_storage_raw import ObjectStorageRawEntity
from infra_object_storage.helper.cos_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from infra_object_storage.helper.minio_helper import <PERSON>o<PERSON><PERSON><PERSON>
from infra_object_storage.helper.object_storage_client import Object<PERSON><PERSON>age<PERSON>lient
from infra_object_storage.object_storage_container import ObjectStorageContainer
from infra_object_storage.settings import StorageSettings


class MockAppSettings(BaseModel):
    database: DatabaseSettings
    storage: StorageSettings


def get_mock_app_settings() -> MockAppSettings:
    test_toml_path = os.path.join(os.path.dirname(__file__), "test_app.toml")
    manager = SettingsManager(MockAppSettings)
    return manager.load_settings(file_path=test_toml_path)


@fixture
def prepare_sync_database() -> SyncDatabase:
    """创建并返回同步数据库实例"""
    settings = get_mock_app_settings()
    database = SyncDatabase(setting=settings.database)
    return database


@fixture()
def create_tables(prepare_sync_database: SyncDatabase):
    """创建所有必要的数据库表"""
    # 确保所有实体类都被加载，以便它们注册到元数据中
    _ = (
        ObjectStorageRawEntity,
        FileInfoEntity,
        FileRelationshipEntity,
        FileInfoHistoryEntity,
        FileRelationshipHistoryEntity,
    )

    session_factory = prepare_sync_database.get_session_factory()
    session = session_factory()
    try:
        engine = session.get_bind()
        # 假设所有实体共享同一个元数据对象，我们可以从任何一个实体中获取它
        metadata = ObjectStorageRawEntity.metadata
        # 在测试前先删除所有表，确保环境干净
        metadata.drop_all(bind=engine)
        # 创建所有已注册的表
        metadata.create_all(bind=engine)
    finally:
        session.close()

    return prepare_sync_database


@fixture()
def prepare_handler() -> BasicResource:
    return BasicResource(
        category="test_object_storage_helper", id="test_object_storage_helper_ext"
    )


@fixture
def mock_app_settings() -> MockAppSettings:
    return get_mock_app_settings()


@fixture
def cos_account(mock_app_settings):
    """
    This fixture extracts the COS account from the accounts list in the main conftest.py
    """
    accounts_list = mock_app_settings.storage.accounts
    cos_account = next((acc for acc in accounts_list if acc.id == "cos_test"), None)
    return cos_account


@fixture
def cos_helper(cos_account):
    """
    This fixture creates a CosHelper instance using the COS account from the accounts list
    """
    return CosHelper(cos_account)


@fixture
def minio_account(mock_app_settings):
    """
    This fixture extracts the Minio account from the accounts list in the main conftest.py
    """
    accounts_list = mock_app_settings.storage.accounts
    minio_account = next((acc for acc in accounts_list if acc.id == "minio_test"), None)
    return minio_account


@fixture
def minio_helper(minio_account):
    """
    This fixture creates a MinioHelper instance using the Minio account from the accounts list
    """
    return MinioHelper(minio_account)


@fixture
def s3_account(mock_app_settings):
    """
    This fixture extracts the S3 account from the accounts list in the main conftest.py
    """
    accounts_list = mock_app_settings.storage.accounts
    s3_account = next((acc for acc in accounts_list if acc.id == "s3_test"), None)
    return s3_account


@fixture
def s3_helper(s3_account):
    """
    This fixture creates a S3Helper instance using the S3 account from the accounts list
    """
    from infra_object_storage.helper.s3_helper import S3Helper

    return S3Helper(s3_account)


@fixture
def oss_account(mock_app_settings):
    """
    This fixture extracts the OSS account from the accounts list in the main conftest.py
    """
    accounts_list = mock_app_settings.storage.accounts
    oss_account = next((acc for acc in accounts_list if acc.id == "oss_test"), None)
    return oss_account


@fixture
def oss_helper(oss_account):
    """
    This fixture creates a OssHelper instance using the OSS account from the accounts list
    """
    from infra_object_storage.helper.oss_helper import OssHelper

    return OssHelper(oss_account)


@fixture
def object_storage_client_from_file(mock_app_settings) -> ObjectStorageClient:
    """
    This fixture creates an ObjectStorageClient instance configured from
    the mock_app_settings fixture.
    """
    accounts_list = mock_app_settings.storage.accounts
    default_account_id = mock_app_settings.storage.default_account_id
    return ObjectStorageClient(accounts=accounts_list, default_account_id=default_account_id)


class TestStubContainer(containers.DeclarativeContainer):
    wiring_config = containers.WiringConfiguration(
        packages=["infra_object_storage", "tests"])

    settings: providers.Provider[MockAppSettings] = (
        providers.Factory(lambda: get_mock_app_settings()))

    database = providers.Singleton(
        SyncDatabase,
        setting=settings.provided.database)  # type: ignore

    uow = providers.Factory(
        SyncSqlAlchemyUnitOfWork,
        session_factory=database.provided.get_session_factory.call(),
    )
    session_proxy = providers.Object(current_session_proxy)

    object_storage_container: ObjectStorageContainer = providers.Container(
        ObjectStorageContainer,
        session_proxy=session_proxy,
        storage_settings=settings.provided.storage
    )  # type: ignore

@fixture
def test_container():
    """创建并返回测试容器实例"""
    container = TestStubContainer()
    container.init_resources()
    return container