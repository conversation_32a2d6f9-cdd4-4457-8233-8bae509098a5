import os
import tomllib

from infra_basic.resource.basic_resource import BasicResource
from infra_database.database import SyncDatabase
from infra_database.model.transaction_model import SystemTransactionModel
from infra_database.settings import DatabaseSettings
from infra_database.uow.sync_sql_alchemy_unit_of_work import SyncSqlAlchemyUnitOfWork
from pytest import fixture

import infra_object_storage
from infra_object_storage.helper.object_storage_client import ObjectStorageClient
from infra_object_storage.object_storage_container import ObjectStorageContainer
from infra_object_storage.service.object_storage_service import ObjectStorageService
from infra_object_storage.settings import (
    CosSetting,
    MinioSetting,
    OssSetting,
    S3Setting,
    StorageClientSetting,
)


@fixture()
def prepare_sync_database(mock_app_settings) -> SyncDatabase:
    return SyncDatabase(mock_app_settings.database)


@fixture()
def prepare_uow(prepare_sync_database) -> SyncSqlAlchemyUnitOfWork:
    return SyncSqlAlchemyUnitOfWork(engine=prepare_sync_database.get_engine())

@fixture
def object_storage_client() -> ObjectStorageClient:
    toml_path = os.path.join(os.path.dirname(infra_object_storage.__file__), "..", "app.toml")
    with open(toml_path, "rb") as f:
        config = tomllib.load(f)

    storage_client_setting = StorageClientSetting(**config.get("storage_client", {}))
    s3_setting = S3Setting(**config.get("s3", {}))
    minio_setting = MinioSetting(**config.get("minio", {}))
    cos_setting = CosSetting(**config.get("cos", {}))
    oss_setting = OssSetting(**config.get("oss", {}))

    return ObjectStorageClient(
        settings=storage_client_setting,
        s3_settings=s3_setting,
        minio_settings=minio_setting,
        cos_settings=cos_setting,
        oss_settings=oss_setting,
    )


@fixture()
def prepare_storage_container(prepare_database) -> ObjectStorageContainer:
    object_storage_container = ObjectStorageContainer()
    object_storage_container.wire(
        modules=[
            "infra_object_storage.service.object_storage_service",
        ]
    )
    object_storage_container.session_proxy.override(prepare_database.get_session())
    return object_storage_container


@fixture()
def object_storage_service(prepare_storage_container) -> ObjectStorageService:
    return prepare_storage_container.object_storage_service()


@fixture()
def prepare_handler() -> BasicResource:
    return BasicResource(
        category="test_object_storage_helper", id="test_object_storage_helper_ext"
    )


@fixture()
def prepare_transaction(
    prepare_storage_container, prepare_handler
) -> SystemTransactionModel:
    uow: SyncSqlAlchemyUnitOfWork = prepare_storage_container.uow()
    return uow.log_transaction(
        handler=prepare_handler, action="test_object_storage"
    )


def test_init_database_table(prepare_database):
    prepare_database.create_tables(scan_module=infra_object_storage)
