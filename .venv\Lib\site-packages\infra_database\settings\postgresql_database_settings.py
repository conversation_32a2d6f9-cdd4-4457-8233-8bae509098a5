"""
PostgreSQL 数据库设置
"""

from typing import Any, Dict, Optional

from pydantic import Field

from infra_database.settings import DatabaseSettings


class PostgreSQLDatabaseSettings(DatabaseSettings):
    """
    PostgreSQL 特定的数据库设置
    """

    # 确保默认驱动是 PostgreSQL
    driver: str = Field(default="postgresql+psycopg2")

    # PostgreSQL 特有的 JIT 配置
    enable_jit: bool = Field(default=False, description="是否启用 PostgreSQL JIT 编译")
    jit_above_cost: float = Field(default=100000, description="查询成本高于此值时启用 JIT")
    jit_inline_above_cost: float = Field(default=500000, description="函数内联成本阈值")
    jit_optimize_above_cost: float = Field(default=500000, description="优化成本阈值")

    # PostgreSQL 特有的其他配置
    application_name: Optional[str] = Field(default=None, description="应用名称，用于在 pg_stat_activity 中标识连接")
    client_encoding: Optional[str] = Field(default=None, description="客户端编码")

    def get_engine_args(self) -> Dict[str, Any]:
        """
        获取包含 PostgreSQL 特定参数的 SQLAlchemy 引擎参数

        Returns:
            Dict[str, Any]: SQLAlchemy 引擎参数字典
        """
        # 获取基类的引擎参数
        engine_args = super().get_engine_args()

        # 添加 PostgreSQL 特有的连接参数
        connect_args = {}

        # 设置应用名称
        if self.application_name:
            connect_args["application_name"] = self.application_name

        # 设置客户端编码
        if self.client_encoding:
            connect_args["client_encoding"] = self.client_encoding

        # 如果启用 JIT，添加 JIT 相关参数
        if self.enable_jit:
            options = (
                f"-c jit=on "
                f"-c jit_above_cost={self.jit_above_cost} "
                f"-c jit_inline_above_cost={self.jit_inline_above_cost} "
                f"-c jit_optimize_above_cost={self.jit_optimize_above_cost}"
            )

            if "options" in connect_args:
                connect_args["options"] += " " + options
            else:
                connect_args["options"] = options

        # 如果有连接参数，添加到引擎参数中
        if connect_args:
            engine_args["connect_args"] = connect_args

        return engine_args
