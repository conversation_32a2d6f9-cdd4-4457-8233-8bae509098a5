"""
同步数据库类模块

本模块提供了PostgreSQL数据库的同步连接和操作功能，包括：
1. 数据库连接管理
2. 连接池配置
3. 连接测试
4. 表创建
5. 上下文管理支持

这些功能通过SQLAlchemy的同步引擎实现，提供了线程安全的数据库操作。
"""

from contextlib import contextmanager
from typing import Any, Dict, Optional

from loguru import logger
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, sessionmaker

from infra_database.database.base_database import Database
from infra_database.error import EntityError
from infra_database.settings import DatabaseSettings, PostgreSQLDatabaseSettings


class SyncDatabase(Database):
    """
    同步数据库类

    提供PostgreSQL数据库的同步连接和操作功能，包括：
    1. 数据库连接管理
    2. 连接池配置和验证
    3. 连接测试和状态检查
    4. 表创建和管理
    5. 上下文管理支持

    属性:
        _engine: SQLAlchemy同步引擎对象，用于管理数据库连接
    """

    def __init__(self, setting: DatabaseSettings):
        """
        初始化同步数据库对象

        参数:
            setting: 数据库配置对象，包含连接信息和连接池设置
        """
        super().__init__(setting)
        self._engine: Optional[Engine] = None
        self._register_extensions()

    def _get_engine_args(self) -> Dict[str, Any]:
        """
        获取引擎参数

        根据配置生成SQLAlchemy引擎的参数字典，包括：
        1. 日志输出设置
        2. 连接池配置
        3. 连接健康检查设置

        返回:
            Dict[str, Any]: 引擎参数字典
        """
        return {
            "echo": self.setting.echo,
            "pool_size": self.setting.pool_size,
            "max_overflow": self.setting.max_overflow,
            "pool_timeout": self.setting.pool_timeout,
            "pool_recycle": self.setting.pool_recycle,
            "pool_pre_ping": self.setting.pool_pre_ping,
        }

    def get_engine(self) -> Engine:
        """
        获取同步数据库引擎

        如果引擎不存在，则创建新的引擎。引擎创建时会：
        1. 构建连接字符串
        2. 应用引擎参数
        3. 注册数据库扩展

        返回:
            Engine: SQLAlchemy同步引擎对象
        """
        if self._engine is None:
            # 构建连接字符串
            connection_string = self._build_connection_string()
            # 创建同步引擎
            self._engine = create_engine(connection_string, **self._get_engine_args())

        return self._engine

    def _register_extensions(self) -> None:
        """
        注册数据库扩展

        根据数据库类型和配置的扩展列表注册相应的扩展。
        目前支持PostgreSQL数据库的扩展注册。
        """
        if isinstance(self.setting, PostgreSQLDatabaseSettings):
            self._register_postgres_extensions()

    def _register_postgres_extensions(self) -> None:
        """
        注册PostgreSQL扩展

        注册PostgreSQL特定的扩展，如datetime适配器。
        此方法会检查psycopg2是否可用，如果可用则注册相应的适配器。

        异常:
            ImportError: 如果psycopg2未安装，会静默跳过扩展注册
        """
        try:
            # 使用importlib.util.find_spec检测psycopg2是否可用
            import importlib.util

            if importlib.util.find_spec("psycopg2") is not None:
                from datetime import datetime

                from psycopg2 import extensions

                from infra_database.adapter.postgresql.infinity_datetime_adapter import (
                    InfinityDateTimeAdapter,
                )

                extensions.register_adapter(datetime, InfinityDateTimeAdapter)

        except ImportError:
            # 如果没有安装psycopg2，则跳过扩展注册
            pass

    def get_session_factory(self) -> sessionmaker[Session]:
        """
        获取同步数据库会话工厂

        返回：
            sessionmaker[Session]: SQLAlchemy同步会话工厂对象
        """
        return sessionmaker(self.get_engine(), expire_on_commit=False)

    def dispose(self) -> None:
        """
        释放同步数据库连接资源

        释放所有数据库连接资源，包括：
        1. 关闭所有连接
        2. 清理连接池
        3. 重置引擎对象
        """
        if self._engine is not None:
            self._engine.dispose()
            self._engine = None

    def test_connection(self) -> bool:
        """
        测试数据库连接是否正常

        通过执行简单的SQL查询来测试数据库连接是否正常。

        返回:
            bool: 连接是否正常，True表示正常，False表示异常
        """
        try:
            with self.get_engine().connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except SQLAlchemyError:
            return False

    @property
    def is_connected(self) -> bool:
        """
        检查数据库是否已连接

        通过检查引擎对象是否存在来判断数据库是否已连接。

        返回:
            bool: 是否已连接，True表示已连接，False表示未连接
        """
        return self._engine is not None

    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器

        提供自动管理数据库连接生命周期的上下文管理器，确保连接在使用后被正确关闭。

        使用示例:
            with db.get_connection() as conn:
                result = conn.execute(text("SELECT * FROM table"))

        返回:
            Connection: 数据库连接对象

        异常:
            SQLAlchemyError: 当连接操作失败时抛出
        """
        engine = self.get_engine()
        connection = engine.connect()
        try:
            yield connection
        finally:
            connection.close()

    def __enter__(self):
        """
        上下文管理器入口

        支持使用with语句管理数据库对象的生命周期。

        返回:
            SyncDatabase: 数据库对象实例
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        上下文管理器出口

        在退出with语句块时自动释放数据库资源。
        """
        self.dispose()

    def create_tables(self, entity_class_list: list[type]) -> None:
        """
        根据传入的实体类列表，在数据库中创建对应的表

        遍历实体类列表，为每个有效的SQLAlchemy实体类创建对应的数据库表。
        如果表已存在，则不会进行任何操作。

        参数:
            entity_class_list: 需要建表的SQLAlchemy实体类列表

        异常:
            当创建表失败时，会打印错误信息但不会中断执行
        """
        engine = self.get_engine()
        for entity_cls in entity_class_list:
            # 检查是否有__table__属性
            if not hasattr(entity_cls, "__table__"):
                raise EntityError(f"{entity_cls} 不是有效的SQLAlchemy实体类")
            try:
                entity_cls.__table__.create(engine, checkfirst=True)
            except Exception as e:
                # 捕获异常并打印错误信息
                logger.error(f"创建表失败: {entity_cls}，错误: {e}")
