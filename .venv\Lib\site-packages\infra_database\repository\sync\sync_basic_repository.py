from typing import Any, Dict, List, Optional, Type, cast

from infra_basic.model.basic_model import BasicModel
from infra_utility.string_helper import is_blank

from infra_database.converter.convert_helper import model_to_flat_dict
from infra_database.entity.basic_entity import BasicEntity, GenericBasicEntity
from infra_database.error import (
    EntityLackIdError,
    EntityNotFoundError,
    InvalidEntityClassWithIdError,
)
from infra_database.model.database_query_model import FilterCondition
from infra_database.repository.orm_helper import is_entity_type_with_id_field
from infra_database.repository.sync.sync_base_repository import SyncBaseRepository


class SyncBasicBaseRepository(SyncBaseRepository):
    def _insert_basic_entity_by_dict(self, entity_cls: Type[BasicEntity], dict_data: Dict[str, Any]) -> str:
        """
        根据字典信息来插入数据库数据
        :param entity_cls: 实体类
        :param dict_data: 字段数据字典
        :return: 新插入实体的主键值（字符串类型）
        :raises EntityError: 插入过程中发生异常
        """
        result_entity = self._insert_entity_by_dict_get_entity(entity_cls=entity_cls, dict_data=dict_data)
        self.get_session().flush()
        return cast(str, result_entity.id)

    def _insert_basic_entity_by_model(self, entity_cls: Type[GenericBasicEntity], model_data: BasicModel) -> str:
        """
        根据Pydantic模型信息来插入数据库数据
        :param entity_cls: 实体类
        :param model_data: Pydantic模型数据
        :return: 新插入实体的主键值（字符串类型）
        :raises EntityError: 插入过程中发生异常
        """
        # 直接将model_data转为字典，调用_insert_entity_by_dict实现插入
        dict_data = model_to_flat_dict(model_data)
        return self._insert_basic_entity_by_dict(entity_cls=entity_cls, dict_data=dict_data)

    def _update_basic_entity_by_dict(
        self,
        entity_cls: Type[GenericBasicEntity],
        entity_id: str,
        update_data: Dict[str, Any],
        limited_col_list: Optional[List[str]] = None,
    ) -> int:
        """
        根据id来更新数据库数据
        :param entity_cls: 实体类
        :param entity_id: 实体ID
        :param update_data: 更新数据字典
        :param limited_col_list: 需要更新的列，如果传入，则只更新传入的字段
        :return: 更新的行数
        :raises EntityNotFoundError: 当实体不存在时抛出
        """
        found_row = self.get_session().query(entity_cls).filter(entity_cls.id == entity_id).first()
        if found_row:
            return self._update_entity_by_dict(
                entity_cls=entity_cls,
                update_data=update_data,
                filter_params=[FilterCondition(column_name="id", operator="=", value=entity_id)],
                limited_col_list=limited_col_list,
            )
        else:
            raise EntityNotFoundError(cls=entity_cls, params={"id": entity_id})

    def _update_basic_entity_by_model(
        self,
        entity_cls: Type[GenericBasicEntity],
        update_model: BasicModel,
        limited_col_list: Optional[List[str]] = None,
    ) -> int:
        """
        根据模型来更新数据库数据
        :param entity_cls: 实体类
        :param update_model: 更新数据模型
        :param limited_col_list: 需要更新的列，如果传入，则只更新传入的字段
        :return: 更新的行数
        :raises EntityLackIdError: 当模型缺少ID时抛出
        :raises EntityNotFoundError: 当实体不存在时抛出
        """
        if is_blank(update_model.id):
            raise EntityLackIdError()
        assert update_model.id is not None  # 类型断言，保证后续类型安全
        update_dict = model_to_flat_dict(update_model)
        # 确保在删除id之前先检查id是否存在
        if "id" in update_dict:
            del update_dict["id"]
        return self._update_basic_entity_by_dict(
            entity_cls=entity_cls,
            entity_id=update_model.id,
            update_data=update_dict,
            limited_col_list=limited_col_list,
        )

    def _delete_entity_by_id(self, entity_cls: Type[BasicEntity], entity_id: str) -> int:
        """
        根据id删除数据
        :param entity_cls: 实体类
        :param entity_id: 实体ID
        :return: 删除的行数
        :raises InvalidEntityClassWithIdError: 当实体类没有id字段时抛出
        :raises EntityNotFoundError: 当实体不存在时抛出
        """
        if not is_entity_type_with_id_field(input_cls=entity_cls):
            raise InvalidEntityClassWithIdError(cls=entity_cls)

        # 首先检查实体是否存在
        entity_to_delete = self.get_session().query(entity_cls).filter(entity_cls.id == entity_id).first()
        if not entity_to_delete:
            raise EntityNotFoundError(cls=entity_cls, params={"id": entity_id})

        # 执行删除操作
        deleted_count = self.get_session().query(entity_cls).filter(entity_cls.id == entity_id).delete()
        # self.get_session().flush() # This is already handled in _delete_entity_by_params in the base class
        return deleted_count
