"""
异步事务仓库

"""

from typing import List

from infra_database.entity.transaction_entity import SystemTransactionEntity
from infra_database.model.transaction_model import SystemTransactionModel

from .async_base_repository import AsyncBaseRepository


class AsyncTransactionRepository(AsyncBaseRepository):
    """
    异步事务仓库，用于持久化事务日志。
    """

    async def save_transaction_logs(self, logs: List[SystemTransactionModel]) -> None:
        """
        批量保存事务日志。

        这个方法会将提供的所有日志模型转换为实体，并添加到当前 Session 中，
        以便在父级工作单元（UoW）提交时一并持久化。

        参数:
            logs: 一个包含 SystemTransactionModel 实例的列表。
        """
        entities = [SystemTransactionEntity.from_model(log) for log in logs]
        self.session.add_all(entities)
        # 在异步UoW中，flush通常由commit触发，这里可以不手动flush
        # await self.session.flush()
