"""
资源与文件之间的关系历史实体类
"""

from typing import Optional

from infra_database.entity.versioned.history_entity import HistoryEntity
from sqlalchemy import Index, String, Text
from sqlalchemy.orm import Mapped, mapped_column


class FileRelationshipHistoryEntity(HistoryEntity):
    """
    资源与文件之间的关系（历史实体类）
    """

    __tablename__ = "st_file_relationship_history"
    __table_args__ = {"comment": "资源与文件之间的关系（历史）"}

    file_info_id: Mapped[str] = mapped_column(
        String(40), nullable=False, comment="文件id", index=True
    )
    resource_category: Mapped[str] = mapped_column(
        String(255), comment="资源类型", nullable=False
    )
    resource_id: Mapped[str] = mapped_column(
        String(40), comment="资源id", nullable=False
    )
    relationship: Mapped[str] = mapped_column(
        String(255), comment="资源与文件之间的关系说明", nullable=False
    )
    summary: Mapped[Optional[str]] = mapped_column(Text, comment="摘要")


Index(
    "idx_st_file_relationship_history_resource_info",
    FileRelationshipHistoryEntity.resource_id,
    FileRelationshipHistoryEntity.resource_category,
)

# 时间检索索引
Index(
    "idx_file_relationship_history_time_range",
    FileRelationshipHistoryEntity.id,
    FileRelationshipHistoryEntity.commenced_on,
    FileRelationshipHistoryEntity.ceased_on.desc(),
    unique=True,
)
