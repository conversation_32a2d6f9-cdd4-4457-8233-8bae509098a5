"""
资源与文件之间的关系历史实体类
"""

from infra_database.entity.versioned.history_entity import HistoryEntity
from sqlalchemy import Column, Index, String, Text


class FileRelationshipHistoryEntity(HistoryEntity):
    """
    资源与文件之间的关系（历史实体类）
    """

    __tablename__ = "st_file_relationship_history"
    __table_args__ = {"comment": "资源与文件之间的关系（历史）"}
    file_id = Column(String(40), nullable=False, comment="文件id", index=True)
    res_category = Column(String(255), comment="资源类型", nullable=False)
    res_id = Column(String(40), comment="资源id", nullable=False)
    relationship = Column(
        String(255), comment="资源与文件之间的关系说明", nullable=False
    )
    summary = Column(Text, comment="摘要")


Index(
    "idx_st_file_relationship_history_resource_info",
    FileRelationshipHistoryEntity.res_id,
    FileRelationshipHistoryEntity.res_category,
)

# 时间检索索引
Index(
    "idx_file_relationship_history_time_range",
    FileRelationshipHistoryEntity.id,
    FileRelationshipHistoryEntity.commenced_on,
    FileRelationshipHistoryEntity.ceased_on.desc(),
    unique=True,
)
