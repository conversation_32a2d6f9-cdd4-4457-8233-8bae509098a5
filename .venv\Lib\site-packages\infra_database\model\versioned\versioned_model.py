"""
版本化Model
"""

from datetime import datetime
from typing import Optional

from infra_basic.model.basic_model import BasicModel
from infra_utility.datetime_helper import local_now
from pydantic import Field


class SimpleVersionedModel(BasicModel):
    """
    简化后的版本Model，用于满足不关注handler信息的场景
    """

    version: int = Field(default=1, title="version")


class VersionedModel(SimpleVersionedModel):
    """
    版本化Model
    """

    handler_category: Optional[str] = None
    handler_id: Optional[str] = None
    handled_on: datetime = Field(default_factory=local_now)
    remark: Optional[str] = None
