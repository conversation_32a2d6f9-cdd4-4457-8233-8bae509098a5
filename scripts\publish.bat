@ECHO ON
REM "publish config"

@ECHO OFF
REM "clean dist folder"
DEL dist\* /F /Q

@ECHO OFF
REM "build dist"
call uv build

@ECHO OFF
REM "检查环境变量"
IF "%TWINE_USERNAME%"=="" (
    ECHO 错误: 请设置 TWINE_USERNAME 环境变量
    EXIT /B 1
)
IF "%TWINE_PASSWORD%"=="" (
    ECHO 错误: 请设置 TWINE_PASSWORD 环境变量
    EXIT /B 1
)

SET TWINE_REPOSITORY_URL=https://repository.qindingtech.com/repository/pypi-releases/

@ECHO OFF
REM "start publishing"
call twine upload ./dist/*
REM "finish publishing"

