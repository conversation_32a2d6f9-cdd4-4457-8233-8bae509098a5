"""
腾讯云帮助类
"""

from io import Bytes<PERSON>
from typing import Optional

from qcloud_cos import CosConfig, CosS3Client

from infra_object_storage.error import ObjectNotPublicReadError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
    ObjectStorageTypeEnum,
)
from infra_object_storage.helper.object_storage_interface import (
    AccountInfo,
    ObjectStorageInterface,
)
from infra_object_storage.settings import CosSetting


class CosHelper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """
    Cos帮助类
    """

    def get_account_info(self) -> AccountInfo:
        """返回账号信息"""
        return AccountInfo(
            type=ObjectStorageTypeEnum.COS,
            site=self.__settings.region,
            unified_id=self.__settings.secret_id,
        )

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获取目标篮子"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if "-" not in target_bucket_name:
            try:
                appid = self.__settings.default_bucket.split("-")[-1]
                target_bucket_name = f"{target_bucket_name}-{appid}"
            except IndexError:
                pass  # or raise an error if appid is mandatory
        return target_bucket_name

    def __init__(self, cos_settings: Optional[CosSetting] = None):
        """初始化"""
        if cos_settings is None:
            cos_settings = CosSetting()
        self.__settings = cos_settings
        super().__init__(default_bucket=self.__settings.default_bucket)
        config = CosConfig(
            Region=self.__settings.region,
            SecretId=self.__settings.secret_id,
            SecretKey=self.__settings.secret_key,
            Token=self.__settings.token,
        )  # 获取配置对象
        self.__cos_client = CosS3Client(config)

    def make_bucket(self, bucket_name: Optional[str]):
        """构建篮子"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        self.__cos_client.create_bucket(Bucket=target_bucket_name)
        self.__cos_client.put_bucket_acl(Bucket=target_bucket_name, ACL="private")

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """判断篮子是否存在"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        response = self.__cos_client.bucket_exists(Bucket=target_bucket_name)
        return response

    def remove_bucket(self, bucket_name: Optional[str]):
        """移除篮子"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        self.__cos_client.delete_bucket(Bucket=target_bucket_name)

    def download_file(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """下载文件"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        response = self.__cos_client.get_object(
            Bucket=target_bucket_name, Key=object_name
        )
        bytes_data = BytesIO()
        body = response.get("Body")
        if body:
            for chunk in body.get_stream():
                bytes_data.write(chunk)
        return bytes_data.getvalue()

    def upload_file(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """上传文件"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__cos_client.put_object(
            Bucket=target_bucket_name, Key=object_name, Body=BytesIO(object_data)
        )
        if is_public:
            self.__cos_client.put_object_acl(
                Bucket=target_bucket_name,
                Key=object_name,
                ACL="public-read",
            )

    def get_file_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """获取文件链接"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__cos_client.get_presigned_url(
            Method="GET",
            Bucket=target_bucket_name,
            Key=object_name,
            Expired=expires_seconds,
        )


    def __is_object_public_read(self, object_name: str, bucket_name) -> bool:
        """
        判断对象是否公开读取
        :param object_name:
        :param bucket_name:
        :return:
        """
        acl = self.__cos_client.get_object_acl(Bucket=bucket_name, Key=object_name)
        for grantee in acl["AccessControlList"]["Grant"]:
            if (
                grantee["Grantee"]["Type"] == "Group"
                and grantee["Grantee"].get("URI", "")
                == "http://cam.qcloud.com/groups/global/AllUsers"
                and grantee["Permission"] in ["READ", "FULL_CONTROL"]
            ):
                return True
        return False


    def get_object_policy(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        return self.get_object_acl(object_name, bucket_name)

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        raise NotImplementedError

    def set_object_public_read(self, object_name: str, bucket_name: Optional[str] = None):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__cos_client.put_object_acl(
            Bucket=target_bucket_name,
            Key=object_name,
            ACL="public-read",
        )

    def get_object_acl(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__cos_client.get_object_acl(
            Bucket=target_bucket_name, Key=object_name
        )
