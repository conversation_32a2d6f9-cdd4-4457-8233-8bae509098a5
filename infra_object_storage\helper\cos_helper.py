"""
腾讯云帮助类
"""

import json
from typing import Dict, Any, Optional, List, Generator
from io import BytesIO

from qcloud_cos import CosConfig, CosS3Client, CosServiceError

from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
)
from infra_object_storage.helper.object_storage_interface import ObjectStorageInterface
from infra_object_storage.settings import CosAccount


class CosHelper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """
    Cos帮助类
    """

    def get_account_info(self) -> CosAccount:
        """返回账号信息"""
        return self.__account

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获取目标存储桶"""
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        # 检查存储桶名称是否已包含 APPID。
        # 腾讯云 COS 的存储桶名称格式为 <BucketName-APPID>，其中 APPID 是一串数字。
        # 我们通过检查名称的最后一部分是否为纯数字来判断是否已包含 APPID。
        parts = target_bucket_name.split("-")
        if not (len(parts) > 1 and parts[-1].isdigit()):
            try:
                # 从默认存储桶名称中提取 APPID。
                # 同样，我们假设 APPID 是默认存储桶名称中以连字符分隔的最后一部分。
                appid = self.__account.default_bucket.split("-")[-1]
                if appid.isdigit():
                    target_bucket_name = f"{target_bucket_name}-{appid}"
            except IndexError:
                # 如果无法从默认存储桶名称中提取 APPID，则不执行任何操作。
                # 或者，可以根据需要在此处引发错误。
                pass
        return target_bucket_name

    def __init__(self, cos_account: CosAccount):
        """初始化"""
        self.__account = cos_account
        super().__init__(default_bucket=self.__account.default_bucket)
        config = CosConfig(
            Region=self.__account.region,
            SecretId=self.__account.secret_id,
            SecretKey=self.__account.secret_key,
            Token=self.__account.token,
        )  # 获取配置对象
        self.__cos_client = CosS3Client(config)

    def make_bucket(self, bucket_name: Optional[str]):
        """创建存储桶"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        self.__cos_client.create_bucket(Bucket=target_bucket_name)
        self.__cos_client.put_bucket_acl(Bucket=target_bucket_name, ACL="private")

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """判断存储桶是否存在"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        try:
            self.__cos_client.bucket_exists(Bucket=target_bucket_name)
            return True
        except CosServiceError:
            return False

    def remove_bucket(self, bucket_name: Optional[str]):
        """移除存储桶"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        self.__cos_client.delete_bucket(Bucket=target_bucket_name)

    def download_object(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """下载文件"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        response = self.__cos_client.get_object(
            Bucket=target_bucket_name, Key=object_name
        )
        bytes_data = BytesIO()
        body = response.get("Body")
        if body:
            for chunk in body.get_stream():
                bytes_data.write(chunk)
        return bytes_data.getvalue()

    def upload_object(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """上传文件"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        self.__cos_client.put_object(
            Bucket=target_bucket_name, Key=object_name, Body=BytesIO(object_data)
        )
        if is_public:
            self.__cos_client.put_object_acl(
                Bucket=target_bucket_name,
                Key=object_name,
                ACL="public-read",
            )

    def get_object_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """获取文件链接"""
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        url: str = self.__cos_client.get_presigned_url(
            Method="GET",
            Bucket=target_bucket_name,
            Key=object_name,
            Expired=expires_seconds,
        )
        return url

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict[str, Any]:
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        policy_info = self.__cos_client.get_bucket_policy(Bucket=target_bucket_name)
        policy: dict[str, Any] = json.loads(policy_info["Policy"])
        return policy

    def set_bucket_policy(self, policy: dict[str, Any], bucket_name: Optional[str] = None):
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        self.__cos_client.put_bucket_policy(
            Bucket=target_bucket_name, Policy=json.dumps(policy)
        )

    def set_object_public_read(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> None:
        target_bucket_name = self.get_target_bucket_name(bucket_name)

        # 获取当前对象的ACL
        current_acl_response = self.__cos_client.get_object_acl(
            Bucket=target_bucket_name, Key=object_name
        )

        # 检查是否已经有public-read权限
        # COS的ACL响应中，GrantList是一个列表，每个元素是一个字典，包含Grantee和Permission
        has_public_read = False
        if "GrantList" in current_acl_response:
            for grant in current_acl_response["GrantList"]:
                if (
                    "Grantee" in grant
                    and "Type" in grant["Grantee"]
                    and grant["Grantee"]["Type"] == "Group"
                    and "URI" in grant["Grantee"]
                    and grant["Grantee"]["URI"]
                    == "http://cam.qcloud.com/groups/global/AllUsers"
                    and "Permission" in grant
                    and grant["Permission"] == "READ"
                ):
                    has_public_read = True
                    break

        # 如果没有public-read权限，则添加
        if not has_public_read:
            self.__cos_client.put_object_acl(
                Bucket=target_bucket_name,
                Key=object_name,
                ACL="public-read",
            )

    def get_object_acl(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> Dict[str, Any]:
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        acl_info = self.__cos_client.get_object_acl(
            Bucket=target_bucket_name, Key=object_name
        )
        return dict(acl_info)

    def object_exists(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bool:
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        exists: bool = self.__cos_client.object_exists(
            Bucket=target_bucket_name, Key=object_name
        )
        return exists

    def delete_object(self, object_name: str, bucket_name: Optional[str] = None):
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        self.__cos_client.delete_object(Bucket=target_bucket_name, Key=object_name)

    def list_objects(
        self, bucket_name: Optional[str] = None, prefix: Optional[str] = None
    ) -> Generator[str, None, None]:
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        marker = ""
        while True:
            response = self.__cos_client.list_objects(
                Bucket=target_bucket_name,
                Prefix=prefix or "",
                Marker=marker,
            )
            if "Contents" in response:
                for item in response["Contents"]:
                    yield item["Key"]
            if response["IsTruncated"] == "false":
                break
            marker = response["NextMarker"]

    def delete_objects(
        self, object_names: List[str], bucket_name: Optional[str] = None
    ) -> Dict[Any, Any]:
        """
        删除多个对象
        
        Args:
            object_names: 要删除的对象名称列表
            bucket_name: 存储桶名称（可选）
            
        Returns:
            dict: 包含删除操作结果的字典
        """
        target_bucket_name = self.get_target_bucket_name(bucket_name)
        # 构造删除列表
        delete_list = [{"Key": key} for key in object_names]
        result = self.__cos_client.delete_objects(
            Bucket=target_bucket_name,
            Delete={"Object": delete_list, "Quiet": "true"},
        )
        # Ensure the return type matches the declared type
        if not isinstance(result, dict):
            raise TypeError("Expected dict from delete_objects, got {}".format(type(result)))
        return result
