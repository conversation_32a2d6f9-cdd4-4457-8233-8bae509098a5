"""
文件信息类
"""

from infra_database.entity.versioned.versioned_entity import VersionedEntity
from sqlalchemy import Column, String, Text

from infra_object_storage.entity.history.file_info_history import FileInfoHistoryEntity


class FileInfoEntity(VersionedEntity):
    """
    文件
    """

    __tablename__ = "st_file_info"
    __table_args__ = {"comment": "文件信息"}
    __history_entity__ = FileInfoHistoryEntity
    storage_info_id = Column(String(40), nullable=False, comment="存储id", index=True)
    original_name = Column(String(255), comment="文件原始名称", nullable=False)
    summary = Column(Text, comment="描述")
