"""
文件信息类
"""

from typing import Optional

from infra_database.entity.versioned.versioned_entity import VersionedEntity
from sqlalchemy import String, Text
from sqlalchemy.orm import Mapped, mapped_column

from infra_object_storage.entity.history.file_info_history import FileInfoHistoryEntity


class FileInfoEntity(VersionedEntity):
    """
    文件
    """

    __tablename__ = "st_file_info"
    __table_args__ = {"comment": "文件信息"}
    __history_entity__ = FileInfoHistoryEntity

    object_storage_raw_id: Mapped[str] = mapped_column(
        String(40), nullable=False, comment="存储id", index=True
    )
    original_name: Mapped[str] = mapped_column(
        String(255), comment="文件原始名称", nullable=False
    )
    summary: Mapped[Optional[str]] = mapped_column(Text, comment="描述")
