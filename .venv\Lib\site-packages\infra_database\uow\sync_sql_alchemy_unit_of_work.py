from types import TracebackType
from typing import Any, Optional, Type

# 日志库
from loguru import logger

# 导入必要的 SQLAlchemy 组件
from sqlalchemy.orm import Session, sessionmaker

# 从你的项目导入同步事务仓库，确保路径正确
from infra_database.error import TransactionLogSaveError
from infra_database.repository.sync.sync_transaction_repository import (
    SyncTransactionRepository,
)

# 从你的项目导入 BaseUnitOfWork
from infra_database.uow.base_unit_of_work_interface import BaseUnitOfWork

# 从你的项目导入上下文存储
from infra_database.uow.unit_of_work_storage import UnitOfWorkContainer, get_uow_storage


class SyncSqlAlchemyUnitOfWork(BaseUnitOfWork):
    """
    基于 SQLAlchemy 实现的同步工作单元。

    它继承了 `BaseUnitOfWork` 提供的事务日志功能，
    并负责管理同步 `Session` 的生命周期和事务（包括嵌套事务/保存点）。
    同时负责将事务日志持久化。
    """

    def __init__(self, session_factory: sessionmaker[Session], log_enabled: bool = True):
        """
        初始化同步工作单元。

        参数:
            session_factory: 一个 SQLAlchemy `sessionmaker` 实例，用于创建新的 Session。
            log_enabled: 是否开启此工作单元的详细日志。
        """
        super().__init__()
        self._session_factory = session_factory
        self._log_enabled = log_enabled
        self._transaction_repo: Optional[SyncTransactionRepository] = None
        self._storage = get_uow_storage()

    @property
    def session_stack_level(self) -> int:
        return len(self._storage.uow_stack)

    @property
    def session(self) -> Session:
        current_container = self._storage.current_container
        if not current_container or not isinstance(current_container.session, Session):
            raise RuntimeError("当前SyncSqlAlchemyUnitOfWork中没有活跃的同步 SQLAlchemy Session。")
        return current_container.session

    @property
    def transaction_repo(self) -> SyncTransactionRepository:
        """懒加载事务仓库，确保它总是使用当前UOW的session"""
        if self._transaction_repo is None:
            if self._log_enabled:
                logger.debug("懒加载 SyncTransactionRepository...")
            self._transaction_repo = SyncTransactionRepository(session=self.session)
        return self._transaction_repo

    def __enter__(self) -> 'SyncSqlAlchemyUnitOfWork':
        current_level = self.session_stack_level
        if self._log_enabled:
            logger.debug(f"SyncUOW [level={current_level}]: 进入上下文。")

        if current_level == 0:
            session = self._session_factory()
            transaction = session.begin()
            if self._log_enabled:
                logger.info(f"SyncUOW [level={current_level}]: 开启顶层事务. Session ID: {id(session)}")
        else:
            parent_container = self._storage.current_container
            if not parent_container:
                raise RuntimeError("SyncUOW: 无法为嵌套事务找到父级容器。")
            session = parent_container.session
            transaction = session.begin_nested()
            if self._log_enabled:
                logger.info(f"SyncUOW [level={current_level}]: 开启嵌套事务 (SAVEPOINT). Session ID: {id(session)}")

        self._storage.push(UnitOfWorkContainer(session, transaction))
        return self

    def __exit__(
        self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[TracebackType]
    ) -> None:
        level_to_complete_log = self.session_stack_level
        if self._log_enabled:
            logger.debug(f"SyncUOW [level={level_to_complete_log}]: 退出上下文。")

        repo = self.transaction_repo if level_to_complete_log == 1 else None
        container = self._storage.pop()
        if not container:
            raise RuntimeError("SyncUOW: 尝试退出，但工作单元堆栈为空。")

        session_to_close = container.session if level_to_complete_log == 1 else None

        original_exception: Optional[BaseException] = exc_val

        try:
            if original_exception:
                if self._log_enabled:
                    logger.error(
                        f"SyncUOW [level={level_to_complete_log}]: 上下文异常，回滚事务。错误: {original_exception}"
                    )
                container.transaction.rollback()
                self._complete_log_entry(level_to_complete_log, is_succeed=False, error_message=str(original_exception))
            else:
                if self._log_enabled:
                    logger.debug(f"SyncUOW [level={level_to_complete_log}]: 准备提交事务。")
                if session_to_close and self.all_transaction_logs:
                    if self._log_enabled:
                        logger.debug(
                            f"SyncUOW [level={level_to_complete_log}]: 持久化 {len(self.all_transaction_logs)} 条成功日志。"
                        )
                    if repo:
                        try:
                            repo.save_transaction_logs(self.all_transaction_logs)
                        except Exception as e:
                            # 如果保存日志失败，我们希望将这个异常包装成一个特定的类型
                            raise TransactionLogSaveError("Failed to save transaction logs") from e

                container.transaction.commit()
                if self._log_enabled:
                    logger.info(f"SyncUOW [level={level_to_complete_log}]: 事务已成功提交。")
                self._complete_log_entry(level_to_complete_log, is_succeed=True)

        except Exception as e:
            if self._log_enabled:
                logger.error(f"SyncUOW [level={level_to_complete_log}]: 处理事务时发生内部错误: {e}，将回滚。")
            container.transaction.rollback()
            self._complete_log_entry(level_to_complete_log, is_succeed=False, error_message=f"事务内部错误: {e}")
            original_exception = e  # 覆盖原始异常，因为这个更重要

        finally:
            if session_to_close:
                # 如果有异常且有日志，尝试在独立事务中持久化失败日志
                if original_exception and self.all_transaction_logs:
                    if self._log_enabled:
                        logger.warning(f"SyncUOW [level={level_to_complete_log}]: 尝试在独立事务中持久化失败日志。")
                    try:
                        with self._session_factory() as log_session:
                            log_repo = SyncTransactionRepository(session=log_session)
                            log_repo.save_transaction_logs(self.all_transaction_logs)
                            log_session.commit()
                            if self._log_enabled:
                                logger.info("失败日志已在独立事务中成功持久化。")
                    except Exception as log_e:
                        if self._log_enabled:
                            logger.error(f"持久化失败日志时发生严重错误，日志可能已丢失: {log_e}")

                if self._log_enabled:
                    logger.info(f"SyncUOW [level={level_to_complete_log}]: 关闭 Session ID: {id(session_to_close)}")
                session_to_close.close()
                self._storage.clear()
                self._cleanup_logs()

        if original_exception:
            raise original_exception

        return None

    async def __aenter__(self) -> 'SyncSqlAlchemyUnitOfWork':
        raise NotImplementedError(
            "SyncSqlAlchemyUnitOfWork 不支持异步上下文管理。请使用 AsyncSqlAlchemyUnitOfWork 替代。"
        )

    async def __aexit__(
        self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[TracebackType]
    ) -> Optional[bool]:
        raise NotImplementedError(
            "SyncSqlAlchemyUnitOfWork 不支持异步上下文管理。请使用 AsyncSqlAlchemyUnitOfWork 替代。"
        )

    # ------------------ 额外的便捷方法 (示例) ------------------
    def add(self, instance: Any) -> None:
        self.session.add(instance)

    def delete(self, instance: Any) -> None:
        self.session.delete(instance)
