import pytest


@pytest.mark.parametrize("config_path", ["oss.toml"], indirect=True)
def test_oss_bucket(object_storage_client, config_path):
    check_bucket_name = "yundinetwork-dev"
    assert object_storage_client.bucket_exists(check_bucket_name) is True

    test_bucket_name = "minio-py-1305036724"
    object_storage_client.make_bucket(test_bucket_name)
    # object_storage_client.remove_bucket(test_bucket_name)
    # assert object_storage_client.bucket_exists(test_bucket_name) is False


@pytest.mark.parametrize("config_path", ["oss.toml"], indirect=True)
def test_oss_upload(object_storage_client, config_path):
    with open("tests/helper/oss.png", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(
            object_name="oss_upload.png", object_data=obj_data
        )


@pytest.mark.parametrize("config_path", ["oss.toml"], indirect=True)
def test_oss_download(object_storage_client, config_path):
    obj_blob = object_storage_client.download_file(object_name="oss_upload.png")
    with open("tests/helper/oss_download.png", "wb") as f:
        f.write(obj_blob)


@pytest.mark.parametrize("config_path", ["oss.toml"], indirect=True)
def test_oss_url(object_storage_client, config_path):
    # test public url
    url = object_storage_client.get_file_url("s3-public.jpg")
    print(url)
    assert url is not None
    url = object_storage_client.get_file_url("oss_upload.png")
    print(url)
    assert url is not None


@pytest.mark.parametrize("config_path", ["oss.toml"], indirect=True)
def test_oss_public_upload(object_storage_client, config_path):
    with open("tests/helper/s3-public.jpg", "rb") as f:
        obj_data = f.read()
        object_storage_client.upload_file(
            object_name="s3-public.jpg", object_data=obj_data,
            is_public=True
        )


@pytest.mark.parametrize("config_path", ["oss.toml"], indirect=True)
def test_oss_object_acl(object_storage_client, config_path):
    acl = object_storage_client.get_object_acl("s3-public.jpg")
    print(acl)
    acl2 = object_storage_client.get_object_acl("oss_upload.png")
    print(acl2)
