"""
minio帮助类
"""

import json
from datetime import timed<PERSON><PERSON>
from io import By<PERSON><PERSON>
from typing import Optional

from minio import Minio

from infra_object_storage.error import BucketNotExistedError, ObjectNotPublicReadError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
    ObjectStorageTypeEnum,
)
from infra_object_storage.helper.object_storage_interface import (
    AccountInfo,
    ObjectStorageInterface,
)
from infra_object_storage.settings import MinioSetting


class MinioHelper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """minio帮助类"""

    def get_account_info(self) -> AccountInfo:
        """返回账号信息"""
        return AccountInfo(
            type=ObjectStorageTypeEnum.MINIO,
            site=self.__settings.host,
            unified_id=self.__settings.access_key,
        )

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获取目标篮子"""
        return self._prepare_bucket_name(bucket_name=bucket_name)

    def __init__(self, minio_settings: Optional[MinioSetting] = None):
        """初始化"""
        if minio_settings is None:
            minio_settings = MinioSetting()
        self.__settings = minio_settings
        super().__init__(default_bucket=self.__settings.default_bucket)
        self.__minio_client = Minio(
            endpoint=f"{self.__settings.host}:{self.__settings.port}",
            access_key=self.__settings.access_key,
            secret_key=self.__settings.secret_key,
            secure=self.__settings.secure,
        )
        self.make_bucket(self.__settings.default_bucket)

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """
        判断篮子是否存在
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__minio_client.bucket_exists(bucket_name=target_bucket_name)

    def remove_bucket(self, bucket_name: Optional[str]):
        """
        删除篮子
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if not self.__minio_client.bucket_exists(target_bucket_name):
            raise BucketNotExistedError()
        self.__minio_client.remove_bucket(target_bucket_name)


    def make_bucket(self, bucket_name: Optional[str]):
        """
        不存在就创建篮子
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if not self.__minio_client.bucket_exists(target_bucket_name):
            self.__minio_client.make_bucket(target_bucket_name)
            policy = {"Version": "2012-10-17", "Statement": []}
            self.__minio_client.set_bucket_policy(
                target_bucket_name, json.dumps(policy)
            )

    def download_file(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """
        下载文件
        :param object_name: 需下载的对象
        :param bucket_name: 篮子名，传入None的话则取默认篮子
        :return: 对象
        """
        minio_data = self.__minio_client.get_object(
            bucket_name=self._prepare_bucket_name(bucket_name),
            object_name=object_name,
        )
        return minio_data.data


    def upload_file(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """
        上传文件
        :param object_name: 对象名
        :param object_data: 对象的数据
        :param bucket_name: 传入的篮子名，None的话则放入默认篮子
        :param is_public: 是否公开
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__minio_client.put_object(
            bucket_name=target_bucket_name,
            object_name=object_name,
            data=BytesIO(object_data),
            length=len(object_data),
        )
        if is_public:
            self.set_object_public_read(
                object_name=object_name, bucket_name=target_bucket_name
            )

    def get_file_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """
        生成一个文件URL
        :param object_name: 对象名称
        :param bucket_name: 篮子名字不传入则从默认篮子里面捞
        :param expires_seconds: 签名URL的有效期（秒），默认1天。
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__minio_client.presigned_get_object(
            bucket_name=target_bucket_name,
            object_name=object_name,
            expires=timedelta(seconds=expires_seconds),
        )

    def is_object_public_read(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bool:
        """
        判断对象是否公开读取
        :param object_name:
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        # bucket_name = self._prepare_bucket_name(bucket_name=bucket_name)
        # 获取bucket的策略
        policy = self.__minio_client.get_bucket_policy(target_bucket_name)
        policy_dict = json.loads(policy)

        # 遍历策略中的所有声明
        for statement in policy_dict.get("Statement", []):
            # 检查声明是否允许公共读取
            if (
                statement.get("Effect") == "Allow"
                and statement.get("Principal") == {"AWS": ["*"]}
                and "s3:GetObject" in statement.get("Action", [])
                and f"arn:aws:s3:::{target_bucket_name}/{object_name}"
                in statement.get("Resource", [])
            ):
                return True

        return False


    def get_object_policy(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        raise NotImplementedError

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> dict:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return json.loads(self.__minio_client.get_bucket_policy(target_bucket_name))

    def set_object_public_read(self, object_name: str, bucket_name: Optional[str] = None):
        return super().set_object_public_read(object_name, bucket_name)

    def get_object_acl(self, object_name: str, bucket_name: Optional[str] = None) -> dict:
        raise NotImplementedError
