"""
minio帮助类
"""

import json
from datetime import timed<PERSON>ta
from io import Bytes<PERSON>
from typing import Any, Dict, Iterable, List, Optional

from loguru import logger
from minio import Minio
from minio.deleteobjects import DeleteObject
from minio.error import S3Error

from infra_object_storage.error import BucketNotExistedError
from infra_object_storage.helper.object_storage_base_helper import (
    ObjectStorageBaseHelper,
)
from infra_object_storage.helper.object_storage_interface import ObjectStorageInterface
from infra_object_storage.settings import MinioAccount


class MinioHelper(ObjectStorageBaseHelper, ObjectStorageInterface):
    """minio帮助类"""

    def get_account_info(self) -> MinioAccount:
        """返回账号信息"""
        return self.__account

    def get_target_bucket_name(self, bucket_name: Optional[str]) -> str:
        """获取目标存储桶"""
        return self._prepare_bucket_name(bucket_name=bucket_name)

    def __init__(self, minio_account: MinioAccount):
        """初始化"""
        self.__account = minio_account
        super().__init__(default_bucket=self.__account.default_bucket)
        self.__minio_client = Minio(
            endpoint=f"{self.__account.host}:{self.__account.port}",
            access_key=self.__account.access_key,
            secret_key=self.__account.secret_key,
            secure=self.__account.secure,
        )
        self.make_bucket(self.__account.default_bucket)

    def bucket_exists(self, bucket_name: Optional[str]) -> bool:
        """
        判断存储桶是否存在
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__minio_client.bucket_exists(bucket_name=target_bucket_name)

    def remove_bucket(self, bucket_name: Optional[str]):
        """
        删除存储桶
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if not self.__minio_client.bucket_exists(target_bucket_name):
            raise BucketNotExistedError()
        self.__minio_client.remove_bucket(target_bucket_name)

    def make_bucket(self, bucket_name: Optional[str]):
        """
        如果存储桶不存在，则创建它。
        :param bucket_name:
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        if not self.__minio_client.bucket_exists(target_bucket_name):
            self.__minio_client.make_bucket(target_bucket_name)
            policy = {"Version": "2012-10-17", "Statement": []}
            self.__minio_client.set_bucket_policy(
                target_bucket_name, json.dumps(policy)
            )

    def download_object(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bytes:
        """
        下载文件
        :param object_name: 需下载的对象
        :param bucket_name: 存储桶名，传入None的话则取默认存储桶
        :return: 对象
        """
        minio_data = self.__minio_client.get_object(
            bucket_name=self._prepare_bucket_name(bucket_name),
            object_name=object_name,
        )
        return minio_data.data

    def upload_object(
        self,
        object_name: str,
        object_data: bytes,
        bucket_name: Optional[str] = None,
        is_public: bool = False,
    ):
        """
        上传文件
        :param object_name: 对象名
        :param object_data: 对象的数据
        :param bucket_name: 传入的存储桶名，None的话则放入默认存储桶
        :param is_public: 是否公开
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__minio_client.put_object(
            bucket_name=target_bucket_name,
            object_name=object_name,
            data=BytesIO(object_data),
            length=len(object_data),
        )
        if is_public:
            self.set_object_public_read(
                object_name=object_name, bucket_name=target_bucket_name
            )

    def get_object_url(
        self,
        object_name: str,
        bucket_name: Optional[str] = None,
        expires_seconds: int = 60 * 60 * 24,
    ) -> str:
        """
        生成一个文件URL
        :param object_name: 对象名称
        :param bucket_name: 存储桶名字不传入则从默认存储桶里面捞
        :param expires_seconds: 签名URL的有效期（秒），默认1天。
        :return:
        """
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        return self.__minio_client.presigned_get_object(
            bucket_name=target_bucket_name,
            object_name=object_name,
            expires=timedelta(seconds=expires_seconds),
        )

    def get_bucket_policy(self, bucket_name: Optional[str] = None) -> Dict[str, Any]:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        policy_dict = json.loads(self.__minio_client.get_bucket_policy(target_bucket_name))
        return policy_dict

    def set_bucket_policy(self, policy: dict, bucket_name: Optional[str] = None):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__minio_client.set_bucket_policy(target_bucket_name, json.dumps(policy))

    def set_object_public_read(
        self, object_name: str, bucket_name: Optional[str] = None
    ):
        target_bucket_name = self._prepare_bucket_name(bucket_name)

        # 获取当前桶策略
        current_policy = self.get_bucket_policy(target_bucket_name)

        # 检查是否已存在针对该对象的公共读权限
        has_public_read = False
        if "Statement" in current_policy:
            for statement in current_policy["Statement"]:
                if (
                    "Effect" in statement
                    and statement["Effect"] == "Allow"
                    and "Principal" in statement
                    and statement["Principal"] == {"AWS": ["*"]}
                    and "Action" in statement
                    and "s3:GetObject" in statement["Action"]
                    and "Resource" in statement
                    and f"arn:aws:s3:::{target_bucket_name}/{object_name}"
                    in statement["Resource"]
                ):
                    has_public_read = True
                    break

        # 如果没有公共读权限，则添加
        if not has_public_read:
            new_statement = {
                "Effect": "Allow",
                "Principal": {"AWS": ["*"]},
                "Action": ["s3:GetObject"],
                "Resource": [f"arn:aws:s3:::{target_bucket_name}/{object_name}"],
            }
            if "Statement" not in current_policy:
                current_policy["Statement"] = []
            current_policy["Statement"].append(new_statement)
            self.__minio_client.set_bucket_policy(
                target_bucket_name, json.dumps(current_policy)
            )

    def get_object_acl(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> dict:
        raise NotImplementedError(
            "Minio acls are limited, please use bucket policy instead."
        )

    def object_exists(
        self, object_name: str, bucket_name: Optional[str] = None
    ) -> bool:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        try:
            self.__minio_client.stat_object(target_bucket_name, object_name)
            return True
        except S3Error as e:
            if e.code == "NoSuchKey":
                return False
            raise

    def delete_object(self, object_name: str, bucket_name: Optional[str] = None):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        self.__minio_client.remove_object(target_bucket_name, object_name)

    def list_objects(
        self, bucket_name: Optional[str] = None, prefix: Optional[str] = None
    ) -> Iterable[str]:
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        objects = self.__minio_client.list_objects(
            target_bucket_name, prefix=prefix, recursive=True
        )
        for obj in objects:
            if obj.object_name:
                yield obj.object_name

    def delete_objects(
        self, object_names: List[str], bucket_name: Optional[str] = None
    ):
        target_bucket_name = self._prepare_bucket_name(bucket_name)
        delete_object_list = [DeleteObject(name) for name in object_names]
        errors = self.__minio_client.remove_objects(
            target_bucket_name, delete_object_list
        )
        # Note: This operation is atomic. Errors are yielded for objects that couldn't be deleted.
        # For simplicity, we are not handling these errors here.
        for error in errors:
            # In a real application, you should log or handle these errors.
            logger.error("Error in deleting object", error)
