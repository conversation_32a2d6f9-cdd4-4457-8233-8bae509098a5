"""
数据库设置
"""

from typing import Any, Dict

from pydantic import BaseModel, Field


class DatabaseSettings(BaseModel):
    """
    通用数据库设置
    """

    driver: str = Field(default="postgresql+psycopg2")
    host: str = Field(default="127.0.0.1")
    port: int = Field(default=5432)
    user: str = Field(default="postgres")
    password: str = Field(default="postgres")
    database: str = Field(default="postgres")
    echo: bool = Field(default=False)
    uow_log_enabled: bool = Field(default=False, description="是否开启UOW的详细日志")

    # 连接池配置
    pool_size: int = Field(default=5, description="连接池中保持的连接数")
    max_overflow: int = Field(default=10, description="连接池允许溢出的连接数")
    pool_timeout: int = Field(default=30, description="等待连接的超时时间(秒)")
    pool_recycle: int = Field(default=1800, description="连接回收时间(秒)")
    pool_pre_ping: bool = Field(default=True, description="是否在使用前检查连接有效性")

    @property
    def url(self) -> str:
        """
        获取数据库连接 URL

        Returns:
            str: 数据库连接 URL
        """
        return f"{self.driver}://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

    def get_engine_args(self) -> Dict[str, Any]:
        """
        获取 SQLAlchemy 引擎参数

        Returns:
            Dict[str, Any]: SQLAlchemy 引擎参数字典
        """
        return {
            "echo": self.echo,
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_timeout": self.pool_timeout,
            "pool_recycle": self.pool_recycle,
            "pool_pre_ping": self.pool_pre_ping,
        }


# 数据库类型常量
class DatabaseType:
    """数据库类型常量"""

    POSTGRESQL = "postgresql"


# 驱动类型常量
class DriverType:
    """驱动类型常量"""

    PSYCOPG2 = "+psycopg2"
    ASYNCPG = "+asyncpg"
