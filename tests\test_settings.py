from typing import List

import pytest

from infra_object_storage.settings import (
    CosAccount,
    MinioAccount,
    OssAccount,
    S3Account,
    StorageAccount,
)


def test_load_storage_settings(mock_app_settings):
    """
    测试从 toml 文件加载存储设置
    """
    storage_settings = mock_app_settings.storage
    assert storage_settings is not None
    accounts: List[StorageAccount] = storage_settings.accounts
    assert len(accounts) == 4

    # 验证 S3 账户
    s3_account = next((acc for acc in accounts if acc.id == "s3_test"), None)
    assert s3_account is not None
    assert isinstance(s3_account, S3Account)
    assert s3_account.provider == "s3"
    assert s3_account.default_bucket == "qindingtech-bucket-dev"
    assert s3_account.aws_access_key_id == "********************"
    assert s3_account.aws_secret_access_key == "mXE3SKxsAO09Y/Nwf/WshUIr//re5F0B7G3EtSqU"
    assert s3_account.region_name == "ap-northeast-1"
    assert s3_account.endpoint_url == "https://s3.ap-northeast-1.amazonaws.com"

    # 验证 COS 账户
    cos_account = next((acc for acc in accounts if acc.id == "cos_test"), None)
    assert cos_account is not None
    assert isinstance(cos_account, CosAccount)
    assert cos_account.provider == "cos"
    assert cos_account.default_bucket == "dev-cos-**********"
    assert cos_account.secret_id == "AKIDCOXVFoBew42qUu7FCyb2KPGusfGgu2yf"
    assert cos_account.secret_key == "av4zkIhspLf8VineJk196uZ2HNNDuRo3"
    assert cos_account.region == "ap-nanjing"
    assert cos_account.endpoint_url == "https://cos.ap-nanjing.myqcloud.com"

    # 验证 Minio 账户
    minio_account = next((acc for acc in accounts if acc.id == "minio_test"), None)
    assert minio_account is not None
    assert isinstance(minio_account, MinioAccount)
    assert minio_account.provider == "minio"
    assert minio_account.default_bucket == "minio-test"
    assert minio_account.host == "dev-minio.qindingtech.com"
    assert minio_account.port == 443
    assert minio_account.access_key == "admin"
    assert minio_account.secret_key == "Pa55word"
    assert minio_account.secure is True

    # 验证 OSS 账户
    oss_account = next((acc for acc in accounts if acc.id == "oss_test"), None)
    assert oss_account is not None
    assert isinstance(oss_account, OssAccount)
    assert oss_account.provider == "oss"
    assert oss_account.default_bucket == "minio-py-**********"
    assert oss_account.access_key_id == "LTAI4GFp1gCtrZETfLBbdXUw"
    assert oss_account.access_key_secret == "7jXI0TGtN8YekJ1h1mM8fmPPaRtcKE"
    assert oss_account.endpoint == "oss-cn-shanghai.aliyuncs.com"